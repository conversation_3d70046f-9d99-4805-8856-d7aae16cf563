
package com.unipay.pay.service;

import com.unipay.core.constants.CS;
import com.unipay.core.entity.MchApp;
import com.unipay.core.exception.BizException;
import com.unipay.core.entity.MchInfo;
import com.unipay.core.entity.PayInterfaceConfig;
import com.unipay.core.entity.UnifiedPayApp;
import com.unipay.core.model.params.IsvParams;
import com.unipay.core.model.params.IsvsubMchParams;
import com.unipay.core.model.params.NormalMchParams;
import com.unipay.core.model.params.alipay.AlipayIsvParams;
import com.unipay.core.model.params.alipay.AlipayNormalMchParams;
import com.unipay.core.model.params.pppay.PppayNormalMchParams;
import com.unipay.core.model.params.wxpay.WxpayIsvParams;
import com.unipay.core.model.params.wxpay.WxpayNormalMchParams;
import com.unipay.pay.model.*;
import com.unipay.pay.model.*;
import com.unipay.service.impl.MchAppService;
import com.unipay.service.impl.MchInfoService;
import com.unipay.service.impl.PayInterfaceConfigService;
import com.unipay.service.impl.SysConfigService;
import com.unipay.service.impl.UnifiedPayAppService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/*
* 配置信息查询服务 （兼容 缓存 和 直接查询方式） 
* @date 2021/11/18 14:41
*/
@Slf4j
@Service
public class ConfigContextQueryService {

    @Autowired ConfigContextService configContextService;
    @Autowired private MchInfoService mchInfoService;
    @Autowired private MchAppService mchAppService;
    @Autowired private PayInterfaceConfigService payInterfaceConfigService;
    @Autowired private UnifiedPayAppService unifiedPayAppService;

    private boolean isCache(){
        return SysConfigService.IS_USE_CACHE;
    }

    public MchApp queryMchApp(String mchNo, String mchAppId){

        if(isCache()){
            return configContextService.getMchAppConfigContext(mchNo, mchAppId).getMchApp();
        }

        // 首先查询商户信息，获取绑定的统一应用ID
        MchInfo mchInfo = mchInfoService.getById(mchNo);
        if (mchInfo != null && mchInfo.getUnifiedAppId() != null) {
            // 使用统一应用，创建一个虚拟的MchApp对象
            UnifiedPayApp unifiedApp = unifiedPayAppService.getById(mchInfo.getUnifiedAppId());
            if (unifiedApp != null) {
                MchApp virtualMchApp = new MchApp();
                virtualMchApp.setAppId(unifiedApp.getAppId());
                virtualMchApp.setAppName(unifiedApp.getAppName());
                virtualMchApp.setMchNo(mchNo);
                virtualMchApp.setAppSecret(unifiedApp.getAppSecret());
                virtualMchApp.setState(unifiedApp.getState());
                virtualMchApp.setRemark("统一应用: " + unifiedApp.getAppName());
                return virtualMchApp;
            }
        }

        // 兼容旧的商户应用模式
        return mchAppService.getOneByMch(mchNo, mchAppId);
    }

    public MchAppConfigContext queryMchInfoAndAppInfo(String mchAppId) {
        // 首先尝试从统一应用查找
        UnifiedPayApp unifiedApp = unifiedPayAppService.getById(mchAppId);
        if (unifiedApp != null) {
            // 这是统一应用，需要找到使用该应用的商户
            // 这里需要传入商户号，但由于是统一应用，我们需要从调用上下文获取
            throw new BizException("使用统一应用时必须提供商户号");
        }

        // 兼容旧的商户应用模式
        MchApp mchApp = mchAppService.getById(mchAppId);
        if (mchApp != null) {
            return queryMchInfoAndAppInfo(mchApp.getMchNo(), mchAppId);
        }

        return null;
    }

    public MchAppConfigContext queryMchInfoAndAppInfo(String mchNo, String mchAppId){

        if(isCache()){
            return configContextService.getMchAppConfigContext(mchNo, mchAppId);
        }

        MchInfo mchInfo = mchInfoService.getById(mchNo);
        if(mchInfo == null){
            return null;
        }

        MchApp mchApp;
        // 如果商户绑定了统一应用，使用统一应用
        if (mchInfo.getUnifiedAppId() != null) {
            UnifiedPayApp unifiedApp = unifiedPayAppService.getById(mchInfo.getUnifiedAppId());
            if (unifiedApp != null) {
                // 创建虚拟的MchApp对象
                mchApp = new MchApp();
                mchApp.setAppId(unifiedApp.getAppId());
                mchApp.setAppName(unifiedApp.getAppName());
                mchApp.setMchNo(mchNo);
                mchApp.setAppSecret(unifiedApp.getAppSecret());
                mchApp.setState(unifiedApp.getState());
                mchApp.setRemark("统一应用: " + unifiedApp.getAppName());
            } else {
                return null;
            }
        } else {
            // 兼容旧的商户应用模式
            mchApp = mchAppService.getOneByMch(mchNo, mchAppId);
            if(mchApp == null){
                return null;
            }
        }

        MchAppConfigContext result = new MchAppConfigContext();
        result.setMchInfo(mchInfo);
        result.setMchNo(mchNo);
        result.setMchType(mchInfo.getType());

        result.setMchApp(mchApp);
        result.setAppId(mchApp.getAppId()); // 使用实际的应用ID

        return result;
    }


    public NormalMchParams queryNormalMchParams(String mchNo, String mchAppId, String ifCode){

        if(isCache()){
            return configContextService.getMchAppConfigContext(mchNo, mchAppId).getNormalMchParamsByIfCode(ifCode);
        }

        // 查询商户的所有支持的参数配置
        PayInterfaceConfig payInterfaceConfig = payInterfaceConfigService.getOne(PayInterfaceConfig.gw()
                .select(PayInterfaceConfig::getIfCode, PayInterfaceConfig::getIfParams)
                .eq(PayInterfaceConfig::getState, CS.YES)
                .eq(PayInterfaceConfig::getInfoType, CS.INFO_TYPE_MCH_APP)
                .eq(PayInterfaceConfig::getInfoId, mchAppId)
                .eq(PayInterfaceConfig::getIfCode, ifCode)
        );

        if(payInterfaceConfig == null){
            return null;
        }

        return NormalMchParams.factory(payInterfaceConfig.getIfCode(), payInterfaceConfig.getIfParams());
    }


    public IsvsubMchParams queryIsvsubMchParams(String mchNo, String mchAppId, String ifCode){

        if(isCache()){
            return configContextService.getMchAppConfigContext(mchNo, mchAppId).getIsvsubMchParamsByIfCode(ifCode);
        }

        // 查询商户的所有支持的参数配置
        PayInterfaceConfig payInterfaceConfig = payInterfaceConfigService.getOne(PayInterfaceConfig.gw()
                .select(PayInterfaceConfig::getIfCode, PayInterfaceConfig::getIfParams)
                .eq(PayInterfaceConfig::getState, CS.YES)
                .eq(PayInterfaceConfig::getInfoType, CS.INFO_TYPE_MCH_APP)
                .eq(PayInterfaceConfig::getInfoId, mchAppId)
                .eq(PayInterfaceConfig::getIfCode, ifCode)
        );

        if(payInterfaceConfig == null){
            return null;
        }

        return IsvsubMchParams.factory(payInterfaceConfig.getIfCode(), payInterfaceConfig.getIfParams());
    }



    public IsvParams queryIsvParams(String isvNo, String ifCode){

        if(isCache()){
            IsvConfigContext isvConfigContext = configContextService.getIsvConfigContext(isvNo);
            return isvConfigContext == null ? null : isvConfigContext.getIsvParamsByIfCode(ifCode);
        }

        // 查询商户的所有支持的参数配置
        PayInterfaceConfig payInterfaceConfig = payInterfaceConfigService.getOne(PayInterfaceConfig.gw()
                .select(PayInterfaceConfig::getIfCode, PayInterfaceConfig::getIfParams)
                .eq(PayInterfaceConfig::getState, CS.YES)
                .eq(PayInterfaceConfig::getInfoType, CS.INFO_TYPE_ISV)
                .eq(PayInterfaceConfig::getInfoId, isvNo)
                .eq(PayInterfaceConfig::getIfCode, ifCode)
        );

        if(payInterfaceConfig == null){
            return null;
        }

        return IsvParams.factory(payInterfaceConfig.getIfCode(), payInterfaceConfig.getIfParams());

    }

    public AlipayClientWrapper getAlipayClientWrapper(MchAppConfigContext mchAppConfigContext){

        if(isCache()){
            return
                    configContextService.getMchAppConfigContext(mchAppConfigContext.getMchNo(), mchAppConfigContext.getAppId()).getAlipayClientWrapper();
        }

        if(mchAppConfigContext.isIsvsubMch()){

            AlipayIsvParams alipayParams = (AlipayIsvParams)queryIsvParams(mchAppConfigContext.getMchInfo().getIsvNo(), CS.IF_CODE.ALIPAY);
            return AlipayClientWrapper.buildAlipayClientWrapper(alipayParams);
        }else{

            AlipayNormalMchParams alipayParams = (AlipayNormalMchParams)queryNormalMchParams(mchAppConfigContext.getMchNo(), mchAppConfigContext.getAppId(), CS.IF_CODE.ALIPAY);
            return AlipayClientWrapper.buildAlipayClientWrapper(alipayParams);
        }

    }

    public WxServiceWrapper getWxServiceWrapper(MchAppConfigContext mchAppConfigContext){

        if(isCache()){
            return
                    configContextService.getMchAppConfigContext(mchAppConfigContext.getMchNo(), mchAppConfigContext.getAppId()).getWxServiceWrapper();
        }

        if(mchAppConfigContext.isIsvsubMch()){

            WxpayIsvParams wxParams = (WxpayIsvParams)queryIsvParams(mchAppConfigContext.getMchInfo().getIsvNo(), CS.IF_CODE.WXPAY);
            return WxServiceWrapper.buildWxServiceWrapper(wxParams);
        }else{

            WxpayNormalMchParams wxParams = (WxpayNormalMchParams)queryNormalMchParams(mchAppConfigContext.getMchNo(), mchAppConfigContext.getAppId(), CS.IF_CODE.WXPAY);
            return WxServiceWrapper.buildWxServiceWrapper(wxParams);
        }

    }

    public PaypalWrapper getPaypalWrapper(MchAppConfigContext mchAppConfigContext){
        if(isCache()){
            return
                    configContextService.getMchAppConfigContext(mchAppConfigContext.getMchNo(), mchAppConfigContext.getAppId()).getPaypalWrapper();
        }
        PppayNormalMchParams ppPayNormalMchParams = (PppayNormalMchParams) queryNormalMchParams(mchAppConfigContext.getMchNo(), mchAppConfigContext.getAppId(), CS.IF_CODE.PPPAY);;
        return PaypalWrapper.buildPaypalWrapper(ppPayNormalMchParams);

    }

}
