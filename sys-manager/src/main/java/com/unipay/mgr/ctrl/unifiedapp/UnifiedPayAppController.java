package com.unipay.mgr.ctrl.unifiedapp;

import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.RandomUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.unipay.core.aop.MethodLog;
import com.unipay.core.constants.ApiCodeEnum;
import com.unipay.core.constants.CS;
import com.unipay.core.entity.SysUser;
import com.unipay.core.entity.UnifiedPayApp;
import com.unipay.core.model.ApiRes;
import com.unipay.mgr.ctrl.CommonCtrl;
import com.unipay.service.impl.UnifiedPayAppService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

/**
 * 统一支付应用管理
 *
 * <AUTHOR>
 * @since 2025-09-25
 */
@Tag(name = "统一支付应用")
@RestController
@RequestMapping("/api/unifiedPayApps")
public class UnifiedPayAppController extends CommonCtrl {

    @Autowired
    private UnifiedPayAppService unifiedPayAppService;

    /**
     * 统一支付应用列表
     */
    @Operation(summary = "统一支付应用列表", description = "")
    @Parameters({
            @Parameter(name = "iToken", description = "用户身份凭证", required = true, in = ParameterIn.HEADER),
            @Parameter(name = "pageNumber", description = "分页页码"),
            @Parameter(name = "pageSize", description = "分页条数"),
            @Parameter(name = "appId", description = "应用ID"),
            @Parameter(name = "appName", description = "应用名称"),
            @Parameter(name = "state", description = "状态: 0-停用, 1-启用")
    })
    @PreAuthorize("hasAuthority('ENT_UNIFIED_PAY_APP_LIST')")
    @GetMapping
    public ApiRes<IPage<UnifiedPayApp>> list() {
        UnifiedPayApp queryObject = getObject(UnifiedPayApp.class);
        IPage<UnifiedPayApp> pages = unifiedPayAppService.selectPage(getIPage(), queryObject);
        return ApiRes.ok(pages);
    }

    /**
     * 新建统一支付应用
     */
    @Operation(summary = "新建统一支付应用", description = "")
    @Parameters({
            @Parameter(name = "iToken", description = "用户身份凭证", required = true, in = ParameterIn.HEADER),
            @Parameter(name = "appName", description = "应用名称", required = true),
            @Parameter(name = "remark", description = "备注")
    })
    @PreAuthorize("hasAuthority('ENT_UNIFIED_PAY_APP_ADD')")
    @MethodLog(remark = "新建统一支付应用")
    @PostMapping
    public ApiRes add() {
        UnifiedPayApp unifiedPayApp = getObject(UnifiedPayApp.class);
        
        // 生成应用ID和密钥
        unifiedPayApp.setAppId(IdUtil.objectId());
        unifiedPayApp.setAppSecret(RandomUtil.randomString(128));
        
        // 设置创建信息
        SysUser currentUser = getCurrentUser().getSysUser();
        unifiedPayApp.setCreatedUid(currentUser.getSysUserId());
        unifiedPayApp.setCreatedBy(currentUser.getRealname());
        
        // 默认状态为启用
        if (unifiedPayApp.getState() == null) {
            unifiedPayApp.setState(CS.YES);
        }
        
        // 默认不是默认应用
        if (unifiedPayApp.getIsDefault() == null) {
            unifiedPayApp.setIsDefault(CS.NO);
        }

        boolean result = unifiedPayAppService.save(unifiedPayApp);
        if (!result) {
            return ApiRes.fail(ApiCodeEnum.SYS_OPERATION_FAIL_CREATE);
        }
        return ApiRes.ok();
    }

    /**
     * 统一支付应用详情
     */
    @Operation(summary = "统一支付应用详情", description = "")
    @Parameters({
            @Parameter(name = "iToken", description = "用户身份凭证", required = true, in = ParameterIn.HEADER),
            @Parameter(name = "appId", description = "应用ID", required = true)
    })
    @PreAuthorize("hasAuthority('ENT_UNIFIED_PAY_APP_VIEW')")
    @GetMapping("/{appId}")
    public ApiRes<UnifiedPayApp> detail(@PathVariable("appId") String appId) {
        UnifiedPayApp unifiedPayApp = unifiedPayAppService.selectById(appId);
        if (unifiedPayApp == null) {
            return ApiRes.fail(ApiCodeEnum.SYS_OPERATION_FAIL_SELETE);
        }
        return ApiRes.ok(unifiedPayApp);
    }

    /**
     * 更新统一支付应用
     */
    @Operation(summary = "更新统一支付应用", description = "")
    @Parameters({
            @Parameter(name = "iToken", description = "用户身份凭证", required = true, in = ParameterIn.HEADER),
            @Parameter(name = "appId", description = "应用ID", required = true),
            @Parameter(name = "appName", description = "应用名称"),
            @Parameter(name = "state", description = "状态: 0-停用, 1-启用"),
            @Parameter(name = "remark", description = "备注")
    })
    @PreAuthorize("hasAuthority('ENT_UNIFIED_PAY_APP_EDIT')")
    @MethodLog(remark = "更新统一支付应用")
    @PutMapping("/{appId}")
    public ApiRes update(@PathVariable("appId") String appId) {
        UnifiedPayApp unifiedPayApp = getObject(UnifiedPayApp.class);
        unifiedPayApp.setAppId(appId);
        
        boolean result = unifiedPayAppService.updateById(unifiedPayApp);
        if (!result) {
            return ApiRes.fail(ApiCodeEnum.SYS_OPERATION_FAIL_UPDATE);
        }
        return ApiRes.ok();
    }

    /**
     * 删除统一支付应用
     */
    @Operation(summary = "删除统一支付应用", description = "")
    @Parameters({
            @Parameter(name = "iToken", description = "用户身份凭证", required = true, in = ParameterIn.HEADER),
            @Parameter(name = "appId", description = "应用ID", required = true)
    })
    @PreAuthorize("hasAuthority('ENT_UNIFIED_PAY_APP_DEL')")
    @MethodLog(remark = "删除统一支付应用")
    @DeleteMapping("/{appId}")
    public ApiRes delete(@PathVariable("appId") String appId) {
        unifiedPayAppService.removeByAppId(appId);
        return ApiRes.ok();
    }

    /**
     * 设置默认应用
     */
    @Operation(summary = "设置默认应用", description = "")
    @Parameters({
            @Parameter(name = "iToken", description = "用户身份凭证", required = true, in = ParameterIn.HEADER),
            @Parameter(name = "appId", description = "应用ID", required = true)
    })
    @PreAuthorize("hasAuthority('ENT_UNIFIED_PAY_APP_EDIT')")
    @MethodLog(remark = "设置默认应用")
    @PutMapping("/{appId}/setDefault")
    public ApiRes setDefault(@PathVariable("appId") String appId) {
        unifiedPayAppService.setDefaultApp(appId);
        return ApiRes.ok();
    }
}
