package com.unipay.mgr.ctrl.merchant;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.unipay.core.entity.MchIncomeRecord;
import com.unipay.core.model.ApiRes;
import com.unipay.mgr.ctrl.CommonCtrl;
import com.unipay.service.impl.MchIncomeRecordService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.Map;

/**
 * 商户收益管理
 *
 * <AUTHOR>
 * @since 2025-09-25
 */
@Tag(name = "商户收益管理")
@RestController
@RequestMapping("/api/mchIncomes")
public class MchIncomeController extends CommonCtrl {

    @Autowired
    private MchIncomeRecordService mchIncomeRecordService;

    /**
     * 商户收益记录列表
     */
    @Operation(summary = "商户收益记录列表", description = "")
    @Parameters({
            @Parameter(name = "iToken", description = "用户身份凭证", required = true, in = ParameterIn.HEADER),
            @Parameter(name = "pageNumber", description = "分页页码"),
            @Parameter(name = "pageSize", description = "分页条数"),
            @Parameter(name = "mchNo", description = "商户号"),
            @Parameter(name = "payOrderId", description = "支付订单号")
    })
    @PreAuthorize("hasAuthority('ENT_MCH_INCOME_LIST')")
    @GetMapping
    public ApiRes<IPage<MchIncomeRecord>> list() {
        MchIncomeRecord queryObject = getObject(MchIncomeRecord.class);
        IPage<MchIncomeRecord> pages = mchIncomeRecordService.selectPage(getIPage(), queryObject);
        return ApiRes.ok(pages);
    }

    /**
     * 商户收益统计
     */
    @Operation(summary = "商户收益统计", description = "")
    @Parameters({
            @Parameter(name = "iToken", description = "用户身份凭证", required = true, in = ParameterIn.HEADER),
            @Parameter(name = "mchNo", description = "商户号", required = true),
            @Parameter(name = "startDate", description = "开始日期"),
            @Parameter(name = "endDate", description = "结束日期")
    })
    @PreAuthorize("hasAuthority('ENT_MCH_INCOME_STATS')")
    @GetMapping("/statistics")
    public ApiRes<Map<String, Object>> statistics(
            @RequestParam String mchNo,
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") Date startDate,
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") Date endDate) {

        Map<String, Object> statistics = mchIncomeRecordService.sumIncomeAmount(mchNo, startDate, endDate);
        return ApiRes.ok(statistics);
    }

    /**
     * 商户收益记录详情
     */
    @Operation(summary = "商户收益记录详情", description = "")
    @Parameters({
            @Parameter(name = "iToken", description = "用户身份凭证", required = true, in = ParameterIn.HEADER),
            @Parameter(name = "id", description = "记录ID", required = true)
    })
    @PreAuthorize("hasAuthority('ENT_MCH_INCOME_VIEW')")
    @GetMapping("/{id}")
    public ApiRes<MchIncomeRecord> detail(@PathVariable("id") Long id) {
        MchIncomeRecord record = mchIncomeRecordService.getById(id);
        if (record == null) {
            return ApiRes.fail("记录不存在");
        }
        return ApiRes.ok(record);
    }
}
