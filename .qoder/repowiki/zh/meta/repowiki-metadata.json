{"knowledge_relations": [{"id": 1, "source_id": "f6ea9ca1-e72f-49d2-9002-b468a8204836", "target_id": "582039cd-7465-43cd-bf89-6aa97148141f", "source_type": "WIKI_ITEM", "target_type": "WIKI_ITEM", "relationship_type": "PARENT_CHILD", "extra": "Wiki parent-child relationship: f6ea9ca1-e72f-49d2-9002-b468a8204836 -> 582039cd-7465-43cd-bf89-6aa97148141f", "gmt_create": "2025-09-22T22:19:10.5764373+08:00", "gmt_modified": "2025-09-22T22:19:10.5764373+08:00"}, {"id": 2, "source_id": "f6ea9ca1-e72f-49d2-9002-b468a8204836", "target_id": "e6370a0e-8724-48a2-a11a-3063f1a4843d", "source_type": "WIKI_ITEM", "target_type": "WIKI_ITEM", "relationship_type": "PARENT_CHILD", "extra": "Wiki parent-child relationship: f6ea9ca1-e72f-49d2-9002-b468a8204836 -> e6370a0e-8724-48a2-a11a-3063f1a4843d", "gmt_create": "2025-09-22T22:19:10.5857404+08:00", "gmt_modified": "2025-09-22T22:19:10.5857404+08:00"}, {"id": 3, "source_id": "f6ea9ca1-e72f-49d2-9002-b468a8204836", "target_id": "8408dd22-433f-40e5-92dd-69222cc396ba", "source_type": "WIKI_ITEM", "target_type": "WIKI_ITEM", "relationship_type": "PARENT_CHILD", "extra": "Wiki parent-child relationship: f6ea9ca1-e72f-49d2-9002-b468a8204836 -> 8408dd22-433f-40e5-92dd-69222cc396ba", "gmt_create": "2025-09-22T22:19:10.5921419+08:00", "gmt_modified": "2025-09-22T22:19:10.5921419+08:00"}, {"id": 4, "source_id": "f6ea9ca1-e72f-49d2-9002-b468a8204836", "target_id": "a07ff904-4886-4cdd-b7f6-cdf735fce308", "source_type": "WIKI_ITEM", "target_type": "WIKI_ITEM", "relationship_type": "PARENT_CHILD", "extra": "Wiki parent-child relationship: f6ea9ca1-e72f-49d2-9002-b468a8204836 -> a07ff904-4886-4cdd-b7f6-cdf735fce308", "gmt_create": "2025-09-22T22:19:10.5994085+08:00", "gmt_modified": "2025-09-22T22:19:10.5994085+08:00"}, {"id": 5, "source_id": "9556590c-b951-4cba-b70e-74f8887eee7c", "target_id": "624becd0-511a-4d90-979b-634fae7fef67", "source_type": "WIKI_ITEM", "target_type": "WIKI_ITEM", "relationship_type": "PARENT_CHILD", "extra": "Wiki parent-child relationship: 9556590c-b951-4cba-b70e-74f8887eee7c -> 624becd0-511a-4d90-979b-634fae7fef67", "gmt_create": "2025-09-22T22:19:10.6058269+08:00", "gmt_modified": "2025-09-22T22:19:10.6058269+08:00"}, {"id": 6, "source_id": "9556590c-b951-4cba-b70e-74f8887eee7c", "target_id": "d05089e4-040f-4bf9-81e4-7163e10e5c37", "source_type": "WIKI_ITEM", "target_type": "WIKI_ITEM", "relationship_type": "PARENT_CHILD", "extra": "Wiki parent-child relationship: 9556590c-b951-4cba-b70e-74f8887eee7c -> d05089e4-040f-4bf9-81e4-7163e10e5c37", "gmt_create": "2025-09-22T22:19:10.6117035+08:00", "gmt_modified": "2025-09-22T22:19:10.6117035+08:00"}, {"id": 7, "source_id": "9556590c-b951-4cba-b70e-74f8887eee7c", "target_id": "9ebbfc56-b484-4fe7-9d7f-a0fc360d0678", "source_type": "WIKI_ITEM", "target_type": "WIKI_ITEM", "relationship_type": "PARENT_CHILD", "extra": "Wiki parent-child relationship: 9556590c-b951-4cba-b70e-74f8887eee7c -> 9ebbfc56-b484-4fe7-9d7f-a0fc360d0678", "gmt_create": "2025-09-22T22:19:10.6180856+08:00", "gmt_modified": "2025-09-22T22:19:10.6180856+08:00"}, {"id": 8, "source_id": "9556590c-b951-4cba-b70e-74f8887eee7c", "target_id": "badb52ec-5efa-4094-823b-ca5a70681a23", "source_type": "WIKI_ITEM", "target_type": "WIKI_ITEM", "relationship_type": "PARENT_CHILD", "extra": "Wiki parent-child relationship: 9556590c-b951-4cba-b70e-74f8887eee7c -> badb52ec-5efa-4094-823b-ca5a70681a23", "gmt_create": "2025-09-22T22:19:10.6234048+08:00", "gmt_modified": "2025-09-22T22:19:10.6234048+08:00"}, {"id": 9, "source_id": "3de88988-e57b-404d-a7b5-8fac6383d7ce", "target_id": "2699efef-5c64-406f-b8a5-384baa85db6d", "source_type": "WIKI_ITEM", "target_type": "WIKI_ITEM", "relationship_type": "PARENT_CHILD", "extra": "Wiki parent-child relationship: 3de88988-e57b-404d-a7b5-8fac6383d7ce -> 2699efef-5c64-406f-b8a5-384baa85db6d", "gmt_create": "2025-09-22T22:19:10.6296848+08:00", "gmt_modified": "2025-09-22T22:19:10.6296848+08:00"}, {"id": 10, "source_id": "3de88988-e57b-404d-a7b5-8fac6383d7ce", "target_id": "caf88b3a-08b1-4e50-845f-0013e2ac59e9", "source_type": "WIKI_ITEM", "target_type": "WIKI_ITEM", "relationship_type": "PARENT_CHILD", "extra": "Wiki parent-child relationship: 3de88988-e57b-404d-a7b5-8fac6383d7ce -> caf88b3a-08b1-4e50-845f-0013e2ac59e9", "gmt_create": "2025-09-22T22:19:10.6357131+08:00", "gmt_modified": "2025-09-22T22:19:10.6357131+08:00"}, {"id": 11, "source_id": "3de88988-e57b-404d-a7b5-8fac6383d7ce", "target_id": "24cdeed2-3c38-46d0-aa6e-ca0344e18fad", "source_type": "WIKI_ITEM", "target_type": "WIKI_ITEM", "relationship_type": "PARENT_CHILD", "extra": "Wiki parent-child relationship: 3de88988-e57b-404d-a7b5-8fac6383d7ce -> 24cdeed2-3c38-46d0-aa6e-ca0344e18fad", "gmt_create": "2025-09-22T22:19:10.6426863+08:00", "gmt_modified": "2025-09-22T22:19:10.6426863+08:00"}, {"id": 12, "source_id": "3de88988-e57b-404d-a7b5-8fac6383d7ce", "target_id": "7f5db0b6-b8fa-4159-a99a-b2edfc93a6b7", "source_type": "WIKI_ITEM", "target_type": "WIKI_ITEM", "relationship_type": "PARENT_CHILD", "extra": "Wiki parent-child relationship: 3de88988-e57b-404d-a7b5-8fac6383d7ce -> 7f5db0b6-b8fa-4159-a99a-b2edfc93a6b7", "gmt_create": "2025-09-22T22:19:10.6488635+08:00", "gmt_modified": "2025-09-22T22:19:10.6488635+08:00"}, {"id": 13, "source_id": "3de88988-e57b-404d-a7b5-8fac6383d7ce", "target_id": "d5e83b7d-e68c-4e29-bd55-961c0619f409", "source_type": "WIKI_ITEM", "target_type": "WIKI_ITEM", "relationship_type": "PARENT_CHILD", "extra": "Wiki parent-child relationship: 3de88988-e57b-404d-a7b5-8fac6383d7ce -> d5e83b7d-e68c-4e29-bd55-961c0619f409", "gmt_create": "2025-09-22T22:19:10.6547729+08:00", "gmt_modified": "2025-09-22T22:19:10.6547729+08:00"}, {"id": 14, "source_id": "845f2b3e-fceb-4fe7-a0a2-4538ee4a50ec", "target_id": "856baffc-6f5d-4ecb-84ff-562f5131e809", "source_type": "WIKI_ITEM", "target_type": "WIKI_ITEM", "relationship_type": "PARENT_CHILD", "extra": "Wiki parent-child relationship: 845f2b3e-fceb-4fe7-a0a2-4538ee4a50ec -> 856baffc-6f5d-4ecb-84ff-562f5131e809", "gmt_create": "2025-09-22T22:19:10.6607919+08:00", "gmt_modified": "2025-09-22T22:19:10.6607919+08:00"}, {"id": 15, "source_id": "845f2b3e-fceb-4fe7-a0a2-4538ee4a50ec", "target_id": "a2ee7955-4ce4-4437-9ce4-00e6273a59ea", "source_type": "WIKI_ITEM", "target_type": "WIKI_ITEM", "relationship_type": "PARENT_CHILD", "extra": "Wiki parent-child relationship: 845f2b3e-fceb-4fe7-a0a2-4538ee4a50ec -> a2ee7955-4ce4-4437-9ce4-00e6273a59ea", "gmt_create": "2025-09-22T22:19:10.6673997+08:00", "gmt_modified": "2025-09-22T22:19:10.6673997+08:00"}, {"id": 16, "source_id": "845f2b3e-fceb-4fe7-a0a2-4538ee4a50ec", "target_id": "c7ae1313-004a-4926-b17c-6d1962922981", "source_type": "WIKI_ITEM", "target_type": "WIKI_ITEM", "relationship_type": "PARENT_CHILD", "extra": "Wiki parent-child relationship: 845f2b3e-fceb-4fe7-a0a2-4538ee4a50ec -> c7ae1313-004a-4926-b17c-6d1962922981", "gmt_create": "2025-09-22T22:19:10.6741504+08:00", "gmt_modified": "2025-09-22T22:19:10.6741504+08:00"}, {"id": 17, "source_id": "845f2b3e-fceb-4fe7-a0a2-4538ee4a50ec", "target_id": "c52534cc-9cfa-4574-b177-b31fa0d5fcb0", "source_type": "WIKI_ITEM", "target_type": "WIKI_ITEM", "relationship_type": "PARENT_CHILD", "extra": "Wiki parent-child relationship: 845f2b3e-fceb-4fe7-a0a2-4538ee4a50ec -> c52534cc-9cfa-4574-b177-b31fa0d5fcb0", "gmt_create": "2025-09-22T22:19:10.6807264+08:00", "gmt_modified": "2025-09-22T22:19:10.6807264+08:00"}, {"id": 18, "source_id": "7762e899-3eb4-4645-b169-a3c616410ea3", "target_id": "71d4dce0-8ae5-4eb3-ac19-f3a11896ea48", "source_type": "WIKI_ITEM", "target_type": "WIKI_ITEM", "relationship_type": "PARENT_CHILD", "extra": "Wiki parent-child relationship: 7762e899-3eb4-4645-b169-a3c616410ea3 -> 71d4dce0-8ae5-4eb3-ac19-f3a11896ea48", "gmt_create": "2025-09-22T22:19:10.6867248+08:00", "gmt_modified": "2025-09-22T22:19:10.6867248+08:00"}, {"id": 19, "source_id": "7762e899-3eb4-4645-b169-a3c616410ea3", "target_id": "14eff330-f980-42cf-9cce-fdbfdeb23809", "source_type": "WIKI_ITEM", "target_type": "WIKI_ITEM", "relationship_type": "PARENT_CHILD", "extra": "Wiki parent-child relationship: 7762e899-3eb4-4645-b169-a3c616410ea3 -> 14eff330-f980-42cf-9cce-fdbfdeb23809", "gmt_create": "2025-09-22T22:19:10.6927256+08:00", "gmt_modified": "2025-09-22T22:19:10.6927256+08:00"}, {"id": 20, "source_id": "624becd0-511a-4d90-979b-634fae7fef67", "target_id": "f95e05eb-36fb-4787-b7b5-b29c5f981ad4", "source_type": "WIKI_ITEM", "target_type": "WIKI_ITEM", "relationship_type": "PARENT_CHILD", "extra": "Wiki parent-child relationship: 624becd0-511a-4d90-979b-634fae7fef67 -> f95e05eb-36fb-4787-b7b5-b29c5f981ad4", "gmt_create": "2025-09-22T22:19:10.6997245+08:00", "gmt_modified": "2025-09-22T22:19:10.6997245+08:00"}, {"id": 21, "source_id": "624becd0-511a-4d90-979b-634fae7fef67", "target_id": "7ce6248a-5fc4-42d6-b2e8-e5454ba4a5fb", "source_type": "WIKI_ITEM", "target_type": "WIKI_ITEM", "relationship_type": "PARENT_CHILD", "extra": "Wiki parent-child relationship: 624becd0-511a-4d90-979b-634fae7fef67 -> 7ce6248a-5fc4-42d6-b2e8-e5454ba4a5fb", "gmt_create": "2025-09-22T22:19:10.7067267+08:00", "gmt_modified": "2025-09-22T22:19:10.7067267+08:00"}, {"id": 22, "source_id": "624becd0-511a-4d90-979b-634fae7fef67", "target_id": "c01fa7ad-d479-4894-a4fe-5af83d39bae3", "source_type": "WIKI_ITEM", "target_type": "WIKI_ITEM", "relationship_type": "PARENT_CHILD", "extra": "Wiki parent-child relationship: 624becd0-511a-4d90-979b-634fae7fef67 -> c01fa7ad-d479-4894-a4fe-5af83d39bae3", "gmt_create": "2025-09-22T22:19:10.7127252+08:00", "gmt_modified": "2025-09-22T22:19:10.7127252+08:00"}, {"id": 23, "source_id": "624becd0-511a-4d90-979b-634fae7fef67", "target_id": "29b6cef6-e23e-422f-9436-bb258a595ba5", "source_type": "WIKI_ITEM", "target_type": "WIKI_ITEM", "relationship_type": "PARENT_CHILD", "extra": "Wiki parent-child relationship: 624becd0-511a-4d90-979b-634fae7fef67 -> 29b6cef6-e23e-422f-9436-bb258a595ba5", "gmt_create": "2025-09-22T22:19:10.7197245+08:00", "gmt_modified": "2025-09-22T22:19:10.7197245+08:00"}, {"id": 24, "source_id": "624becd0-511a-4d90-979b-634fae7fef67", "target_id": "ecdc44cc-d44c-4667-b0c6-5c77e7d09ea8", "source_type": "WIKI_ITEM", "target_type": "WIKI_ITEM", "relationship_type": "PARENT_CHILD", "extra": "Wiki parent-child relationship: 624becd0-511a-4d90-979b-634fae7fef67 -> ecdc44cc-d44c-4667-b0c6-5c77e7d09ea8", "gmt_create": "2025-09-22T22:19:10.726725+08:00", "gmt_modified": "2025-09-22T22:19:10.726725+08:00"}, {"id": 25, "source_id": "856baffc-6f5d-4ecb-84ff-562f5131e809", "target_id": "44aceb56-fd37-46cc-96a6-ba2afcaa8786", "source_type": "WIKI_ITEM", "target_type": "WIKI_ITEM", "relationship_type": "PARENT_CHILD", "extra": "Wiki parent-child relationship: 856baffc-6f5d-4ecb-84ff-562f5131e809 -> 44aceb56-fd37-46cc-96a6-ba2afcaa8786", "gmt_create": "2025-09-22T22:19:10.7337255+08:00", "gmt_modified": "2025-09-22T22:19:10.7337255+08:00"}, {"id": 26, "source_id": "856baffc-6f5d-4ecb-84ff-562f5131e809", "target_id": "562f0a65-6ee1-43bd-b50d-9c4fb1ff0925", "source_type": "WIKI_ITEM", "target_type": "WIKI_ITEM", "relationship_type": "PARENT_CHILD", "extra": "Wiki parent-child relationship: 856baffc-6f5d-4ecb-84ff-562f5131e809 -> 562f0a65-6ee1-43bd-b50d-9c4fb1ff0925", "gmt_create": "2025-09-22T22:19:10.7407259+08:00", "gmt_modified": "2025-09-22T22:19:10.7407259+08:00"}, {"id": 27, "source_id": "856baffc-6f5d-4ecb-84ff-562f5131e809", "target_id": "ee4a84a9-b193-45fb-96af-1d7d6cf12b40", "source_type": "WIKI_ITEM", "target_type": "WIKI_ITEM", "relationship_type": "PARENT_CHILD", "extra": "Wiki parent-child relationship: 856baffc-6f5d-4ecb-84ff-562f5131e809 -> ee4a84a9-b193-45fb-96af-1d7d6cf12b40", "gmt_create": "2025-09-22T22:19:10.7467249+08:00", "gmt_modified": "2025-09-22T22:19:10.7467249+08:00"}, {"id": 28, "source_id": "856baffc-6f5d-4ecb-84ff-562f5131e809", "target_id": "f0fc0516-a1b7-4d4c-93ca-2ab8b155b88a", "source_type": "WIKI_ITEM", "target_type": "WIKI_ITEM", "relationship_type": "PARENT_CHILD", "extra": "Wiki parent-child relationship: 856baffc-6f5d-4ecb-84ff-562f5131e809 -> f0fc0516-a1b7-4d4c-93ca-2ab8b155b88a", "gmt_create": "2025-09-22T22:19:10.7537265+08:00", "gmt_modified": "2025-09-22T22:19:10.7537265+08:00"}, {"id": 29, "source_id": "856baffc-6f5d-4ecb-84ff-562f5131e809", "target_id": "5f00cbe3-b405-49c3-94f0-362cf65a6dac", "source_type": "WIKI_ITEM", "target_type": "WIKI_ITEM", "relationship_type": "PARENT_CHILD", "extra": "Wiki parent-child relationship: 856baffc-6f5d-4ecb-84ff-562f5131e809 -> 5f00cbe3-b405-49c3-94f0-362cf65a6dac", "gmt_create": "2025-09-22T22:19:10.7587261+08:00", "gmt_modified": "2025-09-22T22:19:10.7587261+08:00"}, {"id": 30, "source_id": "d05089e4-040f-4bf9-81e4-7163e10e5c37", "target_id": "91eb7b0c-2b5e-45cf-b3e1-34fe2ab83c4b", "source_type": "WIKI_ITEM", "target_type": "WIKI_ITEM", "relationship_type": "PARENT_CHILD", "extra": "Wiki parent-child relationship: d05089e4-040f-4bf9-81e4-7163e10e5c37 -> 91eb7b0c-2b5e-45cf-b3e1-34fe2ab83c4b", "gmt_create": "2025-09-22T22:19:10.7657265+08:00", "gmt_modified": "2025-09-22T22:19:10.7657265+08:00"}, {"id": 31, "source_id": "d05089e4-040f-4bf9-81e4-7163e10e5c37", "target_id": "a331d706-38a5-42ba-bd1a-0f22c33989b7", "source_type": "WIKI_ITEM", "target_type": "WIKI_ITEM", "relationship_type": "PARENT_CHILD", "extra": "Wiki parent-child relationship: d05089e4-040f-4bf9-81e4-7163e10e5c37 -> a331d706-38a5-42ba-bd1a-0f22c33989b7", "gmt_create": "2025-09-22T22:19:10.771726+08:00", "gmt_modified": "2025-09-22T22:19:10.771726+08:00"}, {"id": 32, "source_id": "d05089e4-040f-4bf9-81e4-7163e10e5c37", "target_id": "5237ed5e-145f-4a20-9d97-170566908058", "source_type": "WIKI_ITEM", "target_type": "WIKI_ITEM", "relationship_type": "PARENT_CHILD", "extra": "Wiki parent-child relationship: d05089e4-040f-4bf9-81e4-7163e10e5c37 -> 5237ed5e-145f-4a20-9d97-170566908058", "gmt_create": "2025-09-22T22:19:10.7777261+08:00", "gmt_modified": "2025-09-22T22:19:10.7777261+08:00"}, {"id": 33, "source_id": "d05089e4-040f-4bf9-81e4-7163e10e5c37", "target_id": "f6bf9e8b-e8c6-4cd5-aa6b-ce5b72cd0ec5", "source_type": "WIKI_ITEM", "target_type": "WIKI_ITEM", "relationship_type": "PARENT_CHILD", "extra": "Wiki parent-child relationship: d05089e4-040f-4bf9-81e4-7163e10e5c37 -> f6bf9e8b-e8c6-4cd5-aa6b-ce5b72cd0ec5", "gmt_create": "2025-09-22T22:19:10.7837258+08:00", "gmt_modified": "2025-09-22T22:19:10.7837258+08:00"}, {"id": 34, "source_id": "9ebbfc56-b484-4fe7-9d7f-a0fc360d0678", "target_id": "8b731572-f320-415e-aee2-4b80ebc49ae6", "source_type": "WIKI_ITEM", "target_type": "WIKI_ITEM", "relationship_type": "PARENT_CHILD", "extra": "Wiki parent-child relationship: 9ebbfc56-b484-4fe7-9d7f-a0fc360d0678 -> 8b731572-f320-415e-aee2-4b80ebc49ae6", "gmt_create": "2025-09-22T22:19:10.7905994+08:00", "gmt_modified": "2025-09-22T22:19:10.7905994+08:00"}, {"id": 35, "source_id": "9ebbfc56-b484-4fe7-9d7f-a0fc360d0678", "target_id": "2fb22635-f226-4a62-95ec-8b3446fbef6e", "source_type": "WIKI_ITEM", "target_type": "WIKI_ITEM", "relationship_type": "PARENT_CHILD", "extra": "Wiki parent-child relationship: 9ebbfc56-b484-4fe7-9d7f-a0fc360d0678 -> 2fb22635-f226-4a62-95ec-8b3446fbef6e", "gmt_create": "2025-09-22T22:19:10.7971952+08:00", "gmt_modified": "2025-09-22T22:19:10.7971952+08:00"}, {"id": 36, "source_id": "9ebbfc56-b484-4fe7-9d7f-a0fc360d0678", "target_id": "e359f432-7ef9-4a4e-a6b3-c546fd4acd35", "source_type": "WIKI_ITEM", "target_type": "WIKI_ITEM", "relationship_type": "PARENT_CHILD", "extra": "Wiki parent-child relationship: 9ebbfc56-b484-4fe7-9d7f-a0fc360d0678 -> e359f432-7ef9-4a4e-a6b3-c546fd4acd35", "gmt_create": "2025-09-22T22:19:10.8035606+08:00", "gmt_modified": "2025-09-22T22:19:10.8035606+08:00"}, {"id": 37, "source_id": "9ebbfc56-b484-4fe7-9d7f-a0fc360d0678", "target_id": "5ef2270b-d24b-493c-a77e-425b5005151b", "source_type": "WIKI_ITEM", "target_type": "WIKI_ITEM", "relationship_type": "PARENT_CHILD", "extra": "Wiki parent-child relationship: 9ebbfc56-b484-4fe7-9d7f-a0fc360d0678 -> 5ef2270b-d24b-493c-a77e-425b5005151b", "gmt_create": "2025-09-22T22:19:10.8091137+08:00", "gmt_modified": "2025-09-22T22:19:10.8091137+08:00"}, {"id": 38, "source_id": "badb52ec-5efa-4094-823b-ca5a70681a23", "target_id": "c7584c25-59fd-431f-b7e6-d95a18cf337a", "source_type": "WIKI_ITEM", "target_type": "WIKI_ITEM", "relationship_type": "PARENT_CHILD", "extra": "Wiki parent-child relationship: badb52ec-5efa-4094-823b-ca5a70681a23 -> c7584c25-59fd-431f-b7e6-d95a18cf337a", "gmt_create": "2025-09-22T22:19:10.8171002+08:00", "gmt_modified": "2025-09-22T22:19:10.8171002+08:00"}, {"id": 39, "source_id": "badb52ec-5efa-4094-823b-ca5a70681a23", "target_id": "3cfaa846-75b9-4932-9dfe-d352d2bcfacf", "source_type": "WIKI_ITEM", "target_type": "WIKI_ITEM", "relationship_type": "PARENT_CHILD", "extra": "Wiki parent-child relationship: badb52ec-5efa-4094-823b-ca5a70681a23 -> 3cfaa846-75b9-4932-9dfe-d352d2bcfacf", "gmt_create": "2025-09-22T22:19:10.8230562+08:00", "gmt_modified": "2025-09-22T22:19:10.8230562+08:00"}, {"id": 40, "source_id": "badb52ec-5efa-4094-823b-ca5a70681a23", "target_id": "e92b5628-8813-4eda-9e83-1e9c81f615c5", "source_type": "WIKI_ITEM", "target_type": "WIKI_ITEM", "relationship_type": "PARENT_CHILD", "extra": "Wiki parent-child relationship: badb52ec-5efa-4094-823b-ca5a70681a23 -> e92b5628-8813-4eda-9e83-1e9c81f615c5", "gmt_create": "2025-09-22T22:19:10.8298341+08:00", "gmt_modified": "2025-09-22T22:19:10.8298341+08:00"}, {"id": 41, "source_id": "badb52ec-5efa-4094-823b-ca5a70681a23", "target_id": "01c5cedd-e7a6-478a-8f7b-2f0935d56e2f", "source_type": "WIKI_ITEM", "target_type": "WIKI_ITEM", "relationship_type": "PARENT_CHILD", "extra": "Wiki parent-child relationship: badb52ec-5efa-4094-823b-ca5a70681a23 -> 01c5cedd-e7a6-478a-8f7b-2f0935d56e2f", "gmt_create": "2025-09-22T22:19:10.8364499+08:00", "gmt_modified": "2025-09-22T22:19:10.8364499+08:00"}, {"id": 42, "source_id": "f95e05eb-36fb-4787-b7b5-b29c5f981ad4", "target_id": "95997cd5-068e-4c1f-905c-5d7612eb1631", "source_type": "WIKI_ITEM", "target_type": "WIKI_ITEM", "relationship_type": "PARENT_CHILD", "extra": "Wiki parent-child relationship: f95e05eb-36fb-4787-b7b5-b29c5f981ad4 -> 95997cd5-068e-4c1f-905c-5d7612eb1631", "gmt_create": "2025-09-22T22:19:10.8438782+08:00", "gmt_modified": "2025-09-22T22:19:10.8438782+08:00"}, {"id": 43, "source_id": "f95e05eb-36fb-4787-b7b5-b29c5f981ad4", "target_id": "ad1e8852-3497-42da-ba70-68ed7<PERSON>edef", "source_type": "WIKI_ITEM", "target_type": "WIKI_ITEM", "relationship_type": "PARENT_CHILD", "extra": "Wiki parent-child relationship: f95e05eb-36fb-4787-b7b5-b29c5f981ad4 -> ad1e8852-3497-42da-ba70-68ed7eededef", "gmt_create": "2025-09-22T22:19:10.8505539+08:00", "gmt_modified": "2025-09-22T22:19:10.8505539+08:00"}, {"id": 44, "source_id": "f95e05eb-36fb-4787-b7b5-b29c5f981ad4", "target_id": "f8710b20-b1a0-405e-a229-27c1c6b7e80e", "source_type": "WIKI_ITEM", "target_type": "WIKI_ITEM", "relationship_type": "PARENT_CHILD", "extra": "Wiki parent-child relationship: f95e05eb-36fb-4787-b7b5-b29c5f981ad4 -> f8710b20-b1a0-405e-a229-27c1c6b7e80e", "gmt_create": "2025-09-22T22:19:10.8568528+08:00", "gmt_modified": "2025-09-22T22:19:10.8568528+08:00"}, {"id": 45, "source_id": "f95e05eb-36fb-4787-b7b5-b29c5f981ad4", "target_id": "39de936f-0ef1-4837-a7a5-8d338d95755f", "source_type": "WIKI_ITEM", "target_type": "WIKI_ITEM", "relationship_type": "PARENT_CHILD", "extra": "Wiki parent-child relationship: f95e05eb-36fb-4787-b7b5-b29c5f981ad4 -> 39de936f-0ef1-4837-a7a5-8d338d95755f", "gmt_create": "2025-09-22T22:19:10.8626151+08:00", "gmt_modified": "2025-09-22T22:19:10.8626151+08:00"}, {"id": 46, "source_id": "7ce6248a-5fc4-42d6-b2e8-e5454ba4a5fb", "target_id": "8aa790ac-5f42-4b25-8ea8-a7e7092a6b19", "source_type": "WIKI_ITEM", "target_type": "WIKI_ITEM", "relationship_type": "PARENT_CHILD", "extra": "Wiki parent-child relationship: 7ce6248a-5fc4-42d6-b2e8-e5454ba4a5fb -> 8aa790ac-5f42-4b25-8ea8-a7e7092a6b19", "gmt_create": "2025-09-22T22:19:10.8694503+08:00", "gmt_modified": "2025-09-22T22:19:10.8694503+08:00"}, {"id": 47, "source_id": "7ce6248a-5fc4-42d6-b2e8-e5454ba4a5fb", "target_id": "fe533d12-d180-42b3-b767-db1c48d4386d", "source_type": "WIKI_ITEM", "target_type": "WIKI_ITEM", "relationship_type": "PARENT_CHILD", "extra": "Wiki parent-child relationship: 7ce6248a-5fc4-42d6-b2e8-e5454ba4a5fb -> fe533d12-d180-42b3-b767-db1c48d4386d", "gmt_create": "2025-09-22T22:19:10.8760247+08:00", "gmt_modified": "2025-09-22T22:19:10.8760247+08:00"}, {"id": 48, "source_id": "7ce6248a-5fc4-42d6-b2e8-e5454ba4a5fb", "target_id": "60d280b5-8429-4844-b079-e24d18e1d3cb", "source_type": "WIKI_ITEM", "target_type": "WIKI_ITEM", "relationship_type": "PARENT_CHILD", "extra": "Wiki parent-child relationship: 7ce6248a-5fc4-42d6-b2e8-e5454ba4a5fb -> 60d280b5-8429-4844-b079-e24d18e1d3cb", "gmt_create": "2025-09-22T22:19:10.8819386+08:00", "gmt_modified": "2025-09-22T22:19:10.8819386+08:00"}, {"id": 49, "source_id": "3cfaa846-75b9-4932-9dfe-d352d2bcfacf", "target_id": "daee9033-a65b-419c-9203-3e2ec38f2a70", "source_type": "WIKI_ITEM", "target_type": "WIKI_ITEM", "relationship_type": "PARENT_CHILD", "extra": "Wiki parent-child relationship: 3cfaa846-75b9-4932-9dfe-d352d2bcfacf -> daee9033-a65b-419c-9203-3e2ec38f2a70", "gmt_create": "2025-09-22T22:19:10.8890004+08:00", "gmt_modified": "2025-09-22T22:19:10.8890004+08:00"}, {"id": 50, "source_id": "3cfaa846-75b9-4932-9dfe-d352d2bcfacf", "target_id": "202b110d-a3a4-463f-87cd-a670636953be", "source_type": "WIKI_ITEM", "target_type": "WIKI_ITEM", "relationship_type": "PARENT_CHILD", "extra": "Wiki parent-child relationship: 3cfaa846-75b9-4932-9dfe-d352d2bcfacf -> 202b110d-a3a4-463f-87cd-a670636953be", "gmt_create": "2025-09-22T22:19:10.8949742+08:00", "gmt_modified": "2025-09-22T22:19:10.8949742+08:00"}, {"id": 51, "source_id": "c01fa7ad-d479-4894-a4fe-5af83d39bae3", "target_id": "28f8a8c2-d45a-41dd-894e-546f8c38ce36", "source_type": "WIKI_ITEM", "target_type": "WIKI_ITEM", "relationship_type": "PARENT_CHILD", "extra": "Wiki parent-child relationship: c01fa7ad-d479-4894-a4fe-5af83d39bae3 -> 28f8a8c2-d45a-41dd-894e-546f8c38ce36", "gmt_create": "2025-09-22T22:19:10.9025713+08:00", "gmt_modified": "2025-09-22T22:19:10.9025713+08:00"}, {"id": 52, "source_id": "c01fa7ad-d479-4894-a4fe-5af83d39bae3", "target_id": "dd3ee873-3fed-4abe-a6c6-6782c201bde5", "source_type": "WIKI_ITEM", "target_type": "WIKI_ITEM", "relationship_type": "PARENT_CHILD", "extra": "Wiki parent-child relationship: c01fa7ad-d479-4894-a4fe-5af83d39bae3 -> dd3ee873-3fed-4abe-a6c6-6782c201bde5", "gmt_create": "2025-09-22T22:19:10.9083284+08:00", "gmt_modified": "2025-09-22T22:19:10.9083284+08:00"}, {"id": 53, "source_id": "c01fa7ad-d479-4894-a4fe-5af83d39bae3", "target_id": "b2e7f8ec-28a4-4927-9c0a-bd7e6c3d37f7", "source_type": "WIKI_ITEM", "target_type": "WIKI_ITEM", "relationship_type": "PARENT_CHILD", "extra": "Wiki parent-child relationship: c01fa7ad-d479-4894-a4fe-5af83d39bae3 -> b2e7f8ec-28a4-4927-9c0a-bd7e6c3d37f7", "gmt_create": "2025-09-22T22:19:10.9140563+08:00", "gmt_modified": "2025-09-22T22:19:10.9140563+08:00"}, {"id": 54, "source_id": "c01fa7ad-d479-4894-a4fe-5af83d39bae3", "target_id": "30284771-dc6e-4651-a0e3-1b4525e170cf", "source_type": "WIKI_ITEM", "target_type": "WIKI_ITEM", "relationship_type": "PARENT_CHILD", "extra": "Wiki parent-child relationship: c01fa7ad-d479-4894-a4fe-5af83d39bae3 -> 30284771-dc6e-4651-a0e3-1b4525e170cf", "gmt_create": "2025-09-22T22:19:10.9243133+08:00", "gmt_modified": "2025-09-22T22:19:10.9243133+08:00"}, {"id": 55, "source_id": "e92b5628-8813-4eda-9e83-1e9c81f615c5", "target_id": "95804084-1ac5-453d-bd92-a8ab299c88e8", "source_type": "WIKI_ITEM", "target_type": "WIKI_ITEM", "relationship_type": "PARENT_CHILD", "extra": "Wiki parent-child relationship: e92b5628-8813-4eda-9e83-1e9c81f615c5 -> 95804084-1ac5-453d-bd92-a8ab299c88e8", "gmt_create": "2025-09-22T22:19:10.9399422+08:00", "gmt_modified": "2025-09-22T22:19:10.9399422+08:00"}, {"id": 56, "source_id": "e92b5628-8813-4eda-9e83-1e9c81f615c5", "target_id": "1979465e-bc1c-4566-b5b3-3183a802cbc4", "source_type": "WIKI_ITEM", "target_type": "WIKI_ITEM", "relationship_type": "PARENT_CHILD", "extra": "Wiki parent-child relationship: e92b5628-8813-4eda-9e83-1e9c81f615c5 -> 1979465e-bc1c-4566-b5b3-3183a802cbc4", "gmt_create": "2025-09-22T22:19:10.9469491+08:00", "gmt_modified": "2025-09-22T22:19:10.9469491+08:00"}, {"id": 57, "source_id": "29b6cef6-e23e-422f-9436-bb258a595ba5", "target_id": "50409fde-ddab-4aca-b782-cecd0ce5c90a", "source_type": "WIKI_ITEM", "target_type": "WIKI_ITEM", "relationship_type": "PARENT_CHILD", "extra": "Wiki parent-child relationship: 29b6cef6-e23e-422f-9436-bb258a595ba5 -> 50409fde-ddab-4aca-b782-cecd0ce5c90a", "gmt_create": "2025-09-22T22:19:10.9542035+08:00", "gmt_modified": "2025-09-22T22:19:10.9542035+08:00"}, {"id": 58, "source_id": "29b6cef6-e23e-422f-9436-bb258a595ba5", "target_id": "07ca4c56-0dad-4888-a101-8efc8bc715e8", "source_type": "WIKI_ITEM", "target_type": "WIKI_ITEM", "relationship_type": "PARENT_CHILD", "extra": "Wiki parent-child relationship: 29b6cef6-e23e-422f-9436-bb258a595ba5 -> 07ca4c56-0dad-4888-a101-8efc8bc715e8", "gmt_create": "2025-09-22T22:19:10.9607906+08:00", "gmt_modified": "2025-09-22T22:19:10.9607906+08:00"}, {"id": 59, "source_id": "29b6cef6-e23e-422f-9436-bb258a595ba5", "target_id": "96d72f62-077e-45e4-a33d-a0bb7b08c887", "source_type": "WIKI_ITEM", "target_type": "WIKI_ITEM", "relationship_type": "PARENT_CHILD", "extra": "Wiki parent-child relationship: 29b6cef6-e23e-422f-9436-bb258a595ba5 -> 96d72f62-077e-45e4-a33d-a0bb7b08c887", "gmt_create": "2025-09-22T22:19:10.9670778+08:00", "gmt_modified": "2025-09-22T22:19:10.9670778+08:00"}, {"id": 60, "source_id": "01c5cedd-e7a6-478a-8f7b-2f0935d56e2f", "target_id": "145a1e49-68cd-490f-8e43-44f986a4b888", "source_type": "WIKI_ITEM", "target_type": "WIKI_ITEM", "relationship_type": "PARENT_CHILD", "extra": "Wiki parent-child relationship: 01c5cedd-e7a6-478a-8f7b-2f0935d56e2f -> 145a1e49-68cd-490f-8e43-44f986a4b888", "gmt_create": "2025-09-22T22:19:10.9736707+08:00", "gmt_modified": "2025-09-22T22:19:10.9736707+08:00"}, {"id": 61, "source_id": "01c5cedd-e7a6-478a-8f7b-2f0935d56e2f", "target_id": "654a0096-939c-458a-a3e0-48849eb784ad", "source_type": "WIKI_ITEM", "target_type": "WIKI_ITEM", "relationship_type": "PARENT_CHILD", "extra": "Wiki parent-child relationship: 01c5cedd-e7a6-478a-8f7b-2f0935d56e2f -> 654a0096-939c-458a-a3e0-48849eb784ad", "gmt_create": "2025-09-22T22:19:10.9798686+08:00", "gmt_modified": "2025-09-22T22:19:10.9798686+08:00"}], "wiki_catalogs": [{"id": "03a715da-a492-42d0-9ebe-c04cfc0eef7f", "repo_id": "94f96537-e6cc-41d0-af2e-17841bee1253", "name": "系统概述", "description": "system-overview", "prompt": "创建关于uni-pay统一支付平台的全面内容。解释其作为全栈微服务架构的核心目的和价值，涵盖支持多支付渠道、多角色管理（商户、代理商、运营）、异步消息通信等关键特性。描述系统整体架构，包括前端UI、后端微服务、公共组件和数据库的交互关系。为初学者提供高层次的概念性概述，同时为经验丰富的开发者提供技术细节，如Spring Boot微服务间的通信机制、Vue.js前端与后端API的集成方式。使用项目中的实际示例，如通过消息队列实现配置热更新的流程。包括系统上下文图和高层次架构图，说明数据流和组件边界。", "parent_id": "", "order": 0, "progress_status": "completed", "dependent_files": "README.md,unipay-portal/项目总览.md", "keywords": "", "workspace_path": "", "gmt_create": "2025-09-22T20:36:07.7068041+08:00", "gmt_modified": "2025-09-22T20:44:22.7921267+08:00", "raw_data": "WikiEncrypted:gWB8HBj+8+/15rQhXgtMjECeCGuLoR2IwruDlfaI6j2FXdOVt+RFdCtMEaZKSc/nUxvTMx6LkZpQMzftkoUEBmLwoTt/XSmczrc9Fl3biH/pKQhKsiYAoRL7MbHT2qqcddmi/HfXi1x9mRvKYQyEn3mgFpExw96r8snxGesMmV9jIgbbxlYv/sObhTW++vvstNscR6asRGhZ4b8SRYtIYbFCFNrB6eSA0qCmTRp1MxqlJ2aTLcubB0tFA97IQi7dRAKuZC/wv/zF+HFguZOR1zmfEFPH8Ci5glz6pggSzhR0ARLbeEI2GxIW6fYD7O1Ao4edk2O0gfoZTIShLkUp/v4fxFKrrEUGmonc6DyDlwDSqjbD3O5e522BomX+74tyMbCJsd81rb4v6GIHS/9SqEzZ3LJ5Gx3+5VLLkajHDmqsO2WP3b4ux9dTaHSYibWBQX/n7Lt7IPFyYxQmDWDVmengPq4jrci2haqDIFjaTE+SPgGRHxSjGMNgsr+7EUT3VOLmj+MNIy6YoO+vkEUyBACV5iKVbYAHb8wS1X/7ZPgnDWOjGxWzY34+mgjhV9nwA42iRGlj2p7T8Pf271nRkUqocdobh/BYyqRhdgib4mOsDsh2HQJJnoyZaNvtOAgeupxPEnoxYp2iSnlIlubrXJzLRomjXrnEwRzD0jVKhENQFBeKMKZ5bOPwJxtfhn0DAKfhzkqe/OwMdc6XzpGYVioS6Xuvi5ca2l0zmy90AErVA/nEc/sRxfwRDJuK2cNetPn+5s9XRUVJT/hx5rL+Dp8LXBiRVt5R3+OpiVCDWHyXF/VEizYgkX8gF5OrmFuVrcZryYy+I1c1Ja/wRjCJF9YQKGbQ1Sk+nR/eioN6ZNuQIfHDHu2zW97d3O9J9sVEWGki2PD0AaBEEYrwluMtTfOUHOtHFHokvoc2E+t26RVB791marxA5rq8UDeR3zmR8gT71rLN2QPoTcz9G+yO0XGDapWs8A1gp9JqQdochTYarpYq1wzVeA88R0G8qx58imTTkScRb7DueMFYhf6qLenj5wAB7q11sHcjVp4pT9FH3zr8iDi01r/0lSVhZJ/IU1ZZCgCzCyi37gpKfBCXTp/CsyfTg8cvZpaiPXDLRmug2A7xsy3crzX2Gukf0KHdiepxW/fjtEokpWEipEkrXzJcxLStkd8i5I9K7jLYoY3lAzNPi2pHPmAnHJxGkcq0nlcO3HO0w5v/jq9wqQxI+bcI/+c/8R72v4IpJEiF+0JqxV+h2Pr399oWnNqZtpEs", "layer_level": 0}, {"id": "9dc2cf0f-4dd9-4667-9176-27002bba3dd6", "repo_id": "94f96537-e6cc-41d0-af2e-17841bee1253", "name": "支付网关", "description": "payment-gateway", "prompt": "创建关于支付网关的详细内容。详细阐述支付网关作为系统核心组件的职责，包括统一下单、支付查询、退款、转账等核心支付功能的实现。解释其如何通过IPaymentService接口与不同的支付渠道（如微信、支付宝）进行集成，并通过策略模式动态路由到具体的支付实现。描述其与核心模块（core）和消息队列组件（components-mq）的依赖关系。为开发者提供支付请求的处理流程、异常处理机制和性能优化建议，例如使用Redis缓存支付结果。", "parent_id": "6c28c636-0567-4d46-9564-c10d609484e7", "order": 0, "progress_status": "completed", "dependent_files": "sys-payment/src/main/java/com/unipay/pay/bootstrap/PaymentApplication.java,sys-payment/src/main/java/com/unipay/pay/channel/IPaymentService.java,sys-payment/src/main/java/com/unipay/pay/ctrl/payorder/PayOrderController.java,sys-payment/src/main/java/com/unipay/pay/service/PayOrderProcessService.java", "keywords": "", "workspace_path": "", "gmt_create": "2025-09-22T20:36:24.8472233+08:00", "gmt_modified": "2025-09-22T21:03:10.7061362+08:00", "raw_data": "WikiEncrypted:lcOTJMVsDrhWtJI8Bt/2Jixn6FlWvkdCVXmhUri2b7bRr9zDpeolouX6PG+A1+brvWqTIS6+vKzKtUamuIOzrm2vBBP+WHsQbrElfEZDc48IDnA5ZkdwsMG6VkfJSINacXay1cjU+XYm5uIpiq/1bPG7h5258rkGirGoRLQ77xRK4abWLwNWFmSI7X0a2pA2XUgmREXvZ0yuNBWiyHBtmWPKZTSwvb35pyPyeaQmKZ5ar2rV8QpfzBttawiyl2QUl2ybKzwd2xg40T8iBW2U70OLrfTZAZSBPsSdg7s6p07EIUC8GmVvo5+Rd3uN4Q8Fc/lVfhZPkUbCbhbmMSqniljma1nKivWa1/TtxSfFGTRB1CF/W/kWaSFxGagQayd9M8ZH+RwFqmrLB16OlWt316VigKQMXCQsNR/2TENXc07eZ1JkBLGO0pcqFUEgbsxV+0TiR7sBEoi8Y6wizoODG9FowAUDXojqDGsxnUv6QEO7LSQBtQqn3ugIXk5v+xDwHn2Ng1841JOODq/VeWXsv7iZojfOCAUM+HirZesIs8qblejUjaz9MOCfYQhc8e1ZfNrY1UrKcQ91JVSjjDvvT25IURG9oxjeAMNXPAp8Hjg/CaXC56R/4AnimEf00+W6civWkUJFaEP8qtwPlcHZ5cUuqcRsTLQ2373n/+LEB9XTHKL07k4cyrK+xBerhQO6m7hW3yGN+PixIjVN24GpWn3N1Jrl2zmb5H3iWDipi7NehlW843noIvCEnnFkjiFckWXoDeKod8z5pqJjtZ5NG1FCNT8uc9O0+NDqACCnIiji+3B3fFxsgpzymRjG8MZL1b1MNiJe464tUoJM9lvRVZIcP3kQVZKzyeCrGqbyguuA5ZCvFU7G9AECL14WU7rjOnkD2BnGIDqqNY87ztKeFdJkRRqtFfgaKRXWJ+SBEccOxrjc5/18eRgmchadu+tNMWSfjAq65r8W6uDvVGOYixNWj3gQ4lt2RRQxho+dLtv3fUB1xbbguMj4NTERzhxpRzHn5lt2jTL1lT/fIHMkgvTkQaJhbKWZuEZxQctU/U1LqSN1maoQuzYnwRPkQ4InsIsBFVTMHjv+Oc5dUhp+8+t2AWmG1c0AEf9AAZz+RKCa0AmooqxwIYSMu1Ie+FEnQawYf7de0RrmCzVQqnglN02qBUHMTcjErB98d2lZCjplsz3R5CiKJzxahY+oS7PTO3OxSOcaWffZ4flcoA/6ZR1CVZZveHrZ/6x9z9FfwtddfA4tBzrsvLaDN0MRLA/Kr1xywFlLZoTqZCWjcLvUk4QTqs9F8V3tEz4UmgpVzQIuCgdurXxfX4p3YafVV7sHH3juWIdh9AZKk0jnuiIxbB4czbyA3rRgGnQcc1miuisGD7jg2rINlkp+uGQi8uxbzIS4yuiisUKXcoURizxjG+Z16S6B63c1NM9jCjrVax3cYUkdGNU/e1OGEywwmxP+m5XJ9y+HR+I5jVWWJK2jpIP+KoO8siVFEf542XxV4eU=", "layer_level": 1}, {"id": "566efde5-8287-43c6-8caa-675d0d56de77", "repo_id": "94f96537-e6cc-41d0-af2e-17841bee1253", "name": "代理商前端", "description": "frontend-architecture-agent", "prompt": "创建关于代理商前端应用的详细内容。介绍该应用作为代理商管理门户的角色和功能，包括代理商信息管理、下级商户管理、分润记录查看等功能。解释基于Vue 3和TypeScript的技术栈，以及Vite构建工具的配置。详细说明路由生成器(generator-routers.js)如何根据权限动态生成菜单。描述JeepayTable、JeepayLayout等自定义组件在代理商管理列表中的应用。提供API请求封装(request.js)和状态管理(store)的使用示例。为开发者提供界面定制和功能扩展的指导。", "parent_id": "776d5083-fe44-4ca2-a556-e434cfd87497", "order": 0, "progress_status": "completed", "dependent_files": "unipay-web-ui/unipay-ui-agent/src/main.ts,unipay-web-ui/unipay-ui-agent/src/layouts/BasicLayout.vue,unipay-web-ui/unipay-ui-agent/src/views/agent/AgentList.vue,unipay-web-ui/unipay-ui-agent/src/api/manage.js,unipay-web-ui/unipay-ui-agent/src/store/modules/user.ts", "keywords": "", "workspace_path": "", "gmt_create": "2025-09-22T20:36:39.2414195+08:00", "gmt_modified": "2025-09-22T21:03:00.8467435+08:00", "raw_data": "WikiEncrypted:rJ/rIw0gVau8jPGqKFBAsAycLDB6WVqwg825dG7e4d5dO3TmpnxsnChYq+MY3e4Wbfl90fAa+bygt1C8fHzjZUZvxdElMuzlRLhy0VkmuCGj9RmheXRGHAz4cizxBT8XcQt3SSX7YisJHwaHliTbMNTuYYxXp8+vhCqc3Pn7a5v0puVMOSR6rgRxG2E5f7yMS74pon/aCMpwCoJo+LxzQA6RkSn55Ve42OBzIm2TCH/gzTfH104RkbV8HYMNT20pvOI0+Q+ysw1BpphpI7NYa7vgZPBvhvQ2RZsBoczha5I4JurTJ46Y8DbJqloFFQ7uqMS6WO16SN02YJPz0MTFFA5w+j41mpMJDD3oMXdiJ/3Busm5vIMPQVFbumBSRB9M2tFQxOlK4AMUZ2vh6Ra9DUAPi0B0lejyeiMT8HyznYeqJ+3O6UdMGg7WSwOR6o6XRBGLkny6Ih9jhw5usz8Bl1hupuwWLH5Ig8cpFRQxJj9yZPVkUf0ymWoXyiTNgM3Xys9eycc0WXN/5+gK8GUZsyXu//cT1AIWSnzaCevtPBtd9IdZi8//Cqb/HYrga4CNs9o1WqzmZvSswzdn3rlCEsz7zeUps+zEveX6M7gzz7WfKLmkA4PwRRCSoSKo4KvCUBbhxe1/p/99oasQzpmqzf0yS0CSocMUHjswVMfhE265iNHA4SsvBPG7RpiRLFquBJRMT9VM4jKHEZe/9I8DgvTR3KnByV43nk6dtDUuXSxC/Z0xVsIm44pec/kYpnirs5qeFMrmVe7yAxgzS9KtjgEOBcK0PK8WEnedkF4MlL2j2OzYCG+I9n5ui8ZK1nx0avx2ragT+fuJeH6tMRETBJqbN3bmAcrBCKk1zsEVLU9AUIjiM3GH3DVzVG/PAzfQYdBVvWb4rFs3i+4yLEDGPBXo/G5qzbGuZ10TInYxjbH0WsST/95Z+5bUQVS8siA1BKuD3D1YKGYIuJCaEv15c91b5T62wCWSydP1mpvef0ehhAtrs4ii4FZjGfBNR7SARtKKdDTZmRLHq8RPu4kUIX6ORw6N7a0cQ22rwFc6Lo1nXUJi+qVRtiXrQ4MgDJydRir1FufZhoGQFxXbaN4X+tTvYNttx/XxMVb92vfnJ6PZpnas67yVgriUyULvkuliuCzXBcAE/4EhWXADlgYL2tc3Ihs+f67QF/7jWcfNSq99pN0wj0rFliGeEeFRx8O9EfOIXtFk58+u41+VCO/RpMqW4r0cMhkE+mFWaT8SG1xxppJ8jR5BJHdEObHORVTnrHZhkCnOocGtUku2mM16wE+NPuY3kgoH/wcmeYnyL9WuvSBvGAKfRERtja9xRfodPL/4NCLL09TvHL+4+gi3QOyJGzsUfKYLJbPjc7GdTvRQdeiAbTiWrH5FKu9pKnwPflGEMsgmtfXd26+6sXMoW8AUwiW8B0/iO8TI5jL7hAaif2kGLUdd/7bbpOdTsAoK", "layer_level": 1}, {"id": "a8b7b27a-17c1-410a-a058-55923eba01d8", "repo_id": "94f96537-e6cc-41d0-af2e-17841bee1253", "name": "支付服务", "description": "payment-service", "prompt": "创建关于支付服务的详细内容，涵盖统一下单、订单查询、退款和转账四大核心功能。详细解释UnifiedOrderController的请求处理流程，包括参数验证、商户配置加载、支付通道选择和支付请求转发。描述PayOrderProcessService如何协调支付订单的生命周期管理，包括状态转换、异步通知和超时处理。提供实际代码示例展示如何通过API发起支付请求，并解释响应数据结构。使用序列图说明从用户发起支付到最终结果通知的完整流程。文档应包含常见错误码解释、签名验证机制和安全最佳实践。", "parent_id": "323cc3e9-9355-4b42-b596-ce729a56c076", "order": 0, "progress_status": "completed", "dependent_files": "sys-payment/src/main/java/com/unipay/pay/ctrl/payorder/PayOrderController.java,sys-payment/src/main/java/com/unipay/pay/service/PayOrderProcessService.java,sys-payment/src/main/java/com/unipay/pay/service/ValidateService.java,sys-payment/src/main/java/com/unipay/pay/rqrs/payorder/UnifiedOrderRQ.java,sys-payment/src/main/java/com/unipay/pay/rqrs/payorder/UnifiedOrderRS.java", "keywords": "", "workspace_path": "", "gmt_create": "2025-09-22T20:36:52.9348455+08:00", "gmt_modified": "2025-09-22T21:05:12.9321468+08:00", "raw_data": "WikiEncrypted:lcOTJMVsDrhWtJI8Bt/2JuUIzDclorI2NrV3ZBndH6kwJ7Ajkx/e+/Xs/yhv+Ya4i+qnWh3NajQYPet4Yj2T86qm04rpz+/g59CyFCt8wPQ2Vema1O3GDUIA4pxeGQlhCx7HGgZc8UiyKzXj15xbnaGPOnpKUbTyTU0h13wVraP/FruiVkEfEXyUjnWt1vd12xYA4SyQ41jmaQmsMdVju0npKAGNr8sxBys6zQVX6M8EQ9kgQsqDYc+qWVK1xh3w3sUd5gh/wDW4PcC4E4WsMEUE6psp17+9hn3T+5nQdgBbqhy7lCuv0VXxeMalfZqYPEWfHjTINWmvhzh7jXi586csyPPdj3aOpiH06n1XlpF7qDqJu1QmMlOVsChGWob3n7OJGF6MQkOZpFSA1Q+sw+vvT+Su7bUjjMUZ5Y62nf2/cIUWTQRKwbs35FQLGkcshpdk8499isIao/i4JJunHPK8liAE1JyHk7dHuv/AHbUO9BuFjhITeQybb0vaXV+5VIFELUpREW6GTsy8QF3VGD+wy0Vn+F4vneWH9wU8NF99cuz3MgxCNq/7oeJ0Y0MpQJwc3Ienr0ePZBAxdNCcAsnRkrYaTzu9GYSjXPkNGuRquaIDmDMVPY7Bvk1M/U8PdHmPpzg1LkGva3Q7D03uTbN8PglCoUp4CWR2yYgULkm9LaKqQVdu+AsIapVmF9+LTP1Sfc673EmMJD4Pi4bStp2O+gbv7sLw65Gs9jlStkYO4ekZhcHisu7pDj9JBiN56YmFTM+mIzTB185DTrQjP/LPFkFvTjUdjya8POL2X06YeqMoQ7kU5qQIQMx0gtpp5mjxwAkjwfy9qSWaX5xRsiCPm8pkdZcd7jWbP4ltdbD4TM3gWhJfkJGgVANe9AP7Xg3MbvZ6nCi+Rt/sbqMOjITBCv5iqV+u+H6XkkhBBCpRcY9OuUzvPSvUKqTWrfCdAJa7llrTMUm+nwg+pRkO3G649YaV9WZCeDW0o59DWNGUhsjL164sroV6CcI0WYmNQELoKVXZt3XJuvYbhitDbj50eTuZriDGO54fEAuXi7+Vvc+Ai7dNNVBWoPA0bF7hMwNCaLlRz6zU2cInmsMaGI7z/foSTJQCUBOq5s3Z4++gV5yIXVVQQTlTbPtOPoIGZhvZpmBxrMCMUqWmWAyi/afkC4XxsCahKduQbz0+CHyUN4D5JyGGtFCDHkHBLelMvc9MMSXCtozQrj4cngDRrunDK5sL0KoRsIJEOU4yhksaOSzYHxyYVjtgokt3ga/GC++yPd6Ui6sCHhBnd+Cuu35MLhQtN8UiGoKhWJ4kd5XLBws+mU1UB679KcD0teD/IIHdtxZ47uwDfvjFkLOdio3f9f7OJDYB+8pNSa2I+b8XGKy5piNgeDK8vkH2IUmZH+s7Qy+BY3SkYhwA2aFxWwaKjlktUPSxafvUQhPNQ7svd9BDc63bpQYxvr8Vu6xcHrtjlcTHJh2uDsEV3IUhT1XMZ9xEbql1cx6IzutgSlrSQsIvdYI0sZB/m9HCILI2kICrRPZGKfbBA/4KKp9rj9ih/jCCjxh/J9UIoBL3nZyXKTs40GrEcKMt4k+qEKfbuQoClGOCpnPWS6LxXJijqokFkMy+ovYLGwcT9gb5M/9Yct79nbeSBJTOeet+3E6AvrJ1Lxbky4kYwz4kpsm4H9X0EOMOL0zJWDlxjJ0Ve3jtF5n50vsWRj/3fQKQbgMwjgUFREwhHb/YcS8vYkhF7A==", "layer_level": 1}, {"id": "20390a93-7ada-4338-9006-b06206c31398", "repo_id": "94f96537-e6cc-41d0-af2e-17841bee1253", "name": "支付API", "description": "payment-api", "prompt": "创建支付API的详细文档，重点描述统一下单接口。文档化POST /api/pay/unifiedOrder的HTTP方法、URL路径、请求头（包含认证token）、请求体（JSON Schema，基于UnifiedOrderRQ）和响应体（基于UnifiedOrderRS）的结构。提供完整的请求/响应示例，涵盖不同支付方式（如支付宝、微信）。详细说明必填和可选参数、签名生成算法、异步通知机制。解释订单状态流转和超时处理策略。提供错误码列表及其解决方案，并给出Java和Python的客户端调用示例。", "parent_id": "fa5aad6c-5218-42b9-ae16-cb022c852b19", "order": 0, "progress_status": "completed", "dependent_files": "sys-payment/src/main/java/com/unipay/pay/ctrl/payorder/UnifiedOrderController.java,sys-payment/src/main/java/com/unipay/pay/rqrs/payorder/UnifiedOrderRQ.java,sys-payment/src/main/java/com/unipay/pay/rqrs/payorder/UnifiedOrderRS.java,sys-payment/src/main/resources/markdown/doc/api1.md", "keywords": "", "workspace_path": "", "gmt_create": "2025-09-22T20:37:00.3307677+08:00", "gmt_modified": "2025-09-22T21:06:08.5768998+08:00", "raw_data": "WikiEncrypted:lcOTJMVsDrhWtJI8Bt/2JomSPSNlxixXQGITg+XBkQR1A651zRsXgTTzt7TTgrAOiNnExSU/6Oe2dMZI/1Vcu31F85KgCnzIBiHA7cRN+B56wBp75J+j5lDoYpShwy2uBs1aVkl/aGJ4l7/DHET9qN5JCbzULzUuNSHKu3Nr/oc5ZlMSfHAcgMsYCl0tM6OOMfiXyhwtvbYO8YAH5E90nugQRzAUILPBls4jqiALPltIzem+Htab8AZiYhEfzTy2wdyhHxTqvto3OnVWCeM0QWeXWxp/92bFfTEWO8VIc4kaHx2KaI/irvOMwBUjv5rCEl7bKn12nJLq1NLmx6SYEhATlqon6ntmrE7k+W/tML7/WQNPRmibw40Mo0cpoqIy6spaoaO36c4e/OAz5pFY06EQP4NATEw5Kciz/s87eKmzeter/c8cg2m22SC7IWTAaDc5RmJiOpsPMH3J2MS/n2gqjCct0Oy68IgXnZgKeWJSN2iulZkmByrUuQw0UTfuO9LqQiETB8HJvpB45R9VYFQyCx5+opJeojymkpYYJxqVVMRV/QWfKmkP3o1tl6GGrFR9ZgS29r5ui8V4oiHQZLGP66+yw9MxIPAbnjsi3eA3y4h11TP+uy2jKsEMxS4lRnv+wEsQpR2m+IZMKHehV4gC15+2D/Ew+A0D6GhPEFfpNoJH7kztcaKdml6n5N+IllUtDnAmMWU/34IDgNJB6N7FC6X7T1lP5pNfi83TCg1TwpnUkUffGrttsoqvN0U60NvwRECTzeksCefmZwaGvGga1z3SyKuaxHV4UgFsmybxwGqYAWgFnnfX3x4LFM38RhMYYHKwGV4gtlYk/xxaWMPW4MyXt7d5R1t+80Q0+izu4Tgp7XhccQ8aYa9qZ3XxyujkVFziXMfLwp/AwcOvU3UXsQmCEOH1H/NMDIkKAKkW+uqzlQJ718ofwJFkuyQYH3KRMFxZ5XKw4QsBi3LwPcKUIZI445MQ3xBQyM6bhQWc14AlQt5bXlSL7bXttW2E8RhAHTorex0UOrT+IJo+5frlMCOKrE2IOndqQSuESyt689yj2pTmgO0TXxoMRA2LvNY+/Vfhsna2zpYeNlKz+ORexZA2fDiT4fC9PIogBx9zDZEdmTe4RW8O2z4TacdD8p84qb0oWEHBtRe+i8jJoB3k8gPa/VObnb4epR57H3peoX2vM7bvbUCjU7Amoa1JLDfcjgCXaFCQvfdlegjihSEuGmlosIYtdNNWzfOBnWOnEK49KY5MnZSlqlaj0/jgd2b+HuZ+QeUzK3e9FbdRF+9NGpPxNXefNNICTv01JzamzMFFQHTv7gH2gOgMh4G7mTgGtnrskqCqCS/ZFypfj7oMF0gv9PbMrhME6dwECi5za/AEeCmkqzdhIIBIlxltNlL90rDpE9JgEK+X92Tz9PTVyRnulWJVcOQhUrfC18KgHYCZPFgypx1S/GaMUq5POoKEGqE8e+57PI0doeyOew==", "layer_level": 1}, {"id": "febddd4e-65a1-484f-9136-623cb352fd8b", "repo_id": "94f96537-e6cc-41d0-af2e-17841bee1253", "name": "设计原则", "description": "message-queue-principles", "prompt": "创建关于消息队列设计原则的详细内容。解释系统采用消息队列的核心目的：服务解耦、异步处理和流量削峰。详细阐述多MQ支持的架构设计，包括IMQSender和IMQMsgReceiver接口的抽象定义，以及如何通过工厂模式（如AliYunRocketMQFactory）实现ActiveMQ、RabbitMQ、RocketMQ和阿里云RocketMQ的无缝切换。说明消息发送的线程池管理（MqThreadExecutor）和消息序列化/反序列化机制。为开发者提供配置不同MQ厂商（MQVenderCS）的指南，并解释MQSendTypeEnum中定义的各类消息用途。", "parent_id": "ec1607f7-2606-4bfd-acfc-daffd521aed2", "order": 0, "progress_status": "completed", "dependent_files": "components/components-mq/src/main/java/com/unipay/components/mq/vender/IMQSender.java,components/components-mq/src/main/java/com/unipay/components/mq/vender/IMQMsgReceiver.java,components/components-mq/src/main/java/com/unipay/components/mq/vender/activemq/ActiveMQSender.java,components/components-mq/src/main/java/com/unipay/components/mq/vender/rabbitmq/RabbitMQSender.java,components/components-mq/src/main/java/com/unipay/components/mq/vender/rocketmq/RocketMQSender.java,components/components-mq/src/main/java/com/unipay/components/mq/vender/aliyunrocketmq/AliYunRocketMQSender.java", "keywords": "", "workspace_path": "", "gmt_create": "2025-09-22T20:37:06.114864+08:00", "gmt_modified": "2025-09-22T21:07:11.3723605+08:00", "raw_data": "WikiEncrypted:IaaHBhg4aKVK2Z27STIpf19qUDrF7RP3htCMsaOE/WJ83eLkUdOenaWOUQoUrdpwvCDhZ5+fwnOMpCuze6wkLgHUepTuQ9nhHMll64bUXO1D9+q4O6G7Bjjv9rjJNa95V6USBbwJqI1wAS9xHVm4vDdiyt1vVCxK2lzegNGvGXcXXmnkRFWtU1GoIKdRUpu1Eoi36tuJY9JCXOB9lAiJAI8nIC9DQAdb2UVejWLMgRYVTqvp0DEYJ29BnJi9jnf/8G6PAzZujrMwlTQL7M1FtR4vv3pMbvHcSovGrDqnUAlU+s9LsRiK7/nNAaHUnW8V6jZFAGBaa8SjqEDXH6gK4wT2usimMv6GaUwJPegNNvVY7x7qq+0eZE+G0XwjakvpjrAgyRcFuh9BBNE+LL1QPyIP+KH5dMPMZgRbmstE16hHFkuFIPrJVNa4+5U9l7FcR3Q7TrqVoxguh5rHNCCvvJRS9zA92M2wHuMv7V6iaqOBjLS4aMiUEDsepuQ3oXWsbx1YQIwGjj2s+OxOesU37596MLZ8BLxIkCEGyAjFoQ3UPRRUZeK5tZrG/o3EJeUShysppd49dktgi1k+533oO9MOZs9Ippfy2DvTT4uwxwwNV/WB56Ub5B+EcR9YITpute4ZR6oaFQD2GG0AebU6EYGxIUpmBuUyYzcl1RMxS+M8PemAbwsNGJPJGyoGmMMD+oOdu4UCFRt05Zhv+Hf/NrsD7Ct/eS64tB7TYRmRQV2qETP/00//axqd34RpkvDVYwAD2r3+PFKKNIH8fvMJTib2XkSAgfwT6Vfi1/nS6nXnXrFM6Y0VWvxDU7Q7o6H4FJoKnx5TZqTZRev0eatZsRV/x4iqf5qoqWpeU9Dv+6JEI7lO3965+AQ3UAzTwIzqK0QHlk8JcwoB08wJIeBo5H7KyYlozdAGN3fx4YWEPZRtK5e1q/iVJqaFGTHkTDpPR5gefpTp9S5R/vWlfNiS+RUmLg1OtUqZJITetyuiv39uAtAI3+dEtHMvpUMbVTONB84x5kTTYdlRuToMywUguYQXO72LNWW3efKkIM5SuH0FMqY9J/bn3GhvOEFezeRJmkzb7uFD6tZH65UXTCEOx8KhCaP1A+Euqi7PRRwDRVHqWdW7PXGLDbqqmyDhM5ASVwMybTAjzBH92B4AJI0rHu8cPbsbHzT3zX+YRlFIVVAK43UWcp5/6ZN10m7yITqn5j1lmtK7Sv5+DShbFiH/r8IQBVrbK+CzlkIqEjdWblMdqn07qfimlhLBE5NExbzpej+3p6nQjEmNFyluTfcYtbwdatsSg4b2f51R19o9PJlFBhqRxS39NSMOwNlZZF+iYfP1TciqZnl8gCnbzE2FQ4rIABmNOYGvxwCxBwnyKDjweMUYxcRjFCs4LBouGZwc8y9gmcLmODDJRT9BTG9bywzxs1ykkSvDlPQ1lfzllW+NHLc8TkeHrw9gq/9FhAqdgumNEwwsbNnx9AKoTbNr2mAONZ3TyAbOwKfMgu+GJ1Hj8mi3DAFdimPRhKgcBPpmVuutHErqfk1RYi76ci9LYdValyiTGz1xf8rhv59g/zZn6tL1Fiz2KR3HspwHIjienyQuYB/yzCAOpyjoVFYbHpK3RANAbmkCSYwEAW29um0Hxxoh7EmxgKCNAQxsD49D7+p5NuLGjxZbksd2/dSQwNjB3rV7jDhOZuXzfxTPTQwcsGFVuRBgdO4QtQ/W2yp4tELAV2vBNd1PSfQ8JXa5EGrql0K1R0g+BXbW60iHRedmnxubAJY22K97UcPo4v5u1QBOB0Vf+5d/WTvLeHCOh2iky0NiynAzpYbHdXOJoGVzZKXDH8JuekPzOv9tBHmbpGNKarCtlM0TST1x/8LRWc0w6JjNc5FyjsSgYcQScmk=", "layer_level": 1}, {"id": "97769763-5625-4e00-a0ed-ae7298840dd1", "repo_id": "94f96537-e6cc-41d0-af2e-17841bee1253", "name": "支付服务", "description": "payment-service", "prompt": "创建关于支付服务的详细内容。深入讲解统一下单接口(UnifiedOrderController)的实现逻辑，包括请求参数验证、商户应用配置查询、支付方式路由等流程。解释PayOrderProcessService如何协调调用具体的IPaymentService实现（如AlipayPaymentService、WxpayPaymentService）来完成支付请求。描述支付结果的同步返回与异步通知（通过PayOrderMchNotifyMQ）机制。提供代码示例展示如何扩展新的支付方式。说明支付订单状态机、超时订单处理（PayOrderExpiredTask）以及支付结果缓存策略。", "parent_id": "9dc2cf0f-4dd9-4667-9176-27002bba3dd6", "order": 0, "progress_status": "completed", "dependent_files": "sys-payment/src/main/java/com/unipay/pay/ctrl/payorder/PayOrderController.java,sys-payment/src/main/java/com/unipay/pay/service/PayOrderProcessService.java,sys-payment/src/main/java/com/unipay/pay/channel/IPaymentService.java,core/src/main/java/com/unipay/core/entity/PayOrder.java", "keywords": "", "workspace_path": "", "gmt_create": "2025-09-22T20:37:21.259338+08:00", "gmt_modified": "2025-09-22T21:25:37.6607913+08:00", "raw_data": "WikiEncrypted:lcOTJMVsDrhWtJI8Bt/2JuUIzDclorI2NrV3ZBndH6kwJ7Ajkx/e+/Xs/yhv+Ya4i+qnWh3NajQYPet4Yj2T86qm04rpz+/g59CyFCt8wPQ2Vema1O3GDUIA4pxeGQlhCx7HGgZc8UiyKzXj15xbnaGPOnpKUbTyTU0h13wVraP/FruiVkEfEXyUjnWt1vd12xYA4SyQ41jmaQmsMdVju0npKAGNr8sxBys6zQVX6M8EQ9kgQsqDYc+qWVK1xh3w3sUd5gh/wDW4PcC4E4WsMEUE6psp17+9hn3T+5nQdgBbqhy7lCuv0VXxeMalfZqYPEWfHjTINWmvhzh7jXi587A5d+vrStJZztPIXR/Pv7JPaW6z5pdo+rkgn772VnZT/PuwxN4RKyYkuc/C04mRRWw5qyuUGkoTG8Q6iuLqw5IIqWsVvZoMfYy94bBo1/XaLNjkGLEhNkiQkscVNJtq1IvoLAZQwrIrrzvP7qskC1EU7TWU3qp0YnI8UgR4urGZYoIqaGTh0qKJSbhJY8zvQ/jx+n/nWDj3F73Fin+47phwIfrNCs28alDGRnYe7EP9fkcj75PfK69CG7RoLy8IOskjQrNpaNuvzRFrwbo4wlhqymCBNrOQYjg2rw7vE3TfkL+6864iu2kn9Kj+14V0OLkRvRmjyi3fhO2bvGKjxZFNQomaXfck2KNMGE9wlnk63AkgyFVgqDTOcJXgS6YmHdb1utj+MfB3xD2iaGJilmM92Fsawxls5+EXbG0ktKzzX9X/fgvWaSUdc5QcI9q7ibINxRUDOKmeUVskJz0Hl4/TlsrPKZNAsJ7e7o86KJ3qGiIvd7KsSGIGW8OSpuvgy6Xu5oVX0Y8n2Rb9U3Zh8O7kkTNVQFuJ27FNfUJXO6Ozt/vdy7HvzyLFly36IcNW1D+3SM2TuOnBMbttHByysP7qxPjhX6a7ugLj61KPjSQ7ZUBOCWkf6JlPY97aAOYzutkSuWbEerQhYa1HslKnAQ08fAsbiVq9Ba8h5pzGFnDAoNsmZ3tlqUBsA1PHreCic97DikoO4HNRg0srLcbzJzHcCX57M54knKxNZIgC4NbjeBW3QQiDA8T16fIUBeq9nDMcTWwYMNnNnsHmUA8hKQbyRfy726z0ixi/FblawVqEXg0Men+M8riMXkB1/Js7g5ZJn8ixCsh9lvmIMtya+4psC8HvoKsqVSrhvsmScGRgBtdO64PlCCNeN7t/kT329+qSxMpIdA5m090uLbYR5Gc89/AMjef8Uq0h6VEMV8dKPtak1nU7kqVApeqTu3SDfYo4M6l1QGmSrwtShdbKHj5QNR2cgyssHbw60YhtH+nq2/6tjvayXCOXaFgBDzpox2yJ1jeJla9NcyNTVf1RfbufA0yXW8GD1dj5l6gYMMg3FwRc51JOxRF10nHrr9clvJ3aHTRJ7K0fvYxKKLp3kOlnISxX/uMziZXjhXU45uK1oPwtKHe0naEVzW3HX7eqWA==", "layer_level": 2}, {"id": "ca62663b-1d2d-4ca1-9625-cc960a5db571", "repo_id": "94f96537-e6cc-41d0-af2e-17841bee1253", "name": "商户信息管理", "description": "merchant-info-management", "prompt": "开发商户信息管理子系统的详细内容。深入解释MchInfo实体类的字段含义和业务约束，如商户号、名称、联系人信息等。详细描述MchInfoController中提供的RESTful API接口，包括创建、查询、更新和删除商户的端点。阐述MchInfoService中的业务逻辑处理，如数据验证、唯一性检查和状态管理。提供实际代码示例展示如何通过API管理商户信息，并说明该模块如何与核心模块（core）和数据访问层（service）集成。解决常见问题，如商户信息更新冲突和数据一致性维护。", "parent_id": "cef51d4a-e713-4964-9d91-96bee7cec865", "order": 0, "progress_status": "completed", "dependent_files": "sys-merchant/src/main/java/com/unipay/mch/ctrl/merchant/MchInfoController.java,sys-merchant/src/main/java/com/unipay/mch/service/MchInfoService.java,core/src/main/java/com/unipay/core/entity/MchInfo.java,service/src/main/java/com/unipay/service/impl/MchInfoService.java", "keywords": "", "workspace_path": "", "gmt_create": "2025-09-22T20:37:23.4937138+08:00", "gmt_modified": "2025-09-22T21:27:09.4232676+08:00", "raw_data": "WikiEncrypted:3KjeyWIVLPDcIZ9hw3jQx0kGXw+8DVuWKBiHk7uFO4t5pZ7mdolMud+OfxNBrloKDTDsuJXdZH3ytN6vTG/zT8XgldkIAU3pnSYw5HS1yApWndYOh7jZLFb32Ns6zZl1hKvU6LRXuFSpLWGoioyIgnVVFcM9OuKX5bokYMrF9HXOON6p0OdUPSFtoTsvUMXiZA9yBO/4xxxHZ1AvHZ4GxyvMaNwMIEzqXO05N8LRMCarocA62iL0v6KwRn/IxNGkSr5GPmQyOva2fzxAxt7X60qZQdSftRCg404mMJFI4iATTHI+v/Ba40V92meOt9r6e/qdaOUG/Z/8qaTNQ1pnUNfnYGgaWuGKamvkBnrjBKeBS7morDhXFTOZaVzgawdatHH+qibgJQQP7JNZoIgcHBVRlO6P/uat1d9cbAjaYPdvnzqAtRpo8rZLNkH1ZHfuwLKgJL+XCAw5vRJoYNGDi0TRuNRxzetsnsEqGKqfaqpTAnePYZn+ePmpnX9SZL8Iz4kRrFBiZxDkRRSQFOspOThrI/0lfGsPFkVXqUFlJ1WUMzp0HLoSfC7FwRr5R26CYKx6kruWyEHi40SVGjaa2wQ/rPqxIZka5hylf91Ib/Xv9b/6TVusxcCVi5aFaQMT0Y+/i/r5+3dGLVxackOKsCX4MaQbFMnWtNRF8zSRUAyyz3zvbKJyDIGus8TXaqAWUcAZHt7jitTaRukCbla9ZU4bBasjAkHJwyIjLcIurWwUhmZcgTRNyuSMEP0L6isosWqRkYFrhk2H1swVswXf1U6ieThxXiNEuYd+Lx6UOiojDtkmfFGIy/X/axS3VLGHVy7Jwh8ZMV6o3jcRsHIvrnYmY8YdRHl9NEoaxhHBAkMkyK5UBGE+jrDXRUwzjkzIh/Hxtw1vB+rYRzYs52qrDyK2NJwZi6C19z6CI5hk04h57P0BhQPcxt4kVgQxjG6k3ixa3PP8iZ9q8ohy2WT3+/W55LsKo1VABsfG6mtsxZxEfz4KJ9TarljJDidwetXdLg7W1Uq2yjD7Lz9+WOWhwH+faYEWzGPy0o64OVk7QIok7BgA0z54fqZmtTxnJObe+FjCVLV2nDEzSxe3AL9wzQ0CfxLeoYURVB7hp8/KpzAijcZ82f1xHF+xhAWp22vZO9WwVI0hJvB5bDn3vw555MexSnlCYlflbGzn+IDUGoQYsW7C4AyxLy6uBdOydrpK7+aNpQfmPSfttckgcTcwF+xMBrXabjqolJDAedqYbb+zZaYMCJc+98tyG2jN/1ACljlOk6vSp/NUwkyZhW4n19S99JJdA2ScejHLIWtDAbzi1ZRiDavhu1H4J1LSCZGI7Lvdt6cowpmxGPPE+kcmzBshNoRlf4/DJUhs7rlaInn7w9IGqvmb6uUTI10atoX+0NusOoOYkeqCp6sYqPP3a8TH+kRjv453kCF9JhfkBhm3V9uUex//TE5escoOlkE4ChyZKQYffMNRVcCCyg3iAw==", "layer_level": 2}, {"id": "123bef40-0c8e-481a-8a40-24f973c3fe5a", "repo_id": "94f96537-e6cc-41d0-af2e-17841bee1253", "name": "代理商管理", "description": "agent-management", "prompt": "创建关于代理商管理功能的详细内容。阐述代理商信息的增删改查操作实现，包括代理商层级关系的维护、状态管理、认证信息处理。解释AgentInfoController中REST API的设计，参数验证机制，以及与AgentInfoService服务层的交互模式。详细说明代理商实体类AgentInfo的字段含义、数据约束和业务规则。提供创建新代理商、更新代理商信息、查询代理商列表的具体代码示例。描述权限控制机制如何确保代理商只能访问自身及下级数据。", "parent_id": "3fa8e6c1-d858-496d-8d38-efe8ff173028", "order": 0, "progress_status": "completed", "dependent_files": "sys-agent/src/main/java/com/unipay/agent/ctrl/agent/AgentInfoController.java,sys-agent/src/main/java/com/unipay/agent/service/AgentInfoService.java,core/src/main/java/com/unipay/core/entity/AgentInfo.java,service/src/main/java/com/unipay/service/impl/AgentInfoService.java", "keywords": "", "workspace_path": "", "gmt_create": "2025-09-22T20:37:43.2077416+08:00", "gmt_modified": "2025-09-22T21:28:08.7655993+08:00", "raw_data": "WikiEncrypted:gTiQk8pR90rzooKLiaXVue1IPGUCezl6C5TAgU+YA3IqJJ9iIdKar4xQPS+qoMTpeWIKDINZT55mkSKkqvl8w/uqnfbIdM1Lq5IIanKLtRirVwWJhRPYoTPrQ6ntQBTIp7HMw3yewoaDX9DyHzyYP/z9FZ9ZkXWFAxX+tJKnHnQ24JWmF6P6KJZrqUuoLusM4HdzQA59Tgwjk3rweqtXUMVi9m+TuwJk9MYdGetlzYlwGbLidQQjX9oj0+6HHsubcvSGzLJYHg8ia5eCVnlOa+Lw705xG32yoYQSUtuAPZfoSC1aBnF8sI7hc8W1LcGfUUtdBR3YQyBNlpLYvKoFCkKopzJsRPZC4qqUnJGWGKKCVVG76Y2FLZIknbbAIA1capNuCE5ORrPk7zyGz9RyjypzvUXI7OicAh0UDgpAQhfRI/EACqCJ8Zf+1rdEGDolPI2VacfY4/Yz7AGXXcgduVwSNW4EWsP3sektWUl4PwRKVKMsQiPBcwMzaPYyHsvoQGbFH6FrGRoRqBmXS22mcxy7rTkVscdwJeyD/UZACJFWuxNEbumQNQQdSLHm0hRHALAiEBOWmfz87L0IdKtjFuIkxYNfDAWFW8sPvYI/V38zqeE26pDaTiO8k0J9ebOEDkjmQ888f6qMlXo+GSOEFyq83oyLkOPwmgeJezDK3kYJymggnh2Omkh/3VXEey3zlShvk40uP07e2gGVkulVc389N76FHcltMPUunbK5o84ph9O9PAfOZkQQ2tTRGMa4I9c+oA6Akb1mlX6S2fEBAjiQYnb4vc7kQw6WTDjCTWn3mbehyYSEHsedq7UPcR0Bq5cKiIZ7BD7H+FsynjdiclmuRKINhdbdr5m02ppkkWeH0gRIWw7R7dR9DiFvwGivWUDNo9LNcCmxnHjXf2svmF7oqyz7SUxXp7ZqdaMMUG5Cod6jUHokOgzwYJkcGFmZt50lOSeFU/BsSaXV8/wT5WOpRGUJQYNRlNbP3JSTNRke8SNCDFvMzzH1Axw+HNv3tQnlqhD9jVhvSgn72yO8MFBKhkwoIaw+B4gJn6sbHxdq4bW5k1Pwwt7mRGNU7Khpw+4tXoaNdCh4E5SsEIO02raqJhxF517Vc7vVEG4JNZoThBTSl5js4b8uViJqKzg1cSlAj4pDMEspZSeG1W3ZTV83nJqssWFMAqjIQbYEbBiwk4q3kuFrjz0IZd8ey+8MGdlguLgQxHbbEjrMyNPWeQc4lEKGhphHmL5k6k2Irbm6mIlGJbb+4eWd4hTjK7BHvy/gTHEFzWgqUOCHvCaSgICKxWZ08Rt6ALOrEEDoQQZnoK85xtHVcTHvgQSU7PCExdPQWgNnM0WzYiGFmn2W4cYvDzumZUnXZaI6XMS99NYlT2PjSh1K5gpjECyM6SwUom+jBqxBQYI42zZbro9dkQ==", "layer_level": 2}, {"id": "59ab9ed0-1544-4013-93b7-2b546edb14c2", "repo_id": "94f96537-e6cc-41d0-af2e-17841bee1253", "name": "用户管理", "description": "operation-platform-user-management", "prompt": "创建关于运营平台用户管理的详细内容。详细说明SysUserController如何处理用户增删改查、角色分配和状态管理等RESTful API请求。解释SysUser实体类的字段含义和业务约束，以及SysUserRoleRela实体类如何实现用户与角色的多对多关系。提供用户创建、角色分配、权限查看等操作的API调用示例。说明服务层如何通过SysUserService和SysUserRoleRelaService实现业务逻辑，并与MyBatis Mapper进行数据持久化。为系统管理员提供用户管理的最佳实践和常见问题解决方案。", "parent_id": "b6853ca4-49b9-45e2-a077-b5608db8020f", "order": 0, "progress_status": "completed", "dependent_files": "sys-manager/src/main/java/com/unipay/mgr/ctrl/sysuser/SysUserController.java,core/src/main/java/com/unipay/core/entity/SysUser.java,core/src/main/java/com/unipay/core/entity/SysRole.java,core/src/main/java/com/unipay/core/entity/SysUserRoleRela.java,service/src/main/java/com/unipay/service/impl/SysUserService.java,service/src/main/java/com/unipay/service/impl/SysRoleService.java,service/src/main/java/com/unipay/service/impl/SysUserRoleRelaService.java", "keywords": "", "workspace_path": "", "gmt_create": "2025-09-22T20:37:43.4145042+08:00", "gmt_modified": "2025-09-22T21:28:55.1741584+08:00", "raw_data": "WikiEncrypted:WmYz76ZuIeWtQgFK+sghtb0LPGrRjms3BBQ/MCwOR4aYo085n/3jnRVKnnWH8h9XcGz4vKmAA3arM02FHUIjNcIUxsN+aEb62JFh/AM+psh202Lzk3Orbv06SqUufxpTgxNidP+0LDqRL0DWcK6DVbmmy32wpoEVdQ7npPb/1y+HhElFm7UqA7dtuF00dEaE6dFHbwc4B3CrcRS7sYaUMdkRVpIm2nKMk1LJVsQyCdnECce8ZhC2z2XbstgBzYbpKHUhgF7rBGQOodl9Th4Q1Z30/+mv67jZt1iSau8+sFQysIK+7Y5B0L9ufyzn2dLbzlo8XAmf+RPKF8bBnverneboKY8wLo/2oINo17kTZllNMyC/XfkcQ+lnQD75hYFDbJBXR37W3u5IJDAakmL4l8SXs5VPslE2JUi01wQzW8XtMgR2N2AxTjYq8pUgpTPUEKA9eNqIcE9Y0lCi8L6JZPSEufwVktJMz+uNbZjYyt+Mgs7X+zU6jiuPcz7K1gAU0CFJlJ6SBQfrrJ8IDI4Pkiy5OSc09ScB+fMWSS725h2VhztoMe0ckzgUIqTBVGpfbL7ddjhKF8zhAasXQ9PExWLZYkp7cof3TpZVrdyUfQAkk14QBQRgKzk2eBD9ZG0APpjtSGpqIex84DmGHxGpIqUwb7s9/SYa3tZB9zIjJ0HhBDRb3u4gGfX7CNCc96wMjlb/+lpGR8A9KqM60UCd63rioZVtaxhE5J2D9VqOVa6yJb0X0fwWxzDIZ6PjSRiHxVMI/iV/3DZoAs8UQk1bpzne2a/+dNbTqUCvLQ0TKH74sk5yCOOo1eGWi76R3WMbKpSxET/fS/8/2ivkOqF//ILSQC4vraDd4VsIOg51LXoq+S9ZArX3jeHEvQaiqcLRYCTsqDbmIb1/VeW8Hnh1o7S/pqD4r49RG9EV+hlokDxRz+0uF+2w4ciclr6ziX3smy7oddVmjG90egCaXFu4SWaVNxlarlBj6gz4ZounceoL/GWBWwYftGIr2qu1090t9JqDpKBx8haeJPeWi2C2i6r7LG0LlWVflYbBwRq1ljTt/UHbObRgv1T8HWCDXtMlF5H49aecFJJd8haI3JuxnwB8Rna0QuuBppZQgmtbIp+ApZFc1wQhQW0o8L7YcG+G+4C/mND7861tG77j3vUhQl3hkWzqIkPCO8Yu9XGhtH8C2xiDysQzBpXS9QkVs6lz+EVH94MDGO/IFffW+VatNaKOQWMVxbcAERna1ORgJgFuKNXzg/QS619LUk2/bAfusGrKvbzuEQQXKV7FfXuS3H1B+SToplDZ1sfASAwFe4RFtrbwo611JoMg/O5DROmT349twKg+yIIq+0y7m0hpPvzo/gDfUfN0DpmiChHHRJGw6NUf6CcCl9T2jjfIloAGaRMZva/SfTh52YZNV2UeaXL7ga/HnYqhUy8X6fHa4oLK1d9uhJfJoHR4YXc47J9Dp/MMZWegy0Q1UOVclzXhy0kjq/jZ23dASq8jIPI3V3JlR5I9SEDG2fBKQbHc+BWriVkCxIazl3I/ZNYnlcZsCp0wBw0eKxatEazY5toiKXlInHdwu9AEYt07LUK03PFtjP8RnfLuTbdlyCMysagDmxx/JwPqpurCdXbvvuoEur1NWrl8C93tabapDycZoDhVUuj+5xPMCdMivVNZb1fxuwnX6JyO26eIwt/WGhhP1NgELgQgIK7VCPMtkQiuXjmSlCG/e+t+3FHW/5noA+OicA==", "layer_level": 2}, {"id": "1788d30f-d310-4d22-afa6-eb221da78333", "repo_id": "94f96537-e6cc-41d0-af2e-17841bee1253", "name": "统一下单", "description": "unified-order", "prompt": "创建关于统一下单功能的详细内容。深入讲解UnifiedOrderController的请求处理流程，包括参数校验、商户身份认证、应用配置加载等前置操作。详细描述PayOrderProcessService如何根据商户配置选择合适的支付渠道（IPaymentService实现类）并执行支付逻辑。解释UnifiedOrderRQ请求对象和UnifiedOrderRS响应对象的字段含义及使用方式。提供实际代码示例展示不同支付方式（如支付宝、微信）的下单流程差异。说明订单创建后的状态管理、超时机制以及异常处理策略。", "parent_id": "97769763-5625-4e00-a0ed-ae7298840dd1", "order": 0, "progress_status": "completed", "dependent_files": "sys-payment/src/main/java/com/unipay/pay/ctrl/payorder/UnifiedOrderController.java,sys-payment/src/main/java/com/unipay/pay/service/PayOrderProcessService.java,sys-payment/src/main/java/com/unipay/pay/rqrs/payorder/UnifiedOrderRQ.java,sys-payment/src/main/java/com/unipay/pay/rqrs/payorder/UnifiedOrderRS.java", "keywords": "", "workspace_path": "", "gmt_create": "2025-09-22T20:38:04.4170185+08:00", "gmt_modified": "2025-09-22T21:56:55.4147194+08:00", "raw_data": "WikiEncrypted:iaIcfwdLscso9uJYzNYkB5LrOYrNt8WrSBB1+JXzvUHWbiVHQLQCDuhHL/mIZh3KgpblsmhhARAlAEuBEzlE4FJ0PCjKfmmHxj8TT5dl2+6xwP3pJgWea/H7N8mk+uvn+uldAJ7v67VFVS7gjBGtUqQpRm+aNGcy1U0KRlxNTealC1onHBByTviPbJ6MNg9x8tFp7RmSlwhEUyQ8Y3RlcDTgcnqd5kLSX8LUEcuw+A2B7FBZwDK8ctLzCcD+D0nQZX5XB4TOP5zGBxZStR37K0P56qSloqkD/9kUwqw1raZi/ENgv9uDRMbEdzIiGx5yNJsEtFcvF9bHqjwcfnhRAzVK/DKgnVkAuzLMx5Cohzuduk6TFSeqA6Sfj7ZR66pOwhc2ucPwksuB7UlljgXUT5Sc4p3oONrF7MlPPWrozbN5B2VwdEtrYHbWVxJtDxb+ivNOx6H77iTGfCYH8gbODIg26QxNW1LxNhrrYqtaJXIJ1McBD+SG5aJL7ME7xIP26XgYpQhwmHD4Ugen00EJm+TNHqrCEpZzNJsC7YjiQAPeN4Hie9+7Gn0161gWIV212nNr/jwqDxeXFkqdYilc8MvY1LkV3PHUWwJHUP8K3z+6NoCrEvFbx2LsmZqzq13ZkMsGVSS5y0btL9ZIiNLf2AbEr1hWYoFDk7Fm9dHWYHh+T+NlY2I7CkZYiJnrCRvzR+bTUZrX0kdiHv0IOhM4a4MR6nYedfdImtiSxE0l3muHg4j0dBZOpLbI6u+LfM569/DwjREooy2f0/mLi6eyOLab/tBc6XJHQ5E3/hjLKcQtVRkee+6laZM1BHx2Z/k2yK2XJE0XzYQTSfnuX9dA+KOAO7UyOW6Qi/sbfVz+w93l46Ya8C/EF2QJ6+IsDGH4ZxdYLnnEmLfB9y0n2eTD34aDwDGZElS9JYF7gpPMnX/gmjfOo3eTnnqfUihkGQljown3ZhkxRgwU5OBzRaoPxrJpWifRDPAfvOVj75bKmEAQ9+MaYB+GIqNJYAMWcjgk1pZ+MafSuKBT2ekGmwxCi+q25mZZSHj32kJtrrpHBLQx4C6QXDDb9Qq8iKfakwKBgFjwlsWMBv1prx5V34FR6+iPUfO8MUzmF0XuCM8Es5vo2deICoLbAJqf5eGR/bAkQ2HpUOB3LRMBAyFSD39Y8BFnwHjuyiINZUre6Cfq4fnhQCqkBmgUKK3BF5q1DldlTRIRkIu+tnt1d4dNd9pNIDHbr0MvQm+pHNBxZDjN6LgBkeWe8U0owzxyhCXiYxynBj9t68JfSQCeIExNI92wuDzMOJYl1HNmSgh36mcg8mvgJuuC3oleP1KxnuGgoxlb8J1WRz1V4TWOoJaDPNDlWsTqO0NDO8dFSvMVFnaOVNRYBWnmn+Bjc+9GUUucPcbXkMdxpl5n4nQ2E+U8P2j4W5bVyqJsoavln0NokV3HXzI01p4q3+WLyAA2atcERElT12RDDFowy5BGYtaGwBM+ZqEy9i4dZSv74o7wnnXB9pI=", "layer_level": 3}, {"id": "5fd729f5-c512-4b2c-b6d1-bfc8d609f364", "repo_id": "94f96537-e6cc-41d0-af2e-17841bee1253", "name": "统一下单API", "description": "unified-order-api", "prompt": "创建统一下单API的详细文档，重点描述POST /api/pay/unifiedOrder接口。文档化HTTP方法、URL路径、请求头（包含认证token）、请求体（JSON Schema，基于UnifiedOrderRQ）和响应体（基于UnifiedOrderRS）的结构。提供完整的请求/响应示例，涵盖不同支付方式（如支付宝、微信、银联）。详细说明必填和可选参数、签名生成算法、异步通知机制。解释订单状态流转和超时处理策略。提供错误码列表及其解决方案，并给出Java和Python的客户端调用示例。", "parent_id": "20390a93-7ada-4338-9006-b06206c31398", "order": 0, "progress_status": "completed", "dependent_files": "sys-payment/src/main/java/com/unipay/pay/ctrl/payorder/UnifiedOrderController.java,sys-payment/src/main/java/com/unipay/pay/rqrs/payorder/UnifiedOrderRQ.java,sys-payment/src/main/java/com/unipay/pay/rqrs/payorder/UnifiedOrderRS.java,sys-payment/src/main/resources/markdown/doc/api1.md", "keywords": "", "workspace_path": "", "gmt_create": "2025-09-22T20:38:07.929029+08:00", "gmt_modified": "2025-09-22T21:31:22.1666429+08:00", "raw_data": "WikiEncrypted:iaIcfwdLscso9uJYzNYkB4Cv1lqsXHhWdRwbIkNSCAM4U1ZUmW0fp/X9oGlEz1e4L4gQ9Z2jX2rn06fFU03UtYTTMTCjfAv6T34lpfxvRY0dfdzRMWgU1qaX6iv6JjnNBimMJAyz+QCu2bOjmm5p9AJAXf+TLvvEXNB9uYFIBgXRYBDnnuT8AtUj6A8uSTwWKEhTf7zO5og/VJjrTkOIx3liWLH2LoZz2QV5+yykryrQKhrj27Ea/xeQJP0/LGOWf7HDCxeGLjjdCOzyCVP9U9jhBjwOfu6p6nbh6Fdc7mxcVcfNqU2jRJRBAgpoj3WENvq931JSsz8Oa9XUhMkpHEp8HcE1CAZCNQ0642NJmPc8S7GWEkLgte7/LSoURx+/JV9xicfaIlZjNlotKI5qVIOyv93seNL6KXnX8hkwlO1OYEhArgNv+iQHx45RlCQzf4/RhrKUWLJgAZ8bplRCUFJ2YDFxS8TQktfo48ASEUl0Zxfk+hc0CWjHe7DUVw2iEKEchqnx2YEiJle8Ou307c59k3AEaapo0OETurrjTUAD0c38gkmbac8WlbFHCrCyVMJ9DdLZVrq77870twj8UzlUTQnKkBYXZHMev0knn6Gz9jt12pg9HcKfqqludiU1YvJcZ2dfrkHEJR/+qadZr07Q3SzeDRBIZJT59A3KkmoBv9181Syvpp0e4CXFOMhzvrIB5J1wr0O7GU66xyCM0MVAepHrPPCFKX944DruAD/q4sIWpT5rj8TcELB247I03nwLlZoC1oq+Rr9FWlOxfGBHquZl2opODQeMol38zhE37WQu9xx3C9q5sLDaBCrE6LwK90bFIDuC71swJ374MiVOTc14sgosWOLXCaWGKk25SNS1zDOpZSb2TOaefrLnXOpgXxuPQ7OfuzuVKVajEh2Febdcyr9NGsAw4/1WPFW3skGMld9y6mpaI1un/Z80uBEu4th7b0sRXQ5cYc/xGDLI9xVasFggpexqvMsRlDr4r2b7ecIb9X1iZ/1cRKRO9ApZnliO7z5Zn5hzc5vw3qXHT175O3F0JDINx6w7wDZIBC3nehzHz052haL5llNWmUncmc/z94UGlFyq6BtdCp26k0Gu8YUvixAmTXa1w+PThHd4VB9CSf2Nxt+73owsIjCLMLYeQXXGueNORrneui84k1lGG4kWUBmClTQU4GpcAkdkaVdyNNJqXvUwNWv/Kz2Q4Gi2v6/3ADIeQCYLp9RkiXuVk+MjnfQ6LY7vKXzmqLtIAN/j0N5C/F1g+22MvYBu+YGanEbYHIarF7aCkY5XWNSKvKgnBquBoYLjLe1rNTUhxhyPa2K9qAryKPtPcFosNVaD35rZ7Chr7EVxUn9YEBV3YhwyKFIWO1dtYAUHLWJuv2XN0+12ocagwnm22Lm5k9maw1yuZyWgLThyTOM8zfx0o/d9sKkH17sXn3g=", "layer_level": 2}, {"id": "b4d0b4e3-8e9c-4717-8d39-d38cbf15d623", "repo_id": "94f96537-e6cc-41d0-af2e-17841bee1253", "name": "退款申请", "description": "refund-application", "prompt": "开发关于退款申请的详细内容。详细阐述从RefundOrderController接收退款请求的流程，包括参数校验、权限验证和业务规则检查（如退款金额不能超过原订单金额）。解释RefundOrderProcessService如何创建退款订单（RefundOrder）并调用相应支付渠道的IRefundService实现发起退款。描述部分退款和全额退款的处理逻辑，以及退款单与原始支付订单的关联机制。提供API接口定义、请求示例和常见错误码说明。", "parent_id": "918d1aa5-5ab9-4cf1-baee-3cb7f20be143", "order": 0, "progress_status": "completed", "dependent_files": "sys-payment/src/main/java/com/unipay/pay/ctrl/refund/RefundOrderController.java,sys-payment/src/main/java/com/unipay/pay/service/RefundOrderProcessService.java,core/src/main/java/com/unipay/core/entity/RefundOrder.java", "keywords": "", "workspace_path": "", "gmt_create": "2025-09-22T20:38:17.3263822+08:00", "gmt_modified": "2025-09-22T21:58:12.7279968+08:00", "raw_data": "WikiEncrypted:IsXrw8CHTmYocng0pZ32k0t/YfGpsERRx2PmGJZMTvWncHgHXtxegvOAzWhz84tMJhmPo7bK/HS++f55GSY6+8IytHW0nf9YwErcp2ZmY96FtOQq0HgitJJPCWraSxp76EMKthLqVxBDLYgnibpMHOuWrm3pz1So4O2Ns8AsmrjxqDFEqW8LkuZ4p5L6dKuKSabw6M2odKUHUlxBtVB/d32lFUmyzClppgQDGffkT0eJUXljkKti+IcayYe0l5K5mEFrRF56x8tL8LOnlEG4FcHM+v64jtdN77UbWLo5AzXRmRuDEaQDUvORxd04aOSDV8SlBD6mvsQTTaewyTQU3gxtGJU6I3Tj7DEji/ewMrhnTVhgRkPi9p8UQNqsx195vtd2cjVYyDKO+RyLkgbYUoSTsaPVyDPtwABMG08z4SXrqhJvffsiHc/aM7WIaCoR5+stojXRrlDdVHIqDJDJxmHXCgzv6yJEuDnea77rRXqrYIiKZ68hVshl0iBXiT0cJDedl0k3Q/QD+T61WsxLSiFGiYecDZsZQNpC1jdm3/7yAF7VyvukF/1yyGhOP34GJPUcnassCZl7vBBAgcVcawwrWInPi7UzjwMe73Eg1uiTuZDtGcUq7jA1f2z+nrEBX6a4rnMV+TY5ssTKkbPWmwaYb+ZQ7qSGXXXB4StzT18my3yyXIhb5cj46jV4htHEg9WRmFU+nqg0u7A1/wiCHpqqWwYr5ACmamgNI3olidJ1w8OE1OCkv49lvXtkxhknvFqlgnXcubTAZksbkn6yH0vnfPM3QVP81V3ShePc3lMVet2P6fytR/qburnTts945MJPQ7CPLwSUjY8DqFPPviYqIRlx6WXX43ZX8VEpmFABuyrrHCTPSPPoKrUgCDJ2lN+3I9rwsRvKi0uHiMhpnQ0EggQ6BpDz2Oj6LaMkh2jUGA5ta4CPfEFpZFBb1MpR1LxbOQprlalANoXs5n0w0b9hEI3v2LuKktPE6cc/C7YNtfVBizevBK+YnqyVCc15WXtLRbuOJBtWZOGt4zTYXHBnK3ReyPWPMtLRwKuknoU3IAVyfkF3PiQH/nVBldS8yYRFTek7UneUEbz13ARICf1CZMbWu3Xgb+YuVCW3zhHHTHL09rQH1b4fWU33SmGWTXpGrVtjHsRh5k0wdqOQZaCcIJrJBAXaFvVnlxLCm3i5ioSwlcKjaudvcMVOjk+W7aNaYv0UWYqwuq9uAK0qlPcwt7cn/mGffzHDvNtB28L9C0lBC0pTq9ULOWD9GegO", "layer_level": 3}, {"id": "4ef29ac6-84b7-4031-a953-aec5213f6db6", "repo_id": "94f96537-e6cc-41d0-af2e-17841bee1253", "name": "转账下单", "description": "transfer-order-create", "prompt": "创建关于转账下单功能的详细内容。详细说明TransferOrderController中处理转账请求的实现逻辑，包括参数校验、订单创建、签名验证等流程。解释TransferOrder实体类的关键字段及其业务含义，如转账金额、收款方信息、渠道类型等。结合代码示例展示如何调用统一下单接口发起转账请求，并说明返回结果的结构和处理方式。对比支付下单流程的异同点，强调资金安全校验的特殊要求。", "parent_id": "52c46886-7235-4b60-a9bc-07e0d54bc939", "order": 0, "progress_status": "completed", "dependent_files": "sys-payment/src/main/java/com/unipay/pay/ctrl/transfer/TransferOrderController.java,sys-payment/src/main/java/com/unipay/pay/service/PayOrderProcessService.java,core/src/main/java/com/unipay/core/entity/TransferOrder.java", "keywords": "", "workspace_path": "", "gmt_create": "2025-09-22T20:38:25.4278834+08:00", "gmt_modified": "2025-09-22T21:59:44.7493002+08:00", "raw_data": "WikiEncrypted:5TgAtbdUXqbKL9lWGANcdUdHlsc+eOJjF0zR24OuscD+0pdTb2at1pV6FfUBs1pfKBLJKj8eDEmd8oqI5idkLmMKP+SRGbU4xbMDVB3YSSFR6Soz/oOIBVA052q1Bw+bIn3roLqKcns4dhaoQsKZPtTT4971KrQ8Km9UI8qqZPKEJYsGfz50bc3SrqAc+uKRYYtC4mi3jGqaR0k05hWBXrNxSqlrcRdWbkiDdVscK98SbEexI1fZshs2p+upqaWtBMPNBQrVO0rI88hCXO5qfAf9PTU+yqtbStK2wn/pyC4JSjeEkiXxljMLHqGAavMJhaxxk4trVOrkD2k97+Pi5bnNOaxtQ4lwST3kJVQWZtftN62w7e1yv6oyl8ZRjIQ6t/A57gk0NKUgwyQx9DRabMujlI/iIrid/dqFn23rfmrMC5L8+f0nJEhSNKTUBEDGV1kxI7jBe/5xFoUi9Maq5zQzkay6PFUxme/L65bAeexSizZYbqT6Iq8LQ11lzghDZwBjhdkw+FGVbED3YKxRFMXbIKwvpRRtDdbMitDAnOrjOiuiiwfOsuDEFBALWsA7Ov6IUjATWjQpy8Sv8tX0gNDkHxGnYZCFYTcQ3vVysgXH2rUYLJefxgKM5UD089n2oxzVGGZwDWT+dz5Vemj8sAqjPR5UkZHtoCdyQgiOL8qL/3O+X+49Oj8bSZEOp+Onk3PI5Y+mTM9YmRzJxcKfKuHNV1ZQlLAO4YBN7AOjjqYtezhFlZJFKvbuUGR2kdsquiCmzTGj0DHRUfHRl/Yd/l5lrwtNY9UAsQjP0WOEcaHKXRa4bs4vIY1FQHW1JwIVd9v5gXUPwW5ueYSRXsMqinNwzs8gghJRxB3+bMaE8DreMfwWdcHXmbJ7ZvYHKRLGXFmpvj1Kqb2EDLs0OdgMDhVCMl/oLTtPH0RmN+EBSylSfOiaQKqGK6t+wASYe+9Q2WaYRhWC4O2dFBlJkcqehDrvI9MN+mwej0OzTOo5DfdD7Bts0LqLmglks6Ktm/n5I8AMZN5EzF8YAWDkG1L/knrheRz4eMJdSGkIwoCMZGg8p1VzDwJTlHGuW3RToVhaZsjmO6APQlhGLBrbyUoPZBjjgebBLjCB/zMh91XWledXwYqrtSk61wlZYqozzHl+AHmLKZEEarQr36UneQohUqdvCJwgX2xKXtVyWd+ILTwU9XmxWEHY/kKWYxCEz0yFm8shOGfrNq0lgNQomQSouqnIm/6QiY6BLRoZJk4afMdBRXC/szV5mV74L13fBi0C", "layer_level": 3}, {"id": "44518afc-6469-4e7d-8802-8fced7e8d8f8", "repo_id": "94f96537-e6cc-41d0-af2e-17841bee1253", "name": "分账执行机制", "description": "division-execution", "prompt": "开发关于分账执行机制的详细内容。详细说明PayOrderDivisionExecController如何接收分账请求并调用PayOrderDivisionProcessService进行处理。阐述PayOrderDivisionProcessService如何根据商户配置的分账规则（MchDivisionReceiverGroup）生成分账明细，并通过策略模式调用不同支付渠道实现的IDivisionService接口（如AlipayDivisionService、WxpayDivisionService）执行实际分账操作。解释分账请求的参数构造、签名验证、异常处理机制，以及如何通过事务保证本地状态更新与分账请求发送的一致性。", "parent_id": "b421b6f6-0693-49b7-9034-6061411d55c4", "order": 0, "progress_status": "completed", "dependent_files": "sys-payment/src/main/java/com/unipay/pay/ctrl/division/PayOrderDivisionExecController.java,sys-payment/src/main/java/com/unipay/pay/service/PayOrderDivisionProcessService.java,sys-payment/src/main/java/com/unipay/pay/channel/IDivisionService.java", "keywords": "", "workspace_path": "", "gmt_create": "2025-09-22T20:38:34.4884701+08:00", "gmt_modified": "2025-09-22T22:00:55.9884528+08:00", "raw_data": "WikiEncrypted:TfUUykgSV0NZhhl6ftJ5NIMXuWAfRfq6NxHyuja9qH37DLtyfNY9QD07uZXvj5EE2M0fLrCyzFGkD13BsUpGKP+4VDBTbe4vKJ3Zwk7CS9aZK7JdDiyVkkfdwQn3xFOLY5LAkYyKyM0/m2E9YfcxBvAbre2xCZNG3Y8We20dAxTiQEFn5DYDhmSK2/8sa15aU2AicukwBA2ComQ0kBA1rHgLW+yhgPlahK//LLNIR7G9e5KNLhp58zk4MNboPnU4iV/thWl8jNAY/P0BuQMFOK4ldN8XLtHBTJUG0rUqnJ72rlBsEH7DWRHqlArjEY5XNjlci8GGwm+BxZHjjffjRKZVD1BYEzk8VMl0A7Dt0LRtKYIX5sAMlTAEkBPUA6NZ47Dxoato5RR/iry/EAM9HEmkpiUZpb0saoGIHQ2QqK4qMyrcA2K3miZIWwoxySmqWR66D8VZfL32AH2fSfgifeicwEe8Mf9noln4J+D6m9/Ys4MZg1fG3LqXjFFk7DSlowXcjYeesnPFvrZOVbpObwLnEXuDkrYPAEn/64/fAw/sFwq6nfM2+gJvDJzReqWc5WQ0PakDkW4mB6EbKZR73WJIGTtuyM3loYg0yBRL0eg1AjxSmznBkdg6H555+E7N6rQXNHDp86wb9hbXIcLIL6Wv29gkpU8pKBRp9+lnyHzi+C5dCsaCpLZ+CQN1g4NIAtDJ61bHVUBHSXgsXCag7zlc1y8PL4t3V6QRyp8b1pd8uTKe6VbIZc2LhrIXufv84AwuLNJbKbDCTfWMvSI+iQWYgfTXDLrMYgJ/8kcZroMHwq4DWiKgFIrJ+ol83ZPK6rD4IQyCZeG5p2QkvIRlYW96Y1ErYwTkOq78QCL+VazCIZCYojPjOk8q/+55ZPCBY75aMw9JcFgtaLjZvVqfElI8KgnzrOf0ENt2wN5+ga3/pG1UOZmFcjat8+7/EIMRqFtUBld4VVtsNaOMxCCydvEo5r2I+s+Jvu0ce0b8GL+gVTPvebFOMjVlx6VFEIQV7T1CJKcyVdTSCZAKZlw31bDyGv/WLxv6TJVdDhVUPa43OeQ8803zZZBEVcqawjNwlBBNj3PXgZ2IekXqXtO01tVNdVbwzleM6qgctn0RbacPbe67JS4IQlxwC6acBIj4bIlcaCRFMoDLkyQsDqsOuzgSyVA+BJHRPW4l2Dgp4J7MoI67GLaBrbYF/AGfRoOq9BUUtd89pV1eh8DiQuA3VOSzLgi8kJzQaWCfT6BafJBULJ8AFsiCjniR8Ury6W2PhfFiKi1WbRwYqvFXMRYgBmhwqFvi8kQiCr7wZadQiXRrQsYXJJK0TjS+zLZlAyNw76Z/hYn9tWhDOB2uOlb/o0rUjHpYhijSOKX7izwd2BCQp1ozjPgaMzkf/xtPW1QsiRvY7sbFIuYQCK53Q/GViSSHRVmKrio+8/g7pwSWWsQRiT5m8ZcCS7GhfWQBwGDtThBi5pT85+TkAdgXiSErcVHm59G/eQVRK9AWMbME3MQMKbCMJhMSqVzpQRBc+X7F", "layer_level": 3}, {"id": "cb06d92a-8459-49d1-9a11-0e87325df449", "repo_id": "94f96537-e6cc-41d0-af2e-17841bee1253", "name": "分润算法", "description": "profit-algorithm", "prompt": "开发关于分润算法的详细内容。深入解释分润计算的核心逻辑，包括如何根据交易金额和预设的分润比例计算各级代理商的分润金额。详细说明多级代理体系下的分润分配策略，如何处理不同层级代理商的分润比例叠加。阐述金额精度处理机制，确保计算结果的准确性。分析幂等性设计，防止同一笔交易被重复计算分润。提供算法实现的代码示例，展示关键计算步骤和边界条件处理。", "parent_id": "574d46ea-ffd8-4b8e-a21f-24c0d9111838", "order": 0, "progress_status": "completed", "dependent_files": "sys-agent/src/main/java/com/unipay/agent/service/AgentInfoService.java,service/src/main/java/com/unipay/service/impl/AgentProfitRecordService.java", "keywords": "", "workspace_path": "", "gmt_create": "2025-09-22T20:38:36.2672132+08:00", "gmt_modified": "2025-09-22T22:01:26.3172318+08:00", "raw_data": "WikiEncrypted:3KJFs/+fakBIzq/vLbWLrc9b7d1h93BzrhwmkcSG+Er8nCa4MW6yHTRhAgHkZRLb9F6hgTITFzpkokjGVdVsqt8Gb5XRy9o1xyx2pfpzCyMqWOIG2wLcB1PIjGSbYX3uBhwW6S4D8oCvuPAjXvNLZagJ7iEVPOsiqg5pEFy8b1a+k7p5TL78bmwg0Kwd6sKRhA+xzrCPeY+jrr+jhOP5kA6jTLyS7JyCLa3DDL2lLBXTDQ9gsluua48oun2NpBhow+hqtXj3W2ECNbIa50tRrYRxLYvDMqr+qcOgEPyrAGQxRWojC5WmGyVZYdQDzSIvAf06bXD4BeyF87ndwaQjgTi5pGb5Lv2w+wSueZu5yCasiFgCTUXYbfqoq/nSM9W58eIHiqJUxsi9AbDOOqjaGzB3bm63FG7nHiGcmvMzRHbzgzsayxQq+gLkGe5K+NrZl7kJMt6/eFUfK4aMgSyIDyLIRUJAkEUR54k1oCAN/Ro28dh/wA2e78WxabMD7ipRPi/eqoMgYQW4T+0uK3jN5eDCOgM9f9wbbVVZRU00zpYFEBeJIpwlEZnL5xTAdpsNoyy6kZZov76rISttxRv4eo/Upka7fl1s3bcxac/u1ufrTJ880yp2wDoMeXWOS12mFdcjPpc7nXrrcynOMrcWOHivfMU1ZNDVSYvV8lzUFppOtDZdVuDXemxf98cUenO+GRByn0DziG8hUFJyBJI3Wb/FfXZr8hl/HgJeszf9pK1+HYHucU4F4Jget8cM3iDLeVzolOBkX9R+inQnfWcKlWhFCtqymBNtFjoKlk2mAIplgOtKWs+BuBQFOeDce6kxBAec/zwIZPAYu9Tlxg/+s9Z9PkeBCGrHxNsyJA1up+rBOob69OERrb4b/mHAwvvqAAcNoIWpAYmF7H7alJENjgUJiEmV4pjshk1/O/hzwOmgxuqUBMyNq5Mgn2gkrunOuCdv3zaMF5PPop1uWutI7uVmI2RkdXxoG+TDloEZ4MOe7cSnhV7Psqp3zWSQt9Vu58MoxTYNiIRxlkDVdmRgXif05JhUyU7LJdxYkyL6Hw6mW121JW10YDzpCO3lMidTWfXPLwQ0v4azrNx5LlpJdksqe6vEjfEE0LOvT5yJfqtu4+dhisFW7qigkN3/GoPj6LftwJTl8OvVjRQIRW4mX0RC4vm9XE7b/4ZrfLqtSYQ=", "layer_level": 3}, {"id": "360a1aa7-38c9-47ab-841a-1814cd3ae0a5", "repo_id": "94f96537-e6cc-41d0-af2e-17841bee1253", "name": "商户信息管理", "description": "mch-info-management", "prompt": "创建关于代理商系统中商户信息管理功能的详细内容。详细阐述MchInfo实体类的字段定义及其业务含义，包括商户基本信息、联系人信息、结算信息等。解释MchInfoController中提供的RESTful API接口，如商户创建、查询、更新和状态变更操作的实现逻辑。说明代理商如何通过该功能管理其下级商户的生命周期，包括商户审核、启用/禁用等操作。提供商户信息变更时的数据校验规则和异常处理机制。描述服务层MchInfoService中的业务逻辑处理，包括与数据库的交互和事务管理。结合实际代码示例，展示代理商批量导入商户信息的实现方式和性能优化策略。", "parent_id": "67d56238-1329-4c27-96f7-320b6ff44c7f", "order": 0, "progress_status": "completed", "dependent_files": "sys-agent/src/main/java/com/unipay/agent/ctrl/merchant/MchInfoController.java,service/src/main/java/com/unipay/service/impl/MchInfoService.java,core/src/main/java/com/unipay/core/entity/MchInfo.java", "keywords": "", "workspace_path": "", "gmt_create": "2025-09-22T20:38:50.540371+08:00", "gmt_modified": "2025-09-22T22:04:04.2140556+08:00", "raw_data": "WikiEncrypted:NfLkXpQup+3yS0k8I+Y3isbLs/PIRZhTdvtWA/HpHjm+J3GOz08aO8VChR3i7ztDh5Pq2QOIJgAOmGieA7RWTiorp7VrEK/3G6XcmfdDf4OoEezTJ8f79xkdUAM4sVQ/t2SNiXVkcje/4t4sxGvXg1iDF5J19Vyn2DBfAoPf+C6EDyJ3j1xMwvF2Pb4RJFyy7SFvEoi5tS75QLNPQysTCZsTpfi2vte42lrewqRGlqky5daAKxbZJ0aPUKjJ7OOVa88Eec0ZPGls9/05n1jw2v6dGuLCnuT1qXiN6dWyZkW0dDH0/zGxQW3u7Ct5cfECdXXMQcXmmAUaAc2z5UaBHDH6Wc/furBUqfm/DQSH+KMyeeqmYMHHm/ENDBHVARlxZTJ4DixtXE8NgZqo5DLUAzS+d0/z022P+pfPyMyjOPiMTEDsFyVuOC8x4UEvrLHQHekoYKr6LrRRXniLU+MgjdNz9quNlZ2m9NXdOZwKDAqoLx85xfbdE2Qf9swGYfZiNZDdxJXqL91JNOCgOG1ibJp+Y2EhNpzY7ugMdjo7v6iouLjP1uvYRaIRldnQESYvIi4B2i1M62UQQwxh/iEDxFMfUMFbfCOQhhaES31HqwqbShgKfupoiVE8yC4rVQg5Ll9vK4K5Atyll5Ai3LwSpU3p6ZNwbmJ0ApFRkND4Nw4SJNWIY/G4u+OeOjQR3RqYduriQgyujPEFWYqipstP2z5rN7Y2zAjogbfqtlLnmcOrzBhlmlT/GOcfF0O6j5CHvjR2Ib/U+mf1RFDgJQ4UzTuBjDCcXbaNDw6AK0M2uhTBnq4RnraT+ghygWjJDc4HZKlrBsWObOobvr2Vm/dItG5Zi0uaOF8Lm5nqmbwx8NuUC2aLojxVO/r2MTZEur/t9eY2yYa71EyB3MfpA/mGEZWcUxVSCW+x+3IGLD+rr9g1nqeDeGsK6gsBMBU/AQ7ak204N8bpMmKqHlLXPq+OhOuNxiex+vYf0N5WeimASVmo917vnCJ8eziPbuNtqqsf4RXS47zmY57nsdpeZYW0+qFfygF12mtj8vzFM/ZkGCJ7VA3iDIeKyqZHPJQnJfVMN1ZcsoqKCPoiVWRwBm6D6oaW9zEC2PN3XxXzuHtgRlfPs9EyxHvRjF/uutk35/vMn7HYTWZ0edJ47qUdexXlU5Fc3rDD3ZGO/TQU5flBxRatpV/bAGUjkwJI7IzTC60EfRp7HZWY6TFPiZiGiIAvz7dv5vwQloibgsRJRGsolj5JB1D3PNt2SeQlxOT5Qp2mOq8uDE9ow5wPv5OUBdTVlTSde5SOYzyaCZBRNhja2VSX9C8VnYwAwoqawdf7y/vdW0UfACOqFuHc1RzwOzom7dt7ZprYU59VqVgTiUkDLqmgVOo7Rgeq4OL9zF+2xZfvLq5gqhJXFxGcGS+A84fWaUQXMPXaqJP5lK+eDzfGmp1YRKDaDogVa6CzECzHfxGui0TPjljT6aoROujN+X+ftBG7rv9ZuffPCM+e4fXVQuUOth1rudN2SLaOPPoZZU7m3rrjW58g0TB62QGs3w5loaJQlKu5ph/qVKNaWDLo70W5oFMC9Dg0UmbIXTNKlrMv", "layer_level": 3}, {"id": "87fe95c0-2775-48bd-9a55-0ad8ff4aef31", "repo_id": "94f96537-e6cc-41d0-af2e-17841bee1253", "name": "商户登录认证缓存清理", "description": "clean-mch-login-auth-cache", "prompt": "创建关于商户登录认证缓存清理机制的详细内容。详细解释CleanMchLoginAuthCacheMQ消息的产生场景和业务意义，说明CleanMchLoginAuthCacheMQReceiver如何监听并处理该消息。阐述消息处理流程中的关键步骤，包括消息反序列化、缓存键的构建、Redis缓存的删除操作等。描述异常处理机制，如消息消费失败时的重试策略。结合代码示例说明如何通过消息队列实现分布式系统间的缓存同步，确保在多节点部署环境下缓存数据的一致性。", "parent_id": "fd539ec1-f638-4212-9ac3-c62dd26de122", "order": 0, "progress_status": "completed", "dependent_files": "sys-agent/src/main/java/com/unipay/agent/mq/CleanMchLoginAuthCacheMQReceiver.java,components/components-mq/src/main/java/com/unipay/components/mq/model/CleanMchLoginAuthCacheMQ.java", "keywords": "", "workspace_path": "", "gmt_create": "2025-09-22T20:38:56.4353652+08:00", "gmt_modified": "2025-09-22T22:03:29.3885442+08:00", "raw_data": "WikiEncrypted:IAPZ2Fm1xOG5S+OxniHLx6+EHaNlCiIdQYlKP3ZVYbaPKyIOmUu6jAae8U/ZP2m2VZ9CPwsxWsVqNUUpyZqQjK6ufRg1otB01sGevhTBJCMGTaLBfX1FJoT7ImEzp3KtAPKnbXlV5B5HD+gztPHEJpW/owmskCX86vK2PIvsFG0SYIh63VIvAdIG8AZ4CCjUP5/wg8yw1c+P8ut6plF1eZFdVyqvC3pI3BEemzFnXD2ZNgL0I/Wi48BcHJlGKvNOjFLi+MWQ4+Ef7V30PqstseGCkCv0ifziNN2cBbqWd/78sGFdQU5lBLu4YX67MWap/rX8PHnQ009MjRj05IpCk45TKZsRMlL1xmLvqvKd0Et48XKxZnvzbY9+qc2PP4XO2ZWO7sf21fAeX2GCBvzG+Dw2OgjBkymvz1RsypKGh0c4tU+XiwIKEyjiAQsklpbRNBjl4SW3fXmtZ/7v9qi9LhcBfQe2d02RqX1LZtEDXzmhUd5ziaBrvf4HEkleL5bMCYkSVz4TcVX2dwirCbMA8/Nd0lB3Sh2kGSud1Z7Jvnz6MkM7+s6ZaDwDawoH6snVDd+19qaBb02yuvgB3WZLbSFnJkDX034avGjx+/VcvWxcvFlBti/Is8DLMwFF0NFpbI/DA5znQ6fhIUD/G+OAeGINdvjT/mpIjWxQZpdPiABAoIvZdYg0hoosOh50wgmaWljMNREYTEmewWFzewrWW0UQ+0IfK7Uq0GVOqbkvX32ptTW0LW3300U1y4vnbXMeC6YcsSZsr8+J+YYKpGl+51igerwLqxOfOF3cpFsMCFzaKRcrOpoBaO8RuFQueRvZTeXrx8KHVeZWWRerh/KlpKbt/67yn+9TRrV0TQyi4Y1H5u09CjVQo5Zv8bT5nKnNiPgqTk91zB+mew8b6c85NkD+4hWcg1k/QrSPUb/6YS2b6jlKRjQmRI3OMqVamCF9mwUahhr7asTvCuFAnDQ+C7wzRp8Lb0v1Lm11flKCBBAmyNcOBfWAtlIvEQx4aU+QZML41YauGyEymph4Blf7dZORVRcTkPf0lCofqa0wF+jtX8Pa3YqX2LBiTf+DKYfYC+/UU00fQyuiaAI4TkIQ3dsdzBlosQePwvwyHggh0ZLzO5pvdufuS9MoDi11ZzTpN7cmJwV5SvJdsiVgqft4tYUlAzKoWBaP/cBDtO0Jx/bfSX3oZ2CwjtkX6auqDOzPpHOt+WnQS69SoS6FBqaq44+mbD9aIZ1opULWDtRsZY0lER74sgVUCPqKg8ffKZQ65aQTBTKccbSf+bF4keta9NDsRAkE/9KCnsyhoo2Edybh6bWrPBESg/R2UJXR9jtF", "layer_level": 3}, {"id": "c69078a6-bdf3-4172-b633-81aef3a29dfc", "repo_id": "94f96537-e6cc-41d0-af2e-17841bee1253", "name": "技术栈与依赖", "description": "technology-stack", "prompt": "创建关于项目技术栈的全面内容。详细说明后端采用的Spring Boot、MyBatis、Redis和多种消息队列（ActiveMQ/RabbitMQ/RocketMQ）的技术选型原因和集成方式。阐述前端采用Vue.js、Vite和TypeScript的优势和开发体验。解释构建工具Maven和包管理器npm/yarn的使用。为初学者提供各技术的基本概念介绍，为开发者提供版本兼容性、依赖冲突解决等高级话题。使用项目中的实际配置文件（如pom.xml）作为示例，说明关键依赖的引入和配置。讨论技术栈如何共同支撑微服务架构和前后端分离模式。", "parent_id": "", "order": 1, "progress_status": "completed", "dependent_files": "pom.xml,unipay-web-ui/unipay-ui-agent/package.json,unipay-web-ui/unipay-ui-manager/package.json", "keywords": "", "workspace_path": "", "gmt_create": "2025-09-22T20:36:07.7129402+08:00", "gmt_modified": "2025-09-22T20:40:45.7815521+08:00", "raw_data": "WikiEncrypted:w0CkdCdnXCcvlN5xOpiEhEX3U68GI0Ngj6azFMJo9xQD0Nwsj9XajeArLn33w436LuYJWl4xP0dT9zLNODhOpI2IFAi7bU1tz9apYGtFtyl5TbxzEvnnr+kgnRd3KNbg86m4PWaUwi/t/sj5J4XW06kkXdFcymzuvRM3nqVbJUue720A1VdzqM1XjXxT4AguPG+xZXTjMBFLG/U1lSsqDSPNwT+gm3g/NKHeF0dBdVumCCp7v6ub/q9DW6Gl2lkKwlLe6SxByNBc/CwRd9rXW/nd0n1QNGdVvYG4k9otkvYqCGgShprF+vAHM5uUfLHc6Mh+C58YEn1HaUeFsdWns9WuNaevJtw95eYjPr+XLz4m8nFCA28N+Bia8fuqvPLKzA5GSQ47KryMoJSe6PDH0VoDb/D7a8UIqX5/zImIBS0kvsPhiVXXIVx/FEntwCSs7DgqaRrswTpwuJg7UiVq/XpC1g+RXNrZcvw1ZIly3W2cl4RVW/vuHNUL+AjIpOzAkDGiRRXLjfTVicVIwhxIU/Am8P+i7eMbltdVD5EKdKO00pWory1cM1HryzARpNZBx6psWn5mZAvUTj50lOYG9RqCUT/QVPbKwugO2elxGUUr6G0Xq3H5JJpAig/YdX+GQ58Z4CZ64gtbb+4YUrJYSxKjhj7b+kMhN51rRwR8N/bvtSNTZ8g5pPb8w1GeyZ5CYSCV1dPI9A7suHOmoBoZGC7iVGe12KxWY32SdMpI2TaRwMT3gLjfqvp1knRwDcDWVkUVcV1DXNPhZXRGC7IGdWswBE/NAdqhNePrGxIXK771rOuNkX7cZiy8vxl3i0NYQCtvvFni/pPVHxyXYxOQQRYCXhBg5BhK+WQUhJV/AaIRLrjtRsYTd42bmUeY8QHkgaXBXlxlIFFwMHMo1qtMFT7T3SInXyh3lDaxJRL5Wb3YZjFlBsWltocJ1/cQNAC+BAH75tRnmb4lnkwMNDDK5DTGdxsxOlh6JCh5XsBRJYmsA0xIhQiz6KaUSiepFXfDkKJDRp34/5EbF+Ah/TthRf+/VbX5Y0ImRqq4n+bUytQbWVKVtTUkS88ZdIG1T6ZsLHwXnScpfS7/vR2USofJxUglOWN8zsJxToNIW58t0bdFCW/aKkPC+uGY4+HFALcEdW9ZPVPUMaX1pa8K1Zu+DoiiqsO96bZaC9iYepkbNJC9uVy4h3H5M5ep4Cyh10Ow42FnfV49vTKVz7y0XFtxNR+rHnpJ+81nZjTf0Wz3iCmkKU1J8RkUc7/4usBrc1+VGCa6p1ce+57rx3JcRymIsw==", "layer_level": 0}, {"id": "cef51d4a-e713-4964-9d91-96bee7cec865", "repo_id": "94f96537-e6cc-41d0-af2e-17841bee1253", "name": "商户系统", "description": "merchant-system", "prompt": "创建关于商户系统的详细内容。解释商户系统为商户提供的业务功能，包括商户信息管理、应用管理、支付通道配置和订单管理。描述其如何接收来自支付网关的支付结果通知（通过PayOrderMchNotifyMQReceiver），并更新本地订单状态。阐述其与核心模块（core）和消息队列的集成方式。为开发者提供处理商户端业务逻辑的最佳实践，以及如何安全地管理商户密钥和配置。", "parent_id": "6c28c636-0567-4d46-9564-c10d609484e7", "order": 1, "progress_status": "completed", "dependent_files": "sys-merchant/src/main/java/com/unipay/mch/bootstrap/MerchantApplication.java,sys-merchant/src/main/java/com/unipay/mch/ctrl/merchant/MchInfoController.java,sys-merchant/src/main/java/com/unipay/mch/service/MchInfoService.java", "keywords": "", "workspace_path": "", "gmt_create": "2025-09-22T20:36:24.868029+08:00", "gmt_modified": "2025-09-22T21:08:52.0777826+08:00", "raw_data": "WikiEncrypted:3KjeyWIVLPDcIZ9hw3jQxxSR3fqCQe+EbrsWbIl1j9wnSwKYNID51YEAxjP1uojeqaMnMYneLP1KRjw4ryJTm4ghKr3lVEn6btdJExp6YHUllLDil/0Pd7tmehh0iUzw8+FEjuFfzCPPB8CmfaJ0/3oXGq7sRiqYCYVR8/5hfzfzFeFwhtE+CnXYkflW2d4GLGsoCBwrJVP8nITXFgJemtg5EiRugkKqTRUK+HncqwFh0xjBhtZM7W/maSzTGSDNNqqR5yAcDQjHKrJVgZEvqrCqXFRw45aETucop9a0LoMgKMIuJmdGd888DmxEW8VQ1I1LH+Z+uu0vTGc3/QDjDuJVJ/6YkMu2AKJMHMPxprBIAt3aPTvfzj23Jh2LYOl9fF+lE8SS57jq2P5SCqBVhgvv06vsg0afGK+0cMUkFmzmHKA8vF1uBvGJIHzR6FlSqW2U0jxhAS9Kk2gNnGwzZ1dk1H9L3d2zaA440I8sQkQtOq8N8wXlbUTDdLUY+LAYfrx+tyta3gpJkB/wdMh0bL4p1poh3YpR6cenPZCxIg1LZkt7YasezFef+ILacMnfjIb297VNl4bdH1yMHLGFUG6GHsXpeQ5QXmoMjQe+dCiKbLqtYrfe2+yBhVC8sQHv6IzhSuuEe87RMp3CJbEANt8/d85VuqvvBeqT2prmy3IyLNMn+2QND0egH7UWtBp4hFhex2R99nJUojr6wCZvdcvFJElS+h8Ra8VmEPYw7DYsqB/J7MPkfFxIXGZTETIUmYUI6ZkJnPjYwQol0WB5Y7VdLmGsXMM4g8jGHbSU/spvjm0C6N1pN+96U7JbuJoxaDbnORsUa0L8dCGkNttq1Knv/rJqR9CTO/ITPjxLKKRM1DE8e77sOVXPZlXEvGbKvpU/4lRZzBLA7W93YbQL6NEGrncx3h0RzRvparI5nmmomJwdPJvr9Bi2keCycVxuYMfW2OsqDtjdxeRCoYQkg7N0nD8seEOox5lEwdjKbVf0jGmgd9W5Ol4aGDEAi19HvexaC+oIUKp6dwcgjWnaUCPZjWmwRvcWu43IZNlW4T61Qg2YL2NEt7ypo+XfLb1kwtbEZp/M9xnHub71F/EB/oqyhunt9/AAyqQGEditmS5WcJERx4EzO3qNlrMi9QZUI6vxAK4mLLGmfSxyO+ncnKQ0fuyVVLcVFbxapjzU7TgrVBp7oYBtJSMN37kySApbusVnUJUXHh/FsynjEeLQRP65Fgw/krlDk3f1/E9CAW6Q8Ikfia2LAQBS7QZ1T0UvS+9+F54KROdrNKiV+VftPw==", "layer_level": 1}, {"id": "d7839cde-6041-4688-a449-fd87d4a9e88f", "repo_id": "94f96537-e6cc-41d0-af2e-17841bee1253", "name": "收银台前端", "description": "frontend-architecture-cashier", "prompt": "创建关于收银台前端应用的详细内容。介绍该轻量级支付收银台的设计目标和用户流程，包括支付方式选择、金额输入、二维码展示等核心功能。解释其基于Vue 2和JavaScript的技术实现，以及移动端适配策略。详细说明支付方式组件(Wxpay.vue, Alipay.vue)的实现和切换逻辑。描述支付通道代码(wayCode.js)的映射机制。提供收银台主题定制和支付方式扩展的开发指南，包括如何添加新的支付渠道UI。", "parent_id": "776d5083-fe44-4ca2-a556-e434cfd87497", "order": 1, "progress_status": "completed", "dependent_files": "unipay-web-ui/unipay-ui-cashier/src/main.js,unipay-web-ui/unipay-ui-cashier/src/views/Cashier.vue,unipay-web-ui/unipay-ui-cashier/src/views/payway/Wxpay.vue,unipay-web-ui/unipay-ui-cashier/src/api/api.js,unipay-web-ui/unipay-ui-cashier/src/utils/wayCode.js", "keywords": "", "workspace_path": "", "gmt_create": "2025-09-22T20:36:39.2606539+08:00", "gmt_modified": "2025-09-22T21:08:35.7555703+08:00", "raw_data": "WikiEncrypted:rJ/rIw0gVau8jPGqKFBAsAycLDB6WVqwg825dG7e4d5ggZqHgI59V1mSFi5rJlcnsn+q/+Jq5p/uSHBmTmHAbIjY3NuugwrMub/Pjuz5y+yIR0Vw2QcSquWToMobGP0V82BGqvnEyytWr4/Ds8UMg7mVO72igSek78mpbW988H2Yr/FiXQZLKBSuCWybapTSzzU3Vm/SMhNzh4PXdpCcpNwcTccbS2gJnHUs7wnWvWa/VBzk5/H4Q2MJsu5zRCIx2Cw4+SLWz1zjpBrMRXfVha8UvJyFzo5iodjTdwtEoCsNK5oIlNkvPjLx2srg2CSoqpmy4cI3ijGFrcCe67s7amBBiAvus2sHzaHF9kf+eutdbxp14NnjmypHsWqmRXICCRnx4RLMgeYftDwaDyoG4OZvTk1f95eOkIzYuVUzUPejHDN6hGJwNrqJQ4rA3mOr79hCc6jCJ/BeZ3lki6VvLQ2l3TvwxCg3mkGq0ON/lHWTim0aLHRRAJNn9UUaknXdbONy93JfUcXs2OmgSbjNR1GVhuFdLSPObgJxDJvpPc4z0m1cBLI5rLqsN3l8bJ3c0PTW+/a8jCghJSHknqDwbX/z+RNPdO0+CzMnO+0vA9ynw+9m5ZXSoP/Iyp3zuL5Otekb+Ac/nnUCmY5Mr274QPYO4/j8reKLUJzN+8n3c/ZCuL1VCh++hveei537Mf3p2m4ujbwPyGkqtn3p1S8vTlJFXb+d4Rxwnt9kEzWawnRkTZOu9EQrFRmr4DQR+jSae5fSkVOsoGtPyTchp7bQ69iVKOpOty7FooGrAr21r7kZiekLeFU0o0UQV7C8p0PskNVk65K3HuA5XKp1fKIBgcvEKb+8BKBww+mGc0JuirhZpNDIRzSuZXHQE947Sz1t8DBGlPPpgoZktVGoSn2gr/rGDPhiTq/t3KBpWe/xTxgiTasSlbAUAZOIH/+P29m2fiD0HRauowgAM2CPJeYpJHHaNW7CwUoVjYAOogGOPsVp3cGE67P9vfftgNkSARyceq0l+MonqE+g/tv2IPv2l4k0yuqBhhfoWxO9LdsLib4F1Gat5dvODVh3PE1ZofLZK+OumchfLverEjeOJE/2xN7z1qsAKkY+5SYeslr0+Z8+b5PhTAWoPgvF1sR+H589k6irPPg550c492dzbi+4xlCGJC+U3RU3s4ncUczv3essx/11zNGEZenzSidZwxDIeq0VONE1LVJphPqKSJwsRFC3L+f3W3RQTcpO2/MZS/wNTSum1QYhV6nVLrfHAaU1BCW07lRIx9Zz0+fQgXHYfPkLOvpnA/iVOygsy0tN6k9I1y1Y34tEA/TyA5uKKLnnz8Vq7pSK8wE78KBUSUAF7nF7PzNUQtuSWVeL3yCwx78=", "layer_level": 1}, {"id": "474e9be4-48f2-4ed7-8ed3-7d18a7fd22d9", "repo_id": "94f96537-e6cc-41d0-af2e-17841bee1253", "name": "分账功能", "description": "division-function", "prompt": "开发分账功能的详细内容，涵盖自动分账和手动分账两种模式。深入解释PayOrderDivisionProcessService的实现机制，包括分账规则解析、分账金额计算和分账请求发送。描述PayOrderDivisionMQ消息的结构和处理流程，以及如何通过消息队列实现系统解耦。说明MchDivisionReceiver和MchDivisionReceiverGroup实体的设计目的和使用场景。提供配置分账规则的步骤指南和API调用示例。包括分账失败处理策略、重试机制和对账流程的说明。", "parent_id": "323cc3e9-9355-4b42-b596-ce729a56c076", "order": 1, "progress_status": "completed", "dependent_files": "components/components-mq/src/main/java/com/unipay/components/mq/model/PayOrderDivisionMQ.java,sys-payment/src/main/java/com/unipay/pay/service/PayOrderDivisionProcessService.java,sys-payment/src/main/java/com/unipay/pay/ctrl/division/PayOrderDivisionExecController.java,core/src/main/java/com/unipay/core/entity/MchDivisionReceiver.java,core/src/main/java/com/unipay/core/entity/PayOrderDivisionRecord.java", "keywords": "", "workspace_path": "", "gmt_create": "2025-09-22T20:36:52.9559347+08:00", "gmt_modified": "2025-09-22T21:11:06.8391616+08:00", "raw_data": "WikiEncrypted:TfUUykgSV0NZhhl6ftJ5NEagY39IrMw2t1ZVkD64aWS22m+Vct4ODbwBqF8jvGxah3XxOzbdLtr7R/U1uRDszifJue672eyOZFOn3bmaa9MJWO+Nn+SGkyJnb0qL721yh6wI5cjPkpi6dtiF8EWLU2ztgxm9X6sFzThnEwPEm+5V/sbbUc+R7dteZo6L8Jvr3aj8tfb7sdi52YOOt/eXsEfhvjDdYBdHDf6rxs3L1yvdCrPPp81+C2Btf8k3WyNZdHNU/uWVUGqBmn3sjJl7tf9L7JtfdrJeT8VspUvMgBUzYONUQBfwD4Hb6N/GAOScfIF5ZCYEMD8bttHJ2bU7K9Cnn9vS1PSLzR1ZR2zC82OCpsNe7V6TzIciHav8IlBflWHj0MdnAAUzFs35em+Pp+GZi7ZcP7EGxTPynIUvU9Zp5l9YXRClUWRVFiCFSitKrGeY9OECqQ9uRyO50xj+9JsjfPHoi+WlS5JGAK3bxeI7dTeRxuPe+NvH5yTrL1vV4G0a5TKEVvZ37NXZN4pb8wxuni/C9GrdxowsU6s0Ve9vsrkEZNUGrh9igohj74qK7Z5nIGr2prTCQfSJiVZNrCQOtjCI2l8s80kLpHHtBWdVPUEM+f+yWC/A0zes3GrfQohg32UDnxa5xk19pAey8wqlJwz0QMFygyCs7CdVRWkywgkRABCYPj5KypwBUrkOdIQCf5e7Xj1+Q8yEPCZFI0drE/VAdZX4jnxI2X6JFwOiVe2bG2zVaHb8nHXY7UF7kmo23pHGhIXBIeSMjWMCu2qaDzXWaaP4zeAVKyuMR0QWGXMi0/gy0SuKWp07aaV1kBiP59qzIbUK4/V+YgCk21xtHvqcKDSHGj6JdZQOY+q8+zvfrP2ZZMBedkAo//eoA5+yOAtlRiUgptGIyWp+B0mnTrr7VIzOGWtbVzDo4ZVYVJXn9+VvZaVkrCcRykp41W2tqNjMlnwz9DQmOE2dQxhPxLDlf7n+NuiXq48yGbLLmCAK8BgCrtB1USyN4+VUnrZKBOp5qCBYRcpoPon9+0dQwTAUAfVyx62/Qspasd2+mbmzxTH/dD53gvQhIFuxSFw1Y5nJ6kKClRBqxIppPzSwYQIO0fBewOojN5xao7Lu9Njiu17ZRU4IJLji6cG2EPddsORAIQ9uEFmaPig8cO2zq4YHL2LKMHHPdadDbudL3JaT/crfDfNb2Dwc3W+siCACOGEKjXudGWDaSzk9CAqM7s+aDa8Yye7SaMAyMp7SKTmhE5zoyDoXtkF+2dBYyzZaoUb9qwc/f4WqStLiMd0mbfWBfbBi5wWhIIrGiEbiS5siRV42Q7UBYx53FpPgLOnv+3WJSZf0oMZaYT6aoMieUPulVdowzXgHKN1Z9ArInccdsSh2HRyfPvhX+ALubwwlVtaVNCgeuFJx/zhIwovvsioOhL8acvP2RzEo4MxBKawbtT+7eVDy7z+qejr3FZxxzkC83MZoICPo6+6U8VshSQ+/bNPMYwDaj3+Wf0V2z+/DQCXP7yMP7EvbZzU5/wAGj7TRQutAIeecoO3x/cRA63ve12Na6Sq3mgZZhEb3Sg2ovt2RTQgdXBJyOqRmoaV5ISsRRGQ6bxOyoeePyiMm/AMh7RfXzasabCXmhz2cEP75H6O5QzT817kCYpEWtkVMEHdhcYcR2ZM7bHORxw==", "layer_level": 1}, {"id": "2f9b2fcd-7489-46ac-bf0f-7e70ffdb3352", "repo_id": "94f96537-e6cc-41d0-af2e-17841bee1253", "name": "退款API", "description": "refund-api", "prompt": "开发退款API的详细内容。系统性地记录POST /api/pay/refundOrder的RESTful接口规范，包括请求参数（基于RefundOrderRQ）、响应结构（基于RefundOrderRS）、认证方式和签名要求。详细说明退款金额限制、多次退款规则、与原始订单的关联关系。提供成功和失败场景的JSON示例。列出所有可能的错误码（如余额不足、订单状态错误）及其处理方法。阐述退款状态的异步通知机制，并提供幂等性处理的最佳实践指南。", "parent_id": "fa5aad6c-5218-42b9-ae16-cb022c852b19", "order": 1, "progress_status": "completed", "dependent_files": "sys-payment/src/main/java/com/unipay/pay/ctrl/refund/RefundOrderController.java,sys-payment/src/main/java/com/unipay/pay/rqrs/refund/RefundOrderRQ.java,sys-payment/src/main/java/com/unipay/pay/rqrs/refund/RefundOrderRS.java,sys-payment/src/main/resources/markdown/doc/api2.md", "keywords": "", "workspace_path": "", "gmt_create": "2025-09-22T20:37:00.3489642+08:00", "gmt_modified": "2025-09-22T21:11:25.0546971+08:00", "raw_data": "WikiEncrypted:IsXrw8CHTmYocng0pZ32k3/lAYvpK9kottRD5hMFQ6crxTvQJWMx5Z8qXW5TS4S7zi2Ol4uXkYhV4SzoGPaggofPt6EeA59AQqJXYcb9iQoHabjhMJ40xnbFLC1l8tidCEL+WipLHVPun8slKACbmO4dDnOup5xr2Ngusty3YTft77pflLJJqy9O2DZ8tsoqX5sCK1jayRnNO8lfetTjM3DZ8CWFuTURVWy7/C6bGeFi1Qz3IA04ETYTQGfPD7VBU0EHs/Hik+3GmNxYqxuDuryUEAm4jCQvfwYtly1F1EE/1Es0vTBmqmSaTz6FNJgtmCnnnzAWhyjmXPt7NY2cDrD52nGQpyv7upxVsVIwsWAE2QHnniIOAb3mGClM4AVzCMINbdu16F7EmHU9d6122680325OMG8rQWFI9B+5N3EB3dQvuuHEXl9wKUEu8zlWbp++S8s5GpkJee4eJ5XBaEZ3rkEOl1eZ8Mhps9RN4zZf4et6U2CjcA9YsghTkpDXNjLF0fL+TlAYTRelcJscAwqpl6Un49/2M7ywG/HFmRH8fS9Ipi/sQQJjYQP3J9W+JqGmKt7V0qgat/2rTtF89y3lJs+meEQxq7AV9rHBltjxqXDf2PppsE9tInZk/icE2soWaobyqt6AtpdGwzojRUdGy5SbTPS7OXrkEGVn/gVw04C8kQackzZJch8fe600k8S3riFMEJg8uMzbB3x50CKjb3OAFUB9guZiSmUMuApBdH2HaQHabJmRvKS4BmJqp4a3N9KUWB1BBlfuKqwYBFvKZvjyxB8TGAwKWd8v1O7OwVVJ440WcUqunq8TczmpaVWshboGvD/clt0K71h5hfOyDHsNGLvfG49puIK0KZJ9YwqasqLwuaKR9ExuNgaggiwi8n+tZCqvh/K3WfBcN59MNfMSu8ATZH26/5Sm2QvBi2wfkfg+VO8YSLVRYknfLOc44IgrbYHuuZGixSRG4PhyzBAiUiwGMxKmZLX2wl2cOvrPfuLKFgcb8pAJlZ7CQveGtNxY3zweSIIJ7iCYUW/A2oC91SS9FE60VyAC0A8Li3Vscb1kby1UrwdddWbCH/s8+Vnb/mIms1NW/o7qSScyfL8fz6Jw9p6jKvDMF6/HdK1g7q69qolfnENZCann/hZ0CF5iuybmvl9YoUAvlvWCzEwouecCSHU4pq5plgGGBThg/ror2x37n1Y4P9/JkGw0OXYEER3UVtAse/7XOaWgZGZb+uUaBbFUwlPVWopsdkW3jdASZsJlOVrd0tD0SmeXPbkT1xGv5NhK2oBbNwdu/kqUaTJ7f3c2/5bQtNXQizehgx/FctIGHtBv84R6lmxZEbdUTUwE+F/EN+eBXZ92XUQZAyeWC4IraK9l9LU=", "layer_level": 1}, {"id": "ac4b8273-8eaa-487f-b71c-c073ef98666c", "repo_id": "94f96537-e6cc-41d0-af2e-17841bee1253", "name": "消息类型详解", "description": "message-types-detail", "prompt": "开发关于消息队列中具体消息类型的详细内容。逐一分析核心消息模型的结构和业务场景：PayOrderMchNotifyMQ（支付订单商户通知）用于异步通知商户支付结果，其接收器在支付网关中实现；PayOrderDivisionMQ（支付分账指令）触发分账流程，由支付网关发出，分账服务接收；ResetAppConfigMQ（重置应用配置）用于广播配置更新，确保所有服务实例配置同步；CleanMchLoginAuthCacheMQ（清理商户登录认证缓存）用于在商户信息变更时清理相关缓存。详细说明每种消息的处理流程、幂等性保证机制和错误处理策略。", "parent_id": "ec1607f7-2606-4bfd-acfc-daffd521aed2", "order": 1, "progress_status": "completed", "dependent_files": "components/components-mq/src/main/java/com/unipay/components/mq/model/PayOrderMchNotifyMQ.java,sys-payment/src/main/java/com/unipay/pay/mq/PayOrderMchNotifyMQReceiver.java,components/components-mq/src/main/java/com/unipay/components/mq/model/PayOrderDivisionMQ.java,sys-payment/src/main/java/com/unipay/pay/mq/PayOrderDivisionMQReceiver.java,components/components-mq/src/main/java/com/unipay/components/mq/model/ResetAppConfigMQ.java,sys-payment/src/main/java/com/unipay/pay/mq/ResetAppConfigMQReceiver.java,components/components-mq/src/main/java/com/unipay/components/mq/model/CleanMchLoginAuthCacheMQ.java,sys-agent/src/main/java/com/unipay/agent/mq/CleanMchLoginAuthCacheMQReceiver.java", "keywords": "", "workspace_path": "", "gmt_create": "2025-09-22T20:37:06.1328023+08:00", "gmt_modified": "2025-09-22T21:14:07.6469195+08:00", "raw_data": "WikiEncrypted: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", "layer_level": 1}, {"id": "918d1aa5-5ab9-4cf1-baee-3cb7f20be143", "repo_id": "94f96537-e6cc-41d0-af2e-17841bee1253", "name": "退款服务", "description": "refund-service", "prompt": "开发关于退款服务的详细内容。详细阐述退款请求的处理流程，从RefundOrderController接收请求到RefundOrderProcessService协调调用具体支付渠道的IRefundService实现（如AlipayRefundService、WxpayRefundService）。解释退款单的创建、状态管理以及与原始支付订单的关联关系。描述退款结果的异步通知机制（ChannelRefundNoticeController）和对账处理。提供处理部分退款、多次退款的业务规则说明，以及退款失败的重试机制（RefundOrderReissueTask）。", "parent_id": "9dc2cf0f-4dd9-4667-9176-27002bba3dd6", "order": 1, "progress_status": "completed", "dependent_files": "sys-payment/src/main/java/com/unipay/pay/ctrl/refund/RefundOrderController.java,sys-payment/src/main/java/com/unipay/pay/service/RefundOrderProcessService.java,sys-payment/src/main/java/com/unipay/pay/channel/IRefundService.java,core/src/main/java/com/unipay/core/entity/RefundOrder.java", "keywords": "", "workspace_path": "", "gmt_create": "2025-09-22T20:37:21.2786491+08:00", "gmt_modified": "2025-09-22T21:30:55.5323766+08:00", "raw_data": "WikiEncrypted:IsXrw8CHTmYocng0pZ32k5BPAgHBv/zsyL5w9AvySxMGlgRrLsNkuKonvQKws7Q+FsyvOcP4i3PHZzAvil0PeFL9rLJz8ebDoxWm9o0JArt+DyyFsfGDcMCHUqdOhjzbv8Ru2C4+bhymLzAPrcM4E6ivQ8D9PFYgKF4PNuX2lcJ/5ogyVfmMn2J56ODxwsiS2ctE1dX85XWS/MUahsh3e+KvEBFC146IiCwJM73tKGncPZxvs0PW05/qmN2X3OAe553rrmnVpmRegvDHt3b8PXArNGpjiuSnBE3VaM7ySH414kqYyMPrfVC+thozsih0SDU77/3PZJuBhredFRtuHgxB+Nup18V9bFb0sjPHq5pqPpzUEx/csWeSvG8WmsHW0JtGQJdiUNch/3unT9kxsgOe3lwkEQ62l7RgTU1aiG4U+qqFEzQSfMjaSIe96gXFaj+rfs6JRLID7S6ogNGAfIyyz5ujjBAdToM0ZJL7vJpXqcZU08TJg3egIPuhmfLDjgorjrCP9PP0TFffRcpVVGxJ6WPiiJPtOG8U/Eya2syNG8aTpT+OPO3ORxqm5Yo3LX2jdSdHecQAAO23ZJ9fgqHx6bYlANMEtkDM6oezdAz2SXev0Ov2OTPNTMHZWFZTPwHgXPg459Y7l5PylDjf+JAEDPgapqyBc0z8ODvXkvP6LYJCd4IBljWW+lhP8DFrGjhnGNEBs2SFd4hHKuAJKldQzpRRWdG7auARYiU9znWAM1ok/BpL2mPnUUKmi3kLKBQ7M9VKWr2kyXbNmcYpdFCRKzgH+0uwjXmMkY0YIIC75LdVh2ieLmzV8tYtDAZo9OzIbTpuRiybIEz/682hIj2T9GSyz47C77gkyZeyW6KsLGBwf52jIDFshRrvJfFjoFaeZCharZGBITq2RLr+DKyVSYoXu92p8Tkwu3VM3CBuLc8RPa1tQDx2/n0CdyNOihRUTUZyfYa/zuwxE7sFf2CchzpdsHTNMryRPTttGyKxN7PMha8rVuf5fkfxMqnqnmau7R5zSWNYR+xpK5qhGZOD5UMMQigblWSvo7KOYMBOiUDo1+gxyT+bmtc/k9ZiF8Kg9vJXtlgrSswC0H+cSGgGsggKBwBVpeI8tS0rOAqDinD+3lgRDkKSD1dw3ethVuRBtpFDGEdLBQ7HL9hoY0Z35NLiQq6aXajZs7S+CtzI1JCNx7fb8wJVOkWyu/sgWvYd4U+zJzprzoDPHfiWbFPoZz50/hIXfQrcR8ahoqOOWWJfGsjjGPUi7Iit8suPuKn/57DPhBdUJ/vsVskBJxUM7z0nrvGmFaQqJ8OdR6ZMG4js5pI8Do5aBx1SIEwB/NTGdji4qWmIeM4QsjvEEnP7Z5Ssu5HXWToJ504JN3MmX6QoNCFOFsGVk3BzUJiNV/WFrvF2YovsCoZiaWXjJC5u0JmGr9U81BhZmlNoRrg=", "layer_level": 2}, {"id": "3f472633-1c5c-4e51-bcd2-61e622203f7a", "repo_id": "94f96537-e6cc-41d0-af2e-17841bee1253", "name": "应用管理", "description": "mch-app-management", "prompt": "开发应用管理子系统的详细内容。详细说明MchApp实体类的设计，包括应用ID、应用密钥、回调地址等关键字段的安全处理。描述MchAppController提供的API接口，如创建应用、获取应用密钥、配置回调地址等。阐述应用密钥的安全存储和使用最佳实践，以及如何防止密钥泄露。解释应用与支付通道配置的关联关系，以及如何通过API进行管理。提供代码示例展示应用的创建和配置流程，并说明该模块在商户系统中的核心作用。", "parent_id": "cef51d4a-e713-4964-9d91-96bee7cec865", "order": 1, "progress_status": "completed", "dependent_files": "sys-merchant/src/main/java/com/unipay/mch/ctrl/merchant/MchAppController.java,sys-merchant/src/main/java/com/unipay/mch/service/MchAppService.java,core/src/main/java/com/unipay/core/entity/MchApp.java,service/src/main/java/com/unipay/service/impl/MchAppService.java", "keywords": "", "workspace_path": "", "gmt_create": "2025-09-22T20:37:23.4987228+08:00", "gmt_modified": "2025-09-22T21:33:12.1599983+08:00", "raw_data": "WikiEncrypted:naY8PhELSlVZyhrLUDeY4MZWJr0tObTo4rEqN7y1xkah3wDUt9jCoHuVJ37AVW7kdF2EcXLdD7sYkzQdBg2eviWO8RThOobP3XeaEo8rfdKlloZMcZupV31HPZlQGuhS3u4fJbxdter3gc7TwIM138cKotNYhm0iKGkQ6DpvVd8xyUWPhik+fvlP/zEEdcyZk6//W43mF2V7mMqE3FLZZsEfNeYv3oVXvD2y6c7Ij3L2ed9MiICyhrEmp1s9wX6Q2oOtnlogeg715wnEN9+6irClvgIp6uMUbF/+w3W3JQmAxNsgkJSIJN9vIX/3xWwimQzBuYl1WFq3ewm0/khdWF1NaUsZ3yDBIOkHGiDyI8BtJF2q/sQFofBEkAc6zriRwiLC9vc9K0WfJaYC1GQyJvcqVcGmAkpCzudwia3cm0CIxuIrB4gt0Blyk+379HbHffvHTebe/yLFn8Sj2fROvH1QItE5wNMAEysPdmaEqGjPWXczNUrd9xz+Nd1jwo3qICg3v4OU3N8cqaI/VYLX3xrUoNPnnTeF9/j+fz2vNNFX3LrglI2/rDAVs4DEsRB3dFNp/x+cg4fLUQyNpYkJbPm93NfzLgamM7TrnHueRDM/3jLtwXHt2uJfoMXCt7VhEirVYAbqiOlPp8I5ZfyPkSSyJQQLy9BlpPmMTIDVV/EiA8IOmOd4is29xS6n+0Eviv9UrtrZ+3ahiUignyy3IoxVIuJnPQLcOMGAD6DwX5rzZBoQo0bRc7xERL5mS77GWGdnkLatZvxogmynY0VptHNxwTouupK0rWA6udmwTXqhCaLFtqSY9TtFnIoPK5pYj64P7zdVvhr1U3R1WeIuQOhH1Zo0UGDM+J29EuL4PpmdSn9Od6nb4Erf/+0sOLX9t6iGgKbF77xyblgWNMCvRwAt8nuQMzWdU8zEveQF+YgNNwaZT7vyZyo8CBXR2C40gZxhu0V28r1LK4A6YExTu51cmECVbNZ+SEEY7Iz13YgM7PFMGrUFlfEg/Nl7JOfncI09NpEG/UfIwUiV5yWuO1DEsdA7a4sRhslbpNK9NvqN/gnKblD8+pXsFgJCKCflRq1aWfuATt/CrEy0Iut/zdEcO8IdGg30bB1BRivcyrs7SOL/rJYTZOj8V2gtqmepY6vasq1qVwrU7CfIXu0q6hPJldcPDSYPWyuvwgvKtyfMmkET4YjqM+3L+LE1w4E0jDtFWbE6UhWCGNsLLTwxVkAH9Do3AYNtB8OjkXhenSo+EiDUl0L8scU/1xE8auMlYEWjyndRsIJCXgOuqVHa44QdC3EHu9o23svuOnVmANf252/pBn17t7cvs5Z7S7ZRima6kYZCqP7MPZ8TwuQHqPaxLvPYpJP8gnHuOmY5Yq+YxtsV2kw8KTIuTU4hKlk2/7SPZBrMG6kl6JW0vbKcXupKsUteDgVDOeqs+sy8RYJUG8/UCyt4iAhuSp1zHtOy", "layer_level": 2}, {"id": "574d46ea-ffd8-4b8e-a21f-24c0d9111838", "repo_id": "94f96537-e6cc-41d0-af2e-17841bee1253", "name": "分润计算", "description": "profit-calculation", "prompt": "开发关于分润计算功能的详细内容。深入解释分润算法的实现逻辑，包括交易金额的计算、分润比例的应用、多级代理的分润分配策略。详细说明AgentProfitRecord实体的设计，记录分润明细的数据结构。阐述分润计算的触发时机（如支付成功后）和异步处理机制。提供分润计算核心代码的分析，包括金额精度处理、防止重复计算的幂等性设计。描述分润记录查询接口的实现，支持按时间范围、代理商级别等条件筛选。", "parent_id": "3fa8e6c1-d858-496d-8d38-efe8ff173028", "order": 1, "progress_status": "completed", "dependent_files": "sys-agent/src/main/java/com/unipay/agent/service/AgentInfoService.java,service/src/main/java/com/unipay/service/impl/AgentProfitRecordService.java,core/src/main/java/com/unipay/core/entity/AgentProfitRecord.java,sys-agent/src/main/java/com/unipay/agent/ctrl/profit/ProfitRecordController.java", "keywords": "", "workspace_path": "", "gmt_create": "2025-09-22T20:37:43.2290983+08:00", "gmt_modified": "2025-09-22T21:33:30.442678+08:00", "raw_data": "WikiEncrypted:3KJFs/+fakBIzq/vLbWLrVi8pqfpPsQP2NyRf5qV2pe/qf8cwIbvzUrzz8vFJ8iFWNd228nPwqIkdzoAizGpXRBT4TtL/7dHSrRslM29UkNv8baCtbsGp29B9N2cKI6vr15jjt9cI1T0TFNxdBobbhWe168vnkMDJqpgtGO5yqENVdoKoCgLS8JkI7TF9ZK1aqB4t5KNlfvnFkvb20/4nzwsRAzDJLrZWzOaDSMxCbPKFS4S3aYBHNbAeLJxokYPA6PC0diWqGZH9pzWgPVU7qzEpCV/eq8dYOpWKOX/72tBrbpUpz6pSF9cA4VFHKTf40676Ivi0U8DKB43KTGJeVwFBfFP8J/0CwnSnpndmWG1WEnNCg1HDa1FYVLb0QqHDES6P5ensLyfau9EncVqn4ThjbF0nn2xAjj2zJpX4z1o+B5X75LwnLubeIm9FpHwT84GPRlffWCLa3RBOBCSnSqJwy+xDcfCX+Qcig0NgS0f3ABwL1oX6NmsnsO49JqHxw46KK9ib3/3n+pwHcawnktnAaFc6Oy2HyD9p7Liy+jtq1Jq4q/21KMmMIlp8EOAcCu3VzLyF5ui/XS0V9VrgAThemN+qfzfqEc4Fob0FCwkJKw8QME+3Q/pe6xaGj6XBs8zHGxsbqYGs3eFGgjG4I1MWU0D9cDUdhAwDW34pOj9vdVLsdsKOy1VNIfff6tR9rglDwQmWEqxhrqINeBGBNRO70GwXpZxV8AkgYQ35hefCHQIzzhHEi+DFBKObnqHmAvllsqQhoPYUK5kmgV2v1U81e9HcLnWgxUfgNdgtvbPuAdqjeuw6ob1HIVbO7YEHnK3eIF1d6kZPl8Unv6Dzv6EwAaqXLdEW+v2yFPB+pVw5mENBaQjxxuAjT4/tG/uDMe/5vBgNPdYbQI+9UbZNCX1pNWyWMQSPzxdDMyu4vQDvNpATP0ZHh1iwXOAUQuiBQLfAaoSopEi7fW/r8Zb7ISDOF84Hv9kKTWieLy1zE9QqBbpfWYlW10FBFUh9tVYX0B8F7duLd5MfrpFcwFp7p89je1nJZNoWFrr2HWBeDotLO/59pzRhSbZ680mGbY4DuPXaYqpH1tWxDy2p3QlhYumllf/eL+bM+eTdVM/DPJ6Fu9dVGxKD1uzDKF7MUqq3NTDMmlx6doH+Qj4u+mttrUQVCmkiZslnA5BFEpMLoGWnAH+6ql9RgpLnpuznCOiGvIM1FYHlszDPA+ufoGNvB7vZmCgYdq5/xMsRlGMn2LHM/pDzSpssygwuUmiv2bD0yw0X/y87v/1fNZywvDyrpy7bkV8qKSO/BQ+GwhKHTHM8oSE/8hdltO3kz6VXTb2LZyLrLKSG6+Ett+PKZ48nNpsRvNvzW8QCHfNN3LG6LwwI7U+8b2YowX3pPEUlY0OYUL6yoOkcFLefu5gDds2oCdZZzWjgAkBxNiKVJqRbPKY7zZl5aRRM+xH65DqYdBfem/oiAopNWLrNHBxnm+QqQ==", "layer_level": 2}, {"id": "c56e8581-79d0-452c-89bc-84ce0e7577fe", "repo_id": "94f96537-e6cc-41d0-af2e-17841bee1253", "name": "角色与权限管理", "description": "operation-platform-role-permission", "prompt": "创建关于运营平台角色与权限管理的详细内容。深入解析SysRoleController和SysRoleEntRelaController如何协同工作，实现角色的创建、修改、删除以及权限分配功能。阐述SysRole实体表示角色基本信息，SysEntitlement实体定义系统权限点，SysRoleEntRela实体建立角色与权限的关联关系。说明权限体系如何通过WebSecurityConfig进行安全拦截，并在运行时通过JeeUserDetailsServiceImpl加载用户权限。提供角色创建和权限配置的完整流程示例，以及权限验证的调试方法。", "parent_id": "b6853ca4-49b9-45e2-a077-b5608db8020f", "order": 1, "progress_status": "completed", "dependent_files": "sys-manager/src/main/java/com/unipay/mgr/ctrl/sysuser/SysRoleController.java,sys-manager/src/main/java/com/unipay/mgr/ctrl/sysuser/SysRoleEntRelaController.java,core/src/main/java/com/unipay/core/entity/SysRole.java,core/src/main/java/com/unipay/core/entity/SysEntitlement.java,core/src/main/java/com/unipay/core/entity/SysRoleEntRela.java,service/src/main/java/com/unipay/service/impl/SysRoleService.java,service/src/main/java/com/unipay/service/impl/SysEntitlementService.java,service/src/main/java/com/unipay/service/impl/SysRoleEntRelaService.java", "keywords": "", "workspace_path": "", "gmt_create": "2025-09-22T20:37:43.4196767+08:00", "gmt_modified": "2025-09-22T21:36:21.305926+08:00", "raw_data": "WikiEncrypted:WmYz76ZuIeWtQgFK+sghtTXfnxF/UtZheVGMzRdNKInFHxC+RAu9TXuXf4xr5hE+qGBSgPOJ0ki0jaE0Xc3RTA5Rkx8yFu7Ebxw7TsBs8XjoAP4EeD5OKL7v4/FWF8suKhZF9j46Ka7QwOwihy2eg5NoJyOeO+sQuvBO9sVe86QG28zqGakvhbkQ21TaaB5PUMMM+J3XWvNhqbGpxZ641vVdDM2slzWukfxTt399EfcZtbNtBcKgmz+q4Ho5SrUvoZHdqq69TpTQNlKX/K/15EqZpXisFmHrETQBvZggpBkLWNQ3d9W7tSLaJUs5wWOGM4/ZWgZir0FN/v+tIkGayKPrK5sijNQbvbBGe9pJA8bk0AB+54Ewg1MHU+b36r1GTaHNfQhoOT7ZsF3K3ta3y44iZASwnfSujt0TL/QmX6JGxDgxiDB8bQGCFWUNbx6hpsnWt7DNE9wQsLjW6yceDdaWWYgWdR0l9mhUW9ufCRE5kapBf9LH2KtE+O88HY+rCNwiUKPLYtMjQCmU5HeCjzxFlUNfpa8B/CZJGFJg0TPzP/tmmbFowvWhp6wF1QEm21VBh1syFuL5a3HG7h7h1ZOsWqcTbskk3BWuNgN27P0p5pJ0dR23q0IrxAVL7LdD0txe6Q4CaeEMgsE5tiO3Lw0vR+f4+hrMPLbgc4YA85EuTZLcam4Og7lEFSWKrj7bm66vIuQkxdgyksLzLA51i05NUjksk69FkbXfSpoqoKZ/4DiVbhviB29cwPNg0xOnTEQwjkO1LSxeS3j0hsvdZg4SGZd4zSBzaUP3u5cbrUBQ6WghG5s0nkHubmPDZChRWnacVNc5Jh5dyAfRJ65Yht7uHDl7HJgNaXliutOIuoGlcyHHLPa1zPWK1l2z1x/4xWpLeBoYX3iCwvApnJ5WgVGSK1XJESMqbWbyuuJyDBNxrTAkHOzDu/7dek8EG/v5sREBeea6rygQSR+JCy4TG8YZ3Ly8wCJ6/ROI9amkZK/ItPidr2DZrW8J19wrkhYrp2rJU6XtXecOfe/C2IH41vIMBrYWBp8mN55nZMa87dFgvsy6RPJF47y7gmKkFhvUjONQAEkQ6k9e/CUTZg6a98GVnr02VOXEhCqyluJrBBtTO/jtZR1A//JyUrvWR0OTR8/kHeTJrbtLNry3e+a80oiqRD9GwQXxV9D5eTtUI0Lm2rjDVkMluNwP8jIvAlYdl7hBejLi8N6KocvxYfBjObks+fIfmU/aN+QDBdwSFoEsOk0R8KcVQMMtp6XXiLNefTf020fAd6aJ31EJnY/8YKgBBTNqMJZoAFDrzDXudfuLU+nH8chLrr0lWFFgvz/O19NGf9gRIgCvbBKuwvgFHfgniuTnNkVD2JnMjpwcREPWDYcD2BldkqaPFty/eD2PcaFuH6SlT2tVAj+VPylkclzE8I1CmC9FWeDWMpngJhwVAfVJ9RBkoIcD0NKBu7XpmzQF0QPoVy8ldtfPUsES/k2WL1fHg0obWtLKBPy55y1lrqPv54iWtyjLvPV4P41MvQuoRLiDA6zqSkYs4K4E8pYQeOvm8cNKBqil9t2NUliqZUNn1Ah2nUbXfeI/EsNij7wguZhA7ZvwlKbhZ8URjJxEL1VF1oAiPaJeaA5hXdASvESK/oApDbg3xb7r7XGQWsCarn+zk0j3my8mG9J1OE7gAF77rtVS0eIokgC71rI0CsQ8EyyKbwqrM9QXkuEPKIwNJ3pfVMQZ96SMkWbGZZx7R8cbQL8X4H90/1/+LAHKEwRSH+ZqmyMylqJ91fpsNngvn/IGCEucl+fErKWvjQe9gd4x0OZX3Pg9bo24IQGLrBWVovVQ3irct2WbcMU30mHC0E52hZ8OunEj2ljjJ7bXVSPlfqsJJPQ3WKL0gDqg3QEAgedjOlmnjT0OAxnXqpmQg+mQlf8ai0bUPnLf8t8qMNItOG7SeNAuvdz4+xU=", "layer_level": 2}, {"id": "29596eb6-ac05-4e07-9b72-b9514d48e73f", "repo_id": "94f96537-e6cc-41d0-af2e-17841bee1253", "name": "支付查询", "description": "query-payment", "prompt": "开发支付查询功能的详细内容。详细说明QueryPayOrderRQ请求参数的设计原则和使用方法，包括商户订单号、平台订单号等查询条件。解释QueryPayOrderRS响应对象中各字段的业务含义。阐述PayOrderProcessService如何协调调用不同支付渠道的IPayOrderQueryService实现来获取订单状态。描述本地订单状态与渠道订单状态的同步机制，以及查询频率限制和缓存策略。提供代码示例展示如何处理查询结果不一致的情况。", "parent_id": "97769763-5625-4e00-a0ed-ae7298840dd1", "order": 1, "progress_status": "completed", "dependent_files": "sys-payment/src/main/java/com/unipay/pay/ctrl/payorder/QueryPayOrderRQ.java,sys-payment/src/main/java/com/unipay/pay/ctrl/payorder/QueryPayOrderRS.java,sys-payment/src/main/java/com/unipay/pay/service/PayOrderProcessService.java,sys-payment/src/main/java/com/unipay/pay/channel/IPayOrderQueryService.java", "keywords": "", "workspace_path": "", "gmt_create": "2025-09-22T20:38:04.6121325+08:00", "gmt_modified": "2025-09-22T22:06:03.5380236+08:00", "raw_data": "WikiEncrypted:l9Ho0Yh0r5bYH9QEePEhkRf8l1+Rk7oJsOChlHGEYVSypCFYzjH8GxGjQWb6h7RwqF0D1JJj1hndT2l/HtnL/7f3fiNlcfSLDuKZZXuyl0ltMSfvoa3Ngxg9+WUxK/Yewo6lQA3tbovPt6AC9XnwMS6NDBYl/wOhRdcjJZF63MArBk+cP43xstL+FMZ0g2Tg4JoG9E3jHUF7VBK9AeKLc7StO2OwSCXyYupNLK6WKgrxt8qMFlGbxS/EOmN+8g/NOH5g3XxUFGsrTkZFkSg7Ji8YQipVVSNaN/Rw5If3TFhDBpU0ZTPVCKbjM9mJ1YcLvGxbV9jXe5okmdittgm08EJOnI2z6OJrb8m4lWpMG6mjXmC8qQVDrODf3Ch5YfTvKH7DVMVbrTC9KvIh9rhqhOxcpp/bbOkrDWJr3llqnSYBXE7d9tGKFDzQlARTJ2kvWOeBPTv8xrcgTYY3eE1xEJk8tGzFCGHCItc53nDl6OJDO2+uWmuOYyRAe6q0G4zPs/xYV2jdZPcYP01j3XIgiYGdezEgWcLSuH1lkeHeIr2p3iwjubzCaKaq7HZpcOt21SfMv+xpf8Wak+xrAdqZU0dQ71tevuXS5WP8Lhueilukyl/KTaeDB/GRE6la7a1rbF5T4mfxsr3lO1GE+GSGEIFRPlcNg69KtwOL2Ied1glcSAz7fPihLu/Iy3fj84CC2K+LF2Ua5cPLxmT+4/sUvJpRb2tMUU6VX01yh2zCm87sW5bpNYb1WPWe4SBkvU6cvsAAtOkdatERqeIEr9fU2HwpKOeqTaIJKU+RYTl1s5F+gxFXgOwwtWmt3iqRvs48EBmIfDxuowwx1bN8XB0k27t2msD497x44YdjL0ozwcqbYNUBe7EyXTrkUmyNBS0+2qSgom7973tFA5amf7bSiKeWdDjCWdrXCTGNxVWeWI3L/AsvtPZEIU5asDwHzPUB0aNcY4vbPFcDwUli/JaZBNXMrqmCtv5GlJBD4P3muF+5b53Ol4+EkMKSt9aRKvcCPwa88Kr1iHpH2QCyVVggMN9ZXC9ga+DNRaZoja1TutuF5k9DPyIXAI03ngcb5Ybw41aY2EjVThc+QvB66vv8kV9q3NWo5rcfbwxJhMPBhdMg7s1KE0xACO+eyt2p/vqybVBvj3VB2ZLCWB3Xf62Bz4YgH4MhyUu5rqA/k+qKnBijvxTlObrG904FSscMkIOQZW9HKgFUN+E+vK+LpVrSh3jc4kAiPtMF/vsNXjV3KHyRpNtsQQMutCWPs09HIhOWkcpMpK2bvj2KKYgKH2Adny46fwUoD8lMtEWzyhfw4Dt6DIHtimr6b/ZktIDt+L6SDdOswWmYaQTXGardkcd3sVA61rZd6ZCQxP/02W0PYN4PkWdUEtSKi/5es1FYUjvq", "layer_level": 3}, {"id": "076f142c-a9a1-49b1-b9a2-44ffd269697b", "repo_id": "94f96537-e6cc-41d0-af2e-17841bee1253", "name": "订单查询API", "description": "query-order-api", "prompt": "开发订单查询API的详细内容，重点描述GET /api/pay/queryOrder接口。详细说明请求参数（如mchOrderNo、payOrderId）、认证方式和响应数据结构。解释查询频率限制、缓存策略和最终一致性保证。提供成功和失败场景的响应示例，包括订单不存在、系统错误等情况。文档化与支付网关的交互流程，并提供重试策略建议。", "parent_id": "20390a93-7ada-4338-9006-b06206c31398", "order": 1, "progress_status": "completed", "dependent_files": "sys-payment/src/main/java/com/unipay/pay/ctrl/payorder/QueryPayOrderRQ.java,sys-payment/src/main/java/com/unipay/pay/ctrl/payorder/QueryPayOrderRS.java,sys-payment/src/main/java/com/unipay/pay/ctrl/payorder/QueryOrderController.java,sys-payment/src/main/resources/markdown/doc/api2.md", "keywords": "", "workspace_path": "", "gmt_create": "2025-09-22T20:38:08.1335218+08:00", "gmt_modified": "2025-09-22T21:36:05.8530628+08:00", "raw_data": "WikiEncrypted:l9Ho0Yh0r5bYH9QEePEhkQx1xLgR7cJWOoOLq7co4cHY0ebdeF1QzJa2D2X0imxGtmsrOiuhUP9YsGXibvqHt21xfYJ+Z9xP/HB/eJDHVO0cLJXvEQgf1oZTcfgIxBRxsGjAEAJF71NEkbPI/ifOpsejIp7phb3iUdc+JDQMa0LUio7hp0OgOetXGa2Ap48JNonbYYlDPv1wNAE8LtgYel9g9BDEUepsW4Pz5Kjog82rnbBh6he/21ytt3ZNypW4mYZD1f+krJN7l/3X//zaT3t+azcEX0i9OqQVS2/Gvx9vXcrcU5AlsSNlQ+x90Nm08E7myTaO0XWQFCIBzMOG3VHHEvVjvlibp5MwDFMZqT0/NFHgf5gNOVzBLsSw9IjMHY4sKriOm2LsfN+oP0W0yuRNRqY7CwO5aLOvH+RCvZ+mTevn2RF4tgjSET9eBh1uhRwnheAKI/AEaXuU2g84odFdzcu+SUfDnQhirRT1Vw4tzdR1I0Vzy21HCL4urflhWxaHUBrusrsbjdw61Xej9VzxKi23DuEph/9i7iMyERgsz4+htYe1lYeFICVGSatF6SK5AnOaNbrtr5lpMPqOVjhJeUgT9lFlHNmQCEhhIawBwk3DkZSERYUkNGVr7z9DsbK1JEckcTnYxDcJkcM/j097tokFxXXbvQ7xoNPY4qFv6tK3eJ0AVvKrlENkTAoRTC7iTKnio+W0JRg9iZ163764naRIXBkcQ+zoOSYw6LP8c3IeJQHilNXCc6nBwx4jOG1lFBtcpgRsIp5sM9uPxl6MjkLQxqWQtcUByRXAJb8xkm7oo4jR7e+z2n3fEeiG43OeMoCjRSGewrjq2AEKhNyNyrcJm0xN2RzXQRAni7UtlLtr5EHIuwIwdWreAK4z7RAB4jxbHUTiJuCPl2TucQBtsKcXszdWDzWLeNhz2B4+y0zvsr7P5WXCcHund4Y1W+yOoB+KqO67EkTBhZ278vlV0fJ3qwfcQM3u2fjt0oCCLJhXGvv/n8mK5zYcY8bHBh7dpDczoey0Lew34L4pQolErS9ru68cpNXfMi20YF+IVEKxle8BqB/8SBuqFPZysl+ISbYI/VPIGdCZGn0QS4MgYiEuO853gK/3ZD9GqCh/Xz7mfaM6cdzuzDXJogXGg2fYTFFAb5lXk79LmxG815j13R1rve+5Vxgunm8OwGMvPmE0YZjlWBquq7We3qivRrsCc3x5d7OAvRjQYAKk3A==", "layer_level": 2}, {"id": "446fda45-5ad3-478a-aa60-ab45082c1837", "repo_id": "94f96537-e6cc-41d0-af2e-17841bee1253", "name": "退款查询", "description": "refund-query", "prompt": "开发关于退款查询的详细内容。详细阐述QueryRefundOrderController如何根据商户号、订单号等条件查询退款订单状态。解释RefundOrderProcessService如何从数据库获取退款订单信息，并在必要时调用支付渠道的查询接口同步最新状态。描述退款状态机（如处理中、成功、失败）及其转换规则。提供API接口定义、请求示例和响应数据结构说明，以及处理查询超时和网络异常的策略。", "parent_id": "918d1aa5-5ab9-4cf1-baee-3cb7f20be143", "order": 1, "progress_status": "completed", "dependent_files": "sys-payment/src/main/java/com/unipay/pay/ctrl/refund/QueryRefundOrderController.java,sys-payment/src/main/java/com/unipay/pay/service/RefundOrderProcessService.java,core/src/main/java/com/unipay/core/entity/RefundOrder.java", "keywords": "", "workspace_path": "", "gmt_create": "2025-09-22T20:38:17.3691614+08:00", "gmt_modified": "2025-09-22T22:06:05.7760744+08:00", "raw_data": "WikiEncrypted:IsXrw8CHTmYocng0pZ32k6ytDayalvju+jOl5Rs1UAfqxc555T9ps+ccoYQ9VwDEBqzPlgpNlmdQywF3D/9CxUXG6LWyx8qdlmAOxJzZQuCDrshRTxyllAoBNNO5Ui8F7urIEV8nMeOwgrvrYgLGpzFWkMjWtAgtW7fUquRGwzpBVOpHxFSFH6IDvGYvSxD8ZtI8TtNKiv9xVugiquR1vNQtppY7AkZJQEcfMCFXhqFghz6yFLyvCASgCOHdMtOE7dZD3nTug81yg5K94FKWPHfBlUDmajg6ZdCDxKCgiqxoPnLzCtMse6oH/ykCT7QINESp73A/l5Vwm1A7mFxuPYONl2PwZfylUjYo4ZUBgsvMWJlKNmd+dzoulYd4dv7RgRSQVWlfqteBnO8ZR10aue6sci1RQnDGTWnkXOI1Mn08+1FslSifOHX6B0FjIqP5lmQDwaUvi5bxIEZA0JcnznbdzdVkxc+DVQ4eirnflN+vGg6GUWSDWVYuP4Co5YA6gwmJmg6K7zEAkIvHJuxnuO3yH6QFNjA1Q+xqMXzLfsUFMUj+ik0ki7qh6U5ClgncICiQqIo0elF8jvAP8NStn+Rqlwy5bsMefc83fzt5o69666rTH+7Rf7uWuBPaNP7QzYz50Ojcenyiab66jJHD7FNXP43XaFVnXm5fA84dTKvQ03kZzuSncJgtzAUnifjdFVhiTBRQ65+TAz8Emfnfv0LitrZ+ZUgARn5C7Y1HDEopmX96+erEc0UtqWpdnuq0J9VVAZoXfvU5jjniw0gM0nJ7KreQtPaCZFR7r+p5OKzgRWDyX7nsBJ8VHTPjsahVrplWO3OVLq9ktuF/OFBuiT9GeforLQHJ1jtaSWRCzJR75e5iYlA1i/oLQ8qDAMYIneAQZs4zV4wxig7W0ei4vv1LnQ1jcBXLQrmrjcKRy69FSq0GMbPHtgSf36TgAb3cVYstGPwmNE+fSKwR37oiy8MaTEkzkrAazseftezDGPzgTeb2OwjZJ+lwI8JOV9WoCug5qOoFT1PhUmo6r9yP1C07QEKFYlqxzUKurx7cYQbxXKJ8A4spGN+S8pbm7qzGfw2yees/0wj03grz7x3HapGQarSRvGur9Gev+f3J6U/yij42/zqBL7Z28CKlSIMvu5t4a51FVS4XmViayQyC+DMjhsKV/mIgv77SR/npOQzKsTrT3HeONScSbVZQJOk9P4k770A2et8+LYFp03Rthw==", "layer_level": 3}, {"id": "8e37f058-c119-4b64-84f8-9b7e5ff41245", "repo_id": "94f96537-e6cc-41d0-af2e-17841bee1253", "name": "转账查询", "description": "transfer-order-query", "prompt": "开发关于转账查询功能的详细内容。阐述QueryTransferOrderController如何根据商户订单号或平台订单号查询转账状态，包括请求参数解析、权限校验、数据库查询等步骤。说明查询接口的幂等性设计和频率限制策略。提供实际调用示例，展示成功和失败场景下的响应数据结构。解释查询结果中各状态码的含义及对应的业务处理建议。", "parent_id": "52c46886-7235-4b60-a9bc-07e0d54bc939", "order": 1, "progress_status": "completed", "dependent_files": "sys-payment/src/main/java/com/unipay/pay/ctrl/transfer/QueryTransferOrderController.java,sys-payment/src/main/java/com/unipay/pay/service/PayOrderProcessService.java", "keywords": "", "workspace_path": "", "gmt_create": "2025-09-22T20:38:25.4335222+08:00", "gmt_modified": "2025-09-22T22:08:19.2719281+08:00", "raw_data": "WikiEncrypted:5TgAtbdUXqbKL9lWGANcdcxVTVzvHdFr7++I8Pfuh2qqnlmBTml9NPHPS8lYxVqjmxqKxYKbQ+68hJ/zH5nSiBGAvCJjnyJG0sE4G12AFjcPRNMps4twwO/DjhKZu9dj5n1q/E5F6BWGz077jq19uloaHGJPG8/xbko4JhBgRVnAI8tKy9KuuMceURt4HMAkJRF8NWslcLuOQ2JreikdR1U6CniXArE2yhFrdRHs13QQQMWpQ+Wcn3MACG2s1qoCqDVE61noOurc9jOI7BzVGFfjZzhPsTC4n8iE6iHgWLbKULXepu4pgITZMB+su1E2EIu4wsEqI90breUWoRYrW/OL5GU+oxbXjSLtmT6/XgI3dbfWBFwrjtJqGG7XuUCjFbMdHKeOrM58TNju5zJ+7AnzJ7OyndyDVB4VGidTiBnkJtsT7JkPpesFXrbr629M/rYFdyda4lHdiQ77ENRnVsmBdCfGgOUhOIWyo7/vJyEGSRDpjWRu/ef5zqYMFWd+IUQ1VYw1ECPTGVapKp/Ebf3G0Y4E9UwBaHDLVInPKoPG0OGSJpGHnKTU9RcBNnkBDnQMnLsxyDUDY1R39cDcs7bLEXQCBK28kvmVydvEf9JKLta93boLvA1pmVBJLPa4gyCRpVqX+Ap5p92PTbrt5zkm2pmYXIa2XENX5t3BoJDe2Y7ieufBsNx38iQDUwh/UGGeU9676n53CtKHenueTtE+WzGVIoN5cZh3N7I6cLr8kAje1FFmFxVrfHfa/fHTdjuAP9CW5YdS0cjGeD3bMZsnmjHCO5XKsfdqAwkdI06695t23zyS5XsL2pR6FUIhDx7GxgwpVf1h7gRTgYpk4EpGRkSGHZKahPdKt1D3iyODokF3A+lPYyF2O6L34LfCWgtoqypZSIjQALOGrUfKXhCp5Al1d2hQy+iSzB1JS2s0j4gl8TFPPI5vKPoRlNWBg0FwzCeNAXiuREWHquMV1jBwqdItqws4RHL81KNeOUNqEGea5koG2yKe6y8G0rgGp6QeHY9CvDoFY3lTuLWOn2LoMwJjZ1hqOAUihc32D6jgQVM+vcIGdOgMD2JHUGrJ", "layer_level": 3}, {"id": "e6442f1c-e8bd-4d4f-aee9-0cc3134f99c1", "repo_id": "94f96537-e6cc-41d0-af2e-17841bee1253", "name": "分账结果通知", "description": "division-notification", "prompt": "开发关于分账结果通知的详细内容。详细说明支付渠道（如支付宝、微信）如何通过DivisionRecordChannelNotifyController回调通知分账结果。描述通知消息的验签流程、数据解析过程，以及如何通过PayOrderDivisionMQ消息将结果异步传递给其他系统模块。解释PayOrderDivisionMQReceiver如何消费消息并更新分账记录状态，确保分账结果的最终一致性。重点阐述幂等性处理机制，防止重复通知导致的重复分账问题。", "parent_id": "b421b6f6-0693-49b7-9034-6061411d55c4", "order": 1, "progress_status": "completed", "dependent_files": "sys-payment/src/main/java/com/unipay/pay/ctrl/division/DivisionRecordChannelNotifyController.java,sys-payment/src/main/java/com/unipay/pay/mq/PayOrderDivisionMQReceiver.java,components/components-mq/src/main/java/com/unipay/components/mq/model/PayOrderDivisionMQ.java", "keywords": "", "workspace_path": "", "gmt_create": "2025-09-22T20:38:34.4971656+08:00", "gmt_modified": "2025-09-22T22:08:12.0064685+08:00", "raw_data": "WikiEncrypted:TfUUykgSV0NZhhl6ftJ5NEKLsM5WXLwKiq6bQGKr1EsYW9y+anbk7B4WOHz/IoYEF4NUN48QVhvKG2AGNFOz4L2gJfv5AdaYBTtn09g9ySOrBIErY2bDz9KEyfI9h4+X9CdTADT111pGjqiv8altUtemsPbQJT1YAA12gJDTF6tA+/ztdjrmZfQ6I1Ni+dKhs0TOaPnjBy0mUG7gbghl+3gCV3iSjSGHYCCW4Ki3YDqua/7aG85GnJHgDXjQu9zUbuVibphw7Ov2evWbqtk5c7Xu3oIJfx2AHxxHA25xQv89Ir1BE6K29MOjkBfYX3Rb87Vup8Ab7AsW3+KvB5AVxqsU250MeZmgHzh7CpUM1DqFCvQZ4Mdecv+8DZ5m0yMlZOnqKCx5UboAOlUpxoQk0sgahl11FEAumEecvlGoFHlfsr1EZbLz9xydyHdixyvY8X7J+KlA+WW+10FlJGOrjbc6/wA2mxOXvm8Ymg71cvcSYHxZLNh52aP5qrBV6mowmAH4oB3b6+IV1VI6s09HLGsPCfomEhYLNnuHAhsbwPCmmG94jmoNi+JFR9UyexpysQXmrs70UwuEubFHKep/R45tlNi8fjTfbPW9yU2PEb+4vKSgJuk5yhjGXGInDdVePv1+7RSbJmGcyCwtjpPTBp9ZkDsnkR3LJddrDzGiO5FohIAfW1wp2aGVCBs73D4ZpGaUJ6ppxp0Odmzfqk0obV+WJfd4LBMVaYOvOPG46tOjUAWYTvPEIMY1dtBdHpx/SAP4hg/hUAgkDDtgNbBs9GTB2YWK3AW3N8F1sRVPIUKN8lDdYtAOdNNZC6C5h5V6V3PHhadzdPITXOI4WcAGQ8YU4kF71mIq7Ah6SnFlLKWHBdylMMRNWKKJO/83mOoGJ7DqxFQIiNYddv9MPDBZ9M6l89SuKjnkxvsj5rROSwFmkt7gSeDfNWDD8GgNKPsoK8ozrf7bSj6boyPk3gPWsEgoQ++2z6R5w/Of35X29vWBg7B3H0tRCa+LHGj9tLmMlAUAZ+6mQNyQbJk9VynUZYyI+MCKfahy8UGh/lHsMGVgeyfZY0Z2/1eofImaF5+oJAPVD1JmuTt0KPxsRruC35xSGVVYsV+vBJzqj9KL5GNCd2FcX8rCky1N1u284uiUtrb9xMW8bDp8nVZJRHzfMnjucsjJixIfvQSYQrO9IBP3IkutayvdTd4pj7TO8SAqrBIoqzeAIWXZQ5JEw/oo3Er74sgHV12janHdiOVwo11WEHNygwGzFHN30PH9N29p37Wuosio3qhAaN+zZpZoxmlX9aoCfm/pS/0HZdOuI597/JnBegH1BF+ZoUTvtBmPphGVL2Yqb26zpiVPU0p3+UkMNaK7/7dHo7O73fTKV9M4FsU/zMLaNI2eD6J276lLqOQsRTd+KWc2F5w2SVBL2A==", "layer_level": 3}, {"id": "907701b8-a606-4a7e-98e3-a6edc3767588", "repo_id": "94f96537-e6cc-41d0-af2e-17841bee1253", "name": "分润记录管理", "description": "profit-record-management", "prompt": "开发关于分润记录管理的详细内容。详细说明AgentProfitRecord实体的数据结构设计，包括分润金额、交易信息、代理商信息、计算时间等字段的定义和用途。阐述分润记录的持久化机制，如何将计算结果存储到数据库。描述分润记录查询接口的实现，支持按时间范围、代理商ID、分润状态等条件进行筛选和分页查询。提供分润记录查询的API示例，包括请求参数、响应格式和错误处理。", "parent_id": "574d46ea-ffd8-4b8e-a21f-24c0d9111838", "order": 1, "progress_status": "completed", "dependent_files": "core/src/main/java/com/unipay/core/entity/AgentProfitRecord.java,sys-agent/src/main/java/com/unipay/agent/ctrl/profit/ProfitRecordController.java", "keywords": "", "workspace_path": "", "gmt_create": "2025-09-22T20:38:36.2751478+08:00", "gmt_modified": "2025-09-22T22:10:10.4859486+08:00", "raw_data": "WikiEncrypted:3KJFs/+fakBIzq/vLbWLraxfIgYBy4kBRC8oePd2M95+QsZx0efVtwtDi9BxhpTMJ/Y/DKfJw787+GB1kD5qvJdDNLu272MXLCBDZVfYXrLYz3NAIjq/dtsHY+X1ure23kxV61TE7IXDGP5+jfoY8BBY5vVWRiJVwr77ZYFpoxTOHcowsE77tgAzn7/F2SF2ox8otcwebYzm7mNPzp3Sug8dO1bzgHfU+k9L3PzF4fCMm8WCZ0LFOB2ZUDbEJpSZM4xrtI/3ke+hK1jgO1AiNzVOcgJoS6PGvbs4R/x6V/0/9vkEJNzGPOyCjK2mwQmwv9am/Ht4mXacQz/rPAqA8bHOiFy6svszkYvYIQDki+CIHi78ieR8EiyrCT046Y8KLYjKAUktzdCipEqU+BLnf4h/AUih12gZ0EVGmoXg+NjXkmGIHxtmMJdvC2I5gAbzvynMDd5ukGIfRhe+meFXOmyDfWLdJlvnViRBzQyV0TwF5VPp4qafU5t5S9BMu3HmkRMWhnWi4LR/OJMMUgQPpBfg2YxJwm4orP7PC4fSG7sanBZ20cPz+5vq5bEbBnoDaCzHZ3fY6SSeG3Dqi3NIWGpQcTEzC21XbHWrfyO0EUZgzgd/sNC54H1UqI+IX1XF2T/p35cargyf6ZgXp/+WijfZfH0qF0K1IWVqn7dlpBlwsV10WfwoNZLgi9JK9PL9MXB8PVE0DeAFaQtTBarQvvB8UnqOMj81AdcPGVkjYHbOy6TL8dZfcC10xPACJwuQUIbUQIKJvhdkNSEKBG7Iut12jhls0K2q015+v0N73vCGlJ6wxPzIVSN4nLJ5wyC8BDfD0gkbIKj+G1ytu2DpAOvt/LNzG+bzhUP63hAzKppRC1cuwVqSQ8zjArQeG+TFWAafvaNpz81GMprAgtPE8Hj3Nev3tqsjN0qzECaPzWCLuf9U+UYxctXDjdR1aEaX5L+Fj4LgsHHsLTaoJ8LL4Zh3p365QYLtz2rB0/am0NPabWy9da8UxxT0MPJ4bdxHqvxOzXXO9eC1MemSdP9CHotHHesl9t2EDPVxOVfD3zgLXc+YyMBoMBW+KHBMlHt4LBHKC5TD5F85zEa6dNJVwvQKoqY0kggxm2ZfYHngT38logvNAfMsrgjN2X575Etg3ofwGJfWluEmhwF5z24diQLGhlg+arI1bmSkTqo/Xmto96y+mg9kyKNmQ32WkUxP", "layer_level": 3}, {"id": "15f52f6a-6c1f-44ce-9ca5-711c10f5e6b4", "repo_id": "94f96537-e6cc-41d0-af2e-17841bee1253", "name": "应用配置管理", "description": "mch-app-configuration", "prompt": "创建关于代理商系统中商户应用配置功能的详细内容。详细阐述MchApp实体类的结构和字段含义，包括应用编号、应用名称、回调地址等配置项。解释MchAppController中提供的API接口，如应用的创建、修改、删除和查询操作的实现细节。重点说明支付通道配置（MchPayPassage）的管理功能，包括如何为商户应用配置不同的支付方式和通道参数。描述支付接口参数配置的实现机制，特别是针对不同支付渠道（如微信、支付宝）的参数设置。提供代理商为商户批量配置支付通道的使用场景和代码实现示例。说明应用配置变更时的验证规则、权限控制和配置同步机制，确保配置变更的安全性和一致性。", "parent_id": "67d56238-1329-4c27-96f7-320b6ff44c7f", "order": 1, "progress_status": "completed", "dependent_files": "sys-agent/src/main/java/com/unipay/agent/ctrl/merchant/MchAppController.java,service/src/main/java/com/unipay/service/impl/MchAppService.java,core/src/main/java/com/unipay/core/entity/MchApp.java,sys-agent/src/main/java/com/unipay/agent/ctrl/merchant/MchPayPassageConfigController.java", "keywords": "", "workspace_path": "", "gmt_create": "2025-09-22T20:38:50.5533757+08:00", "gmt_modified": "2025-09-22T22:10:58.647825+08:00", "raw_data": "WikiEncrypted:naY8PhELSlVZyhrLUDeY4GtJIWseykegvtjXtRYZlND3FOVlsjrOvLXuw5/fA31mXhDGn6FaavZ+VQ9MEzqHQzvcDchFc7+FOiBe7Vyw5/9z9Pnk+p2FD9lmzwt2o+KHISqNWQg0FRhendbo06pbHTePQfybBmhOpdsMJikQvzFUWh6CRzEvCoxVJ2X2pPldJxyTVF2+JxHuzWAiq20xXCSuZMunMQGGbHMx8TW+BGAbbcNuaDbRhamSJyAOTXzw0yKXg0x+/PIiP2PH4rvDpiaKyBxpKz89T7VGc3zCWbjGyXQYy4534CDj9HH13PWQbFQI9EewpmRnEhvJhgPpGvahe1mFFUyypCQiUU2V1uc19ynIb5oPGhKtfxBIvY0iMukvG6r+9SGkAOEyMhjv2znZehZfRiUSnrDovB0b9i2ypCsB302RI19zdH4qXPwHLFe6Wnh6Pj4LpiTG0pAiHKuR//MbNzukarDs/ORwHgYQTEWjX2iPI+aNfooa4Ank+Di45GWTKGKpDf6mkT3ZPapeuHbY1pYng3Xnv3RvwgbyPkaySPfWeaEVtWh1zxAgkb7ZnRd0JA5oPtkOIM477RMNp6yWip00HkTy7Nthm2+BwHk9Cye8GCJNCgFKzySqsi4CHmM/PTuPy6OxN/7MMOnr7tzP4Nf4MqNKpo1iIDMXnNQJXCO5rac1I7tCdsd9MrHNgtfEDJkt/9mDFQrRZMh57qNF8EFzFTre7wKkCxCkurHsfdju0xxrQd6Q64PRXEu9AVu5/KWhD6z2H2DxGeEFiRhc6ghxcqycZKsCF1p+AhN0OD8M+dK3HBvAO71qC4QrHpVAHw4GqXhgftiejFeKJIXmPg2ktBNax2kM3w3H+lWzbHIQCOy2Exu8boEdlWGR2Vo5Oq2N7moeH0iDbvEmTxsIvxz/f6FkTZdPkDZrYfjnmJYcVFXYliUnHZOD+gXKNlnUwTg4BV50IGryPE3l8Ns/MjcePMukIhEU3Ie6o+3/0m6hJ4v3ZlKHgHpLReYKzPgdvAaSQqd62d7LDCucyR4/t451fEKPpRA7p2J4yjEHHpmcC62oWB5vWqaHjDEGBEZrhqg17FesWbJDUCG4ZEn/YhPwWjm4pYSwnD1NAabXEZPlFeFFOmALwWYTyzRF8cZkwo8hFXTEx1C0ZC1WRXV0U/ig6CvIk4t/NHRBMx1Vg+GIygU7NeAv+jcuVISZsGrq2E9uCbC8kWpN7keRGwdXT4U4eFIqEUWnTMlXre6K6GalL84sTiDz+EuablYSBwnGkGxyeBigzSQ11vxM6s5HX4EPLiB0VvIbLZg/2EHxYbQqwbdKjpwaWhEHP8P8ibMf6KpNYpS/gXgzrPOyABRM8JBqhCDbwpz/ssl6sHZl0Pvwlh5OrEEZwbz5OhednYNrbTztZFCFZBucp6phU3elPSSRa1tY1/ISnbGlQaeyEbMYdyrmuqOMiwql0A+LbxdwdC3Fmtb2WSbbg7+w6PpgCkHoZZC5m05G+11n3AYFilpMJee4TOqGuqm+utAnP8OyNZ28HhfwxEhIM606LcwXGZNp8QHXblgZOZwva/Zws3ftxwBxDSbGlO/ByH+AEfx/TgV0e/Cv/hyxuibmr0+oIxLwIrsr2rw24c/xCCJ8CbPAKdZFnEbdKjx2g32ETQm3lX3rS35Mt5ntN5bJITw7C3I5W4Ng0Fh44phyq5jKlRqDvwwlABu+/6k5uezGZld6dqzyWgmayc151+2cOJ5W8boJT5nVN5Cdp6yNooogYxJtoW9KgrPwBFza", "layer_level": 3}, {"id": "50fdea45-f53c-48f3-9caf-c2ed64516890", "repo_id": "94f96537-e6cc-41d0-af2e-17841bee1253", "name": "应用配置重置", "description": "reset-app-config", "prompt": "创建关于应用配置重置机制的详细内容。详细解释ResetAppConfigMQ消息的触发条件和系统影响，说明ResetAppConfigMQReceiver如何接收并处理配置重置通知。阐述配置重置的业务流程，包括清除本地缓存的配置信息、重新加载最新配置等操作。描述消息处理过程中的线程安全考虑和性能优化措施。结合实际场景说明该机制如何支持系统的动态配置更新能力，确保代理商系统能够及时响应全局配置变更。", "parent_id": "fd539ec1-f638-4212-9ac3-c62dd26de122", "order": 1, "progress_status": "completed", "dependent_files": "sys-agent/src/main/java/com/unipay/agent/mq/ResetAppConfigMQReceiver.java,components/components-mq/src/main/java/com/unipay/components/mq/model/ResetAppConfigMQ.java", "keywords": "", "workspace_path": "", "gmt_create": "2025-09-22T20:38:56.4426069+08:00", "gmt_modified": "2025-09-22T22:11:50.686901+08:00", "raw_data": "WikiEncrypted:T2VlUJ1UWErFI/XdWMhiWdnO4+l7fRxIkNokA+kGdEEGWb6eFjp/RDn+GJzhNtz6nXek6meeYQLRH9x1wC/kKc/AI6nLrBVAFYSwZRIp2fq1huAiEvAb79cjvPGbzxr/gV1FSJ4Getm7d0nZlhVkgYwuUMoFURWZMiTpLFxqXqXEDIwBrFjiftngxbC3xN1I7uNYTEvinN18/Tis+aWVz2ozDf+20UUTxn2HDsJHW6wMbFj9CP3vJ58XcP1PQ0EWrkxaI357PdISNBZVVuMbu+wiVotVOb4oR24ShM3F9l0Pl/PNeG3D+UGiPiAn63KgIajyc7TuX9C04JX1suk+ROfE6jme1nGPmjibM1K29fr3Xb2aBG0qCFZDbUR0ScokbMdPXrAbM8HpdS5XmZLwKXJ60FbgqxzYwhDhSPiomenA0CrkPkwShWA42jfiMl2/6mcns/lzOY0e97MSd5UJ6RiNqNJl1ckpdsQ18hefQDZ24F8msHrPy9ZK/pW5Z/0KRlg003rwONLtGLS7rxoJGuiuZLIyeAIja6S36tJXvi8DEdimQqcnhaAZ1Cpyq42pXw+FMOCpOV2ibcl41ThRjxatm8n5D/PkbBHeYbBzcIlpjL1Rcw7iLei4zxnqGVjua8JMxdg5J9SUCDlv6rFS9TCWy15kQOb20KbzN4d50uyBKKqVX6CYfb1M/2mJixtjDo7UTYVTYYIJSfEYosmy7PAyo8aXZTEVWhfZdrYhdO4sQhQJU2am8056Q6sVW3GRmFKyrDQoPw1/ns+CvykOZ7Ld5AbjAziYOKZ4wKN5inbE1/1Eg/WrrHuid0BN3bmQHJECBIU/ZbEqCTQnAQbAiCKxyGAvGC7d+KwxfQJ9CKTgVVrHbbBHk6qdaaMRqHTJtL9skEm1boiLGN/40peDq9sbN+amz3Q0sJl4zvrW2MFeLOXzXrvw4qSayGoCFajqRjBILk+npwt5U8CaVkydcsFOCtC+4nbOZdwYUfZoRyMVfWim4ncuD/Gbvtxl2+UaU0X4rBrZAQuU09XMBQaaCjHEAtbN8vnEqP8FdKE78TulycVUlDU+YPeVHwfH12jHHaCVGC+fz01RIpx2PDs7HSmRwTIx+xVl4iwSwXQ85OAbxN96me4swFBjGLeq1tAynsn9JXwOyNuHNVEhp5dmvzV7wyfvHRA7we+PsPmVG6kFopz5tp+5Qpwlsu03uQUrUA7bUynZ7Ttp9YX2B65p3g==", "layer_level": 3}, {"id": "b9abe1e4-88c6-4131-9355-45ab354ef2b0", "repo_id": "94f96537-e6cc-41d0-af2e-17841bee1253", "name": "目录结构说明", "description": "directory-structure", "prompt": "创建关于项目目录结构的详细内容。系统性地解释每个顶级目录的职责：code-gen用于代码生成，components存放可复用的公共组件，core包含实体和工具类，service负责数据访问，sys-*系列是独立的微服务应用，unipay-web-ui包含所有前端UI，z-docs存放文档和脚本。阐明这种分层结构如何促进代码复用、模块化开发和独立部署。为新开发者提供导航指南，说明如何根据功能查找对应的代码位置。使用项目结构树作为参考，突出核心模块间的依赖关系，例如所有微服务对core模块的依赖。", "parent_id": "", "order": 2, "progress_status": "completed", "dependent_files": "code-gen/pom.xml,components/components-mq/pom.xml,core/pom.xml,service/pom.xml,sys-agent/pom.xml,sys-manager/pom.xml,sys-merchant/pom.xml,sys-payment/pom.xml,unipay-web-ui/unipay-ui-agent/package.json,z-docs/deploy/deploy.sh", "keywords": "", "workspace_path": "", "gmt_create": "2025-09-22T20:36:07.7187115+08:00", "gmt_modified": "2025-09-22T20:43:10.3099232+08:00", "raw_data": "WikiEncrypted:vEO7Ijy2dU+nEIDHKdZkAfwFWhgNVyNCKalPSpB9ioLOdKPffBi32fMi+4j681gqqIl5fiSTM5hfsX7Hogob1PuRihwt7t7DSrkyUYN+VEqL7b8vpTUB0vRKI830Ok2oFZ0Ui94+gWOhYGqBqfF59bZMKjjI7KhzmsVndIe8kjH1ZEtUwRKmN1Wajp2FZPDDsIfSYSz507NBbYvQ+fOKnvNpWK9ftRSlG589+5S/OPCSPCG+K4AWVc+bzlgfVrwCU2OZ73vua4uyeUE+Yj4zMgaGHx16g2TdmB4d0lt1u94fQQpa2v2GQKFPhDF5yNwfndQu4Ay3qy09c9l3fBTZkhokac4AhenfMgrC1S1b5egE20uoqc7n9RldSIXwCozfL3EXwlTgxddL6WVaIgm3lo8/uIYhKl/VA+rZW6Mge2uSKlTvXzy2jWfOFNcX3CJnEV2shSNmx+3hCTx/x3XppJ92Gs9P7Sy9tk7TWqVG+yGI9nqLvtZL2TwwFunW+aHIi+R1NyykTivKrPrW9S2VgtLug9KdDe4MTz8STmeUmH3o+Iqde+4/uRcvdpp0nTbDJ9+J6zg9I9UAA/mvyEyUxfCPVS4V1A26WT2UiZOTC1dRBsNqGEWT4h9KhPVFPXUPV5iI7WIh+b1EN8A/TU5VEaBP1891bEqZBVKeWzGkWRB1+87E0S/PoNDKdmYFhR61ISdGfqCbZ1u6Wf5DmSIok2JBoJQ7l1vs22/rUq02dBKuTlC/K9v/ciuTNGxGuhf7UmwWj9qg4U7zf2CJM+ziToQW/gVgfM1IeUen0yWR8+0JeP3U3FXRgNDLYeOi0z1nNJF4AVybxUSaJc7eVooU2jD/ewOakrRPZIiwnWaXsY9BbVOQmgzHOy2aqIg9jGnqq8ZKQdRO14fIuDxmiucVuhMnwhAVVu0b2nh7gDsJlZDTr8Zx7zn4C0MX7z8OO+hr+TnG2VGkSy2FoL2WOgQMewzLh7c3MmQDwJwKQQdDRi31T13CR57ytzwX65C3LvbDhlw0B/c97fx4L6a0VSuWVqgSXiSR00poU2BLK/ddx+8940oUZisMQEyOTHznRsRHuragxsUtu2KkKdmOyJtWmjIlBaTVKxfWi2hdE92XLsq5Wdj7N1lroS2i5H8nVxoc6MSCE0HOsdAW9CrqF/83vMI7tYmCqIp9sZDT9uCGnaoNTYuB1VRhBN5vx8qN8o0aIPYgcdmyuDNDJKrvm4vCtIWppiYrue0QLCmuTGc3wP0O3FNzTXSPjmp1Hty2LIhJIzAF125VJpM2ObWUS2JDLjva6/pbPyelIywyVNpFZWVkjo4/RoCmwztWXp7XQtMfWM+abKMj6RPKRChZp00an77it5lPJ5yLLd1U7/f3loZzHVMpa+1OYd8QaNHdMFU+cKHXFXXRu/uDjMcKowAfgjCP5n1ciA45x8Pcz6+Y2fb9KRZFR4TRHp0N9j1PdxcX", "layer_level": 0}, {"id": "b6853ca4-49b9-45e2-a077-b5608db8020f", "repo_id": "94f96537-e6cc-41d0-af2e-17841bee1253", "name": "运营平台", "description": "operation-platform", "prompt": "创建关于运营平台的详细内容。描述运营平台作为系统管理中枢的角色，负责用户管理、角色权限控制、系统配置管理和全局监控。重点说明其如何通过SysConfigController管理全局系统参数，并通过消息队列（ResetAppConfigMQ）将配置变更广播到所有微服务，实现配置的动态刷新。解释其与代理商系统和商户系统的数据交互关系。为管理员提供用户权限分配和系统监控的指南。", "parent_id": "6c28c636-0567-4d46-9564-c10d609484e7", "order": 2, "progress_status": "completed", "dependent_files": "sys-manager/src/main/java/com/unipay/mgr/bootstrap/ManagerApplication.java,sys-manager/src/main/java/com/unipay/mgr/ctrl/config/SysConfigController.java,sys-manager/src/main/java/com/unipay/mgr/ctrl/sysuser/SysUserController.java", "keywords": "", "workspace_path": "", "gmt_create": "2025-09-22T20:36:24.8740977+08:00", "gmt_modified": "2025-09-22T21:13:39.1123907+08:00", "raw_data": "WikiEncrypted:WmYz76ZuIeWtQgFK+sghtdwARNAT3DqTela1x8CDibIBSDKznBbtg0v98QyxlUGd6PhKJ812E4pOHRn65cAdKDK8mF1fANookBae63sgbhNIXfOgstBGR64mqciQMFLmlhcZyXmSxgji5lNTJK6YUCWepCjMl7GbbnY7UkTouzhBMnWE35grEIGsndUUXWGg+3YoYmYA7wzDAwSFPSMDA7M0q3CJ8SFdU+cco0j8QTdioxl5zoVvwMzOOmRlmVDNUrivOyHE5pQsmCEe3PeQ1mXm+uhzlRK8dT9Kiy/rK8enRmgvXP0u3fdxgUfXx8qLHuyo7CHYb/g/rcLPF3Ct2nf2vfuJqL6cxr35bWNYul/IYufRTyENgaGbknnLz8NPV2vscVK2DZ2SQM8Vj+ej253zOlA45e7le9LgqRJ7s14WnfX78wLfzSW6hKnbSkADUBiVv1EU5Qg86G/kD2d33k2wtJneR2Rw3PVu6+WpZV8cBq7xNWaBzU8Q8NYNXYPF7gps9PasaWISLEMXrsXYELsAh2UMg9zzeXczMeZ1oXb0Q/7e+Z+8u6mxunryL0Aoqv/3RuqVcLVnBp+FT3GJXN0fkEXH1L2GfHLuBNFuPJzo6gCcLCwfbANxvVNrwOZKlKcGKYX1GUsWmfDnCLDawR1JEDfohzw/0E5r94ZMlxruMybWAaNG2/65z1g95gYMg+kBwiT/9gNCC1D2Hqt8N6kAGdEopVVAAKpkejFTyzAx+t/Q4b79G5oiP4E+PngARsqp447CN0xc2smaSRl6AM5bX7aKYyjx5XyHXJrHhna3NO4g1vHGehGNsSKmbxqbxcYzXSTkw7hyLpwfK1qz/50ydqy55nA8mt80QbYL5CyUEFI9QWFi3lAhjLIM4ukpTW3mC9Z+bVPDX4sn7PlleBHoR5+StpW/K5I96WNIchk6aVJ5ZLWDTDQX127Leg6ZB5HvpJTota7KhXU68qzWHBqC9qI7YUU1NCdkZfZQtSFIFjt9kRAy+sMG7kHUricKDNrUBtYlRXtRV/AgNHMGQl++DX+lRzp55PisdSkveq753foHYAt8TQFyiQhS6BMxSSiG5tdV3zfWuvdSmj4eoX3js3fCKUknq318FuN/2gwKZ6eb/IReb9GXgP5Dl8y+G/l47nZzvtjcPhrWdvbPsvJtKRmqco7LpmNbaxvLxuaqgDiUIOS/XIT0A1opv4ox6iyp/M4pWEewUv1SadV9a3NG7vQ4uUKAvjVUraoAbq9xoUDcg+PFXrCAbD9sy/ZzJeA+6Ttvd3rdYLVCYvWOakaoY3o6sRMFaZNHqB/95nE=", "layer_level": 1}, {"id": "1b2c50b4-ddcd-4397-9764-7b5660cf6ec5", "repo_id": "94f96537-e6cc-41d0-af2e-17841bee1253", "name": "运营平台前端", "description": "frontend-architecture-manager", "prompt": "创建关于运营平台前端应用的详细内容。介绍该系统作为超级管理员操作界面的角色，涵盖ISV管理、系统配置、日志监控等高级功能。解释其与代理商前端相似但权限更高的UI架构。详细说明ISV支付配置(custom目录下组件)的特殊实现，以及系统参数配置(SysConfig.vue)的全局影响。描述运营平台特有的功能模块如权限管理(ent)和系统日志(sys/log)。为系统管理员和开发者提供配置管理的最佳实践和安全注意事项。", "parent_id": "776d5083-fe44-4ca2-a556-e434cfd87497", "order": 2, "progress_status": "completed", "dependent_files": "unipay-web-ui/unipay-ui-manager/src/main.ts,unipay-web-ui/unipay-ui-manager/src/layouts/BasicLayout.vue,unipay-web-ui/unipay-ui-manager/src/views/isv/IsvList.vue,unipay-web-ui/unipay-ui-manager/src/views/sys/config/SysConfig.vue,unipay-web-ui/unipay-ui-manager/src/api/manage.js", "keywords": "", "workspace_path": "", "gmt_create": "2025-09-22T20:36:39.2661345+08:00", "gmt_modified": "2025-09-22T21:16:13.3749391+08:00", "raw_data": "WikiEncrypted:rJ/rIw0gVau8jPGqKFBAsAycLDB6WVqwg825dG7e4d73YuErW8wzYXwB5C/J4CgZxaXhs1suAQtC/noIjH/q8/aVIIKNweTJBrA2MrCsSEuoZDyW6VZzZzRWt8zl+WIlkkvvWnxDm4mIcVwMpr6TIheCA3++GhKN1g0Wqe91JtSlFQMISw5283l/4yH2akSxnBgqOBa696wmo5KZ3mG38jNmkK2n+5sZRVgGzcVxoOjevJcjb3YWDCjK7d1l8VNjApaMGdlp1IWbYsq5OjGO2hhCY9jvU1oYPbkSJvLTltLWic+3G0lxpIK8PpsOWXdWdEH70PHM4/NE+tQ20yl2j+Z0Ih1rX6i9etWzx6ivJkvtEjRr9nnC5ggM6ExpbdKZWz8IdtFz7DpfkRAGoVT2Ffm7CzkPOdr9N/duf6gZBOZduJi9GbI8uGUaqq8Y1/WUwbRTd+xmnLphAcAGf5C7GdS1638ZsaV4rlyCOaMnCohseufhjyQPaKSk+HgEgLZJyUOYsiZmXxQJLuXv+4/oKDlewxxKWD6Wd5jz15csc9PriY3S+eql9n62wHGyN6V4zPp/XerYTG999X+Olb5fty6/p85ra0YETa+1JI8rkP21BnXwuA6VhqSQR8YHz3EV2kjzriwyIGJCq1OvZ+8NZxr/N9UJu+MGMHWWo9n1zs80VUN8A88l8NCHdk0YcDdWXcI2m6mR/X8UtqVvc7Cu+89DKq1BhJnTDl1uAd9ib0laVXGLMx7JSweZZrvCRtNmxgB4QchRo+u2nvjRdBr3jNr+CJGyyYNtN5alUQ5UHgJaMGJMsOiiPajOXn37uWdzu4nyoVOAaDlwTI7TtloT+VfRJk+j+WpFUfIeGKA6cn+Je3heZCU+ADD1jz7tOcOZNNzS1JZ0K3IlKvNtIuS2KjnwxIjOnDrojDbYpFaan1EtjPE9+19LCqvts0HEm0DgWPIm6ENdizAwJAdrucUXdrpDuzlEvlY4GL19gUatQO3Br/lEdpCFTFFJpQv7mRR204317vpZjg8Ynaev6X1HkkhFH8eui4Wd1d9NU5GLc4nHwZI/yULDzVpYKyOH3z/+gWgmQMWP6zxq7Ne9jq+wGvxjuWlkN9muvEBH2wyZqA0jy0/ow9EeX+cchnOMw6/auHcfGBTZjdBF/G/U+MLfTooZ2Kzo8T57Vnk+sY8BBDU4nTlhYSXytKj0mfe/3IW+GHzWbAvzYQ4q29iA8ZHLzGy6gAyLSBTK9yZYA/G+iObGis6W1Yi8mAWVdGySdI7qFRxMXAbIOpmsGXXeTI5IBG/3IS+/6WAxJt88KxcmbkxGLgwpkl4opMZv19RDsOHM7YZYoaw1ghGqzCwIg8nRtQSPZXvaGN4MO28EmXlWhKAvrVvg1aeo52yicwKSizMWPE7b+Gn4s78U1ZEgJkmEJw==", "layer_level": 1}, {"id": "6c31f0b6-271f-4662-94f7-7a7c00581bc3", "repo_id": "94f96537-e6cc-41d0-af2e-17841bee1253", "name": "多级代理", "description": "multi-level-agent", "prompt": "创建多级代理体系的详细内容，涵盖代理商管理、层级关系维护和分润计算三大方面。详细解释AgentInfo实体的字段含义和层级编码规则，以及如何通过AgentInfoService实现代理商的增删改查和上下级关系绑定。描述分润计算的业务逻辑，包括利润分配规则、结算周期和AgentProfitRecord的生成过程。提供代理商系统特有的API接口说明和使用示例。包括代理商权限控制机制、数据隔离策略和报表生成功能的介绍。", "parent_id": "323cc3e9-9355-4b42-b596-ce729a56c076", "order": 2, "progress_status": "completed", "dependent_files": "core/src/main/java/com/unipay/core/entity/AgentInfo.java,service/src/main/java/com/unipay/service/impl/AgentInfoService.java,sys-agent/src/main/java/com/unipay/agent/ctrl/agent/AgentInfoController.java,core/src/main/java/com/unipay/core/entity/AgentProfitRecord.java,service/src/main/java/com/unipay/service/impl/AgentProfitRecordService.java", "keywords": "", "workspace_path": "", "gmt_create": "2025-09-22T20:36:52.9642319+08:00", "gmt_modified": "2025-09-22T21:17:07.1582418+08:00", "raw_data": "WikiEncrypted:Zt230nV3k3ww0eU+N8tTkmoveeMnTFwvT/WV0TXpZSI/+S4TA5zZV0o1FmGjYamq6VL3wz9g0hfUPtMRxcdxhEjPx7ojl3vldT+tuxhsQLe+NDz9Vvmuojvpg0T0FxKjAJJiNU/OIv675Ncq4Cgoeq0yvmHgrhkxppUOmm8wh/4g49WA1MYU0r8iZq7UyuXfTmBDG+LWhgQoKWvbhF4Hb29vT5HCJs5NXl+LXP3qlD2bweVatljLZf6hxut1BKOVt3rqwkoGeXsK2Q6XEIfKdnP3tL6tcxExIdcwlg11nptLgMGQzA6J5PjXB0TRCLLHoEqEMuxys2DaB9tvSgJ+8r089axxic2FF3G3AZWd/JAM1sEn/J4EnfAX6bFr4gSyMfcxPdOM9jCjb9rdg0uI1BtmSPl1MTC8mlf3lhOBMmGd6Is7Ry4HtKmXU5nlu6cfCDx/JBl9Xe76AgkxbPPj/U9DjFr/K9KxTB56JQ22BYF6zDET8Zb+ipr0N7A86oA06ivXo3zl/X3bPG4AJR+OYH349iJhsJye4TVsREnKgqDK+o0a29ayG+t/Q/CAosqWebSeMrZovJGKXZAwcLbFdG8lvgjWmr7bgi0S9Kr1vYVXK6yN/Qlu8HgotpaJBpj+kxwP76vwib6GSgHaiHu/Ef34Xq8cgTVY5S+dpTWSBHY76RdCieixqyVutUHuFk6Cjdsf5g3U6U6trQOKOaoXcMnHVzvxcTmI32L6+SwDzN2OEE+eDypstI0iC7VZpFh0Z4QraHLAoWBB9cD1Bzo4ZlVj9uYqOwyC8GMGQqkZ5xHgtWBocuBqV+6fqmi16MraaYTH40jdcPjkGoZKiw6G0EUp9q/y89eaqxa4BH2Ftza56oNP/103+2Y9e7DjKvKQhkySwbxS/+6uukMt5r0dO0Tq6H/zAGA4nQCEltj7Wuc+8fiQZjBg7+rxBPbQYm6IDxlvsO3MUYuuQcwwN3PP+s7keEewWqhF0jNhYrLoKuGMe6TX9g3o3Zzvp7ColS339LlrkIfzDx5i99wAcsBogYX1Vw3Cslus4bI6Kv5axalZaKfElOsXwI4Qs+aGJ40qwH4VMGawWJPyw7+/p/i5i3CDQVEUi3afSJ2h15LIE9Yio1buD29BNEsg4UhRqd2K9FmVnSXUjITZXHvT403pR8a/aRRVHfdYRtLVStfpwjjlnsH0Ud/vVlHQNiawaptOYIWeDTLtOzFcP3BEGq/xn+5NXRoBqocrvqAmvhljVmOLNxwojDNlUdSr5wJiRQq6WlJV9dVRQQxTy6eVXeiB8OAiUojxRnJZlXy+lV/vIqMiEwfWFZQFpKAFvmVqK8Xp7gswhswUuwUfh/ClNH18Gl9f9O288Ql1G29ObvOMYCmXSfacJcUb6ivz/DVn4gm/JeHhh20XGvhz0b1kH4CoivAIdYPM3iBDi7Z4ZxUdkkYoENQDkm9C4c5wM1XhD3nFpZXgkWrKVxxfwbngvjv4uxtGFJ0kWmvC/Ka4xbAxcYe0DRXTSfxvhJn/VENWvUh/zh/l5JEPz0cdCCI2JAkTvDnhQaApvS9xnvDy6msyhMY=", "layer_level": 1}, {"id": "fab1e9f9-cde1-42d6-bcfc-9595bd179692", "repo_id": "94f96537-e6cc-41d0-af2e-17841bee1253", "name": "转账API", "description": "transfer-api", "prompt": "创建转账API的全面文档。详细描述POST /api/pay/transferOrder接口的使用方法，包括请求体结构（TransferOrderRQ）、响应格式（TransferOrderRS）、安全认证和签名验证流程。解释转账类型（如平台到用户、用户到用户）、渠道限制和手续费计算规则。提供详细的请求示例，涵盖正常转账和异常情况。文档化所有错误码及其含义，并说明转账状态的查询和异步通知机制。强调资金安全和防重放攻击的最佳实践。", "parent_id": "fa5aad6c-5218-42b9-ae16-cb022c852b19", "order": 2, "progress_status": "completed", "dependent_files": "sys-payment/src/main/java/com/unipay/pay/ctrl/transfer/TransferOrderController.java,sys-payment/src/main/java/com/unipay/pay/rqrs/transfer/TransferOrderRQ.java,sys-payment/src/main/java/com/unipay/pay/rqrs/transfer/TransferOrderRS.java,sys-payment/src/main/resources/markdown/doc/api3.md", "keywords": "", "workspace_path": "", "gmt_create": "2025-09-22T20:37:00.3551503+08:00", "gmt_modified": "2025-09-22T21:19:09.9902201+08:00", "raw_data": "WikiEncrypted:5TgAtbdUXqbKL9lWGANcdUfqP+lAWNpSuRjI12kKIVUK3D9IxlUFVW4Y4Wxnt6JxqipA70LgAtTMwztzADx8OHTWOJldEXpjYjymeSIitYL6hO1gYCfcedCoVcAS0yAW95nqpbhWZi2seGXmGW4oFbvwaIhP44YSS0ARc/Apk99Vd9cUN2tO4EXNYdzl6x42n3G2TyUv0AbJ6aif05FHcvHzfp1JmV2Zju2ejkifzHYazvojdWqbc8qbxBpNSXBMpgHtMF67Vo1h4b3UhIbJ8874p7yU2C2uvdiMXjT7BQ1pSScP6j0ilUHshikPca1XLupyVEiOPW49rWeMg2a757JmsSzHMFAP/RNKU8jAROfZkOqI6ZDdQ7O17aR8jw6UNWVOMazyfJ8Oi7XTT/O9cJs6GDVWJmncCT1FjVQmEndj/oygY47lPz7VJVLykncf/9YhebRVaLybapKUai4S1YwI3lf2t07fRzsAA98x4/ldclOiQhiR1FTd2vsXATkNbjbxtnvEpoK2+VLweCsAGI1aZ1WYYhWi+9X6SScjajBc50ekYv8Q5Jedn5C2IBhcfdNFCgUV0d+SSkeeZW1oyw1JnqJKor6KpLx1yOrimYiuLUB2n5UZGlQAw5AIXtwQkdFca9pvyguMs9qmr3twen/KVWqgE0hkoHhwNuwr812ttOXHKWBnqlLnPDCqJGySlQJ2blnVprsZA+PSQoKDp39vcb3suYvQTE3tNnjjMEAeRqDZGWsm5veqerAowFVRzmlHHsyX4/BFT19IE70BALPXa5SoijZ5IKikDGN6x6sjLE5dhaO3Gn0D5+xxGBQDY8hQnF2n4u9tNhwTrPVfd94YXkb2O/dUhPYsC2rXWc3QOrFBEhOogBtEHrArwkly8XOr6aM5n+Gzq8dpuIxtJgrkPeGd5xFwTFh7t6CUNSkKU8eU5orjWTfOz3rl0NzEcf8J1NJd6bfFfHB/28xUSHY3qMLfzLkU2MGnl9cLbVwgFD8n6JjVxqrrkNy4dEZ14tUQsZjcBGmoXLFSDQ5kVQVrxikRaPO6Cmz1o4bXjN+aZoIOb3ukbTY+Yk1WMGxiHHjlNFI0/ChcYNjt8s8/vLOtvrXFu6+VPThMrcshz16OCzbp2oUKQr+GcwdRVjJRVFxc/w5ygCCodL1dW2P/s9g1JXCRHktl3pG06jVzbDVBNSLmt4EB3cK255QGJr1fqXMJsRmrTNqCvaxGubyf1otHrraVEx+ALSptRoPITUtribHUAhtAesXqTGjGeeQTsxUWjWfsgr34unbskHyorcILOuT1UIq6Zg2QnszB6JJGdqln5C8Kdh2yiRGGdV3nyQoyTPrK2GGxcalpUJQOscXINtm2Vj2dzyBYw6azZWQhRFPggTiCu0uK/dBiWJMb", "layer_level": 1}, {"id": "52c46886-7235-4b60-a9bc-07e0d54bc939", "repo_id": "94f96537-e6cc-41d0-af2e-17841bee1253", "name": "转账服务", "description": "transfer-service", "prompt": "创建关于转账服务的详细内容。解释转账功能的业务场景和实现方式，包括TransferOrderController如何处理转账请求，以及ITransferService接口如何被不同支付渠道实现。描述转账订单的状态流转、异步结果通知（TransferNoticeController）以及转账失败的自动重发机制（TransferOrderReissueTask）。说明与支付、退款服务在流程上的异同点，以及在资金安全方面的特殊考虑。", "parent_id": "9dc2cf0f-4dd9-4667-9176-27002bba3dd6", "order": 2, "progress_status": "completed", "dependent_files": "sys-payment/src/main/java/com/unipay/pay/ctrl/transfer/TransferOrderController.java,sys-payment/src/main/java/com/unipay/pay/service/TransferOrderReissueService.java,sys-payment/src/main/java/com/unipay/pay/channel/ITransferService.java,core/src/main/java/com/unipay/core/entity/TransferOrder.java", "keywords": "", "workspace_path": "", "gmt_create": "2025-09-22T20:37:21.2834975+08:00", "gmt_modified": "2025-09-22T21:39:24.1230297+08:00", "raw_data": "WikiEncrypted:5TgAtbdUXqbKL9lWGANcdTbZbc2BjKS0LrDWS7Xkj1u99DS8GhHOOBPafwf9d6A7y6k3QmpSjYVPTn6UDUQW6iDCwYwV8fHJGAjGLVoccaM8XqOPPY9Q9tKOPWHEjY+U8tA5u6TDfjI7n2MpD0PvgxxrVbGHAUH/9rxvITP3Npy+yPSLmBl5HhkzBnV1gLd6zskYWWjmp1HrPDUkdrkB8VmrjgXV2hVvsA/SAWraHTHwwXboG5LSLMRCF1peoWWNNhqcZjuxXqY0cekK9Dgjko82PhOY+QhS84Ld45Kq6hqdE4LCBk+wF27V57KObIjtL/+G9h1rX2VNDiutUrPYl+4pEAkI3CCaabdRBzydrl+NF7NqNaNtIQbKgFc3r/Fq5YV5dx3XE0YCxNqUaAfZeRAhGyQ6QKpiNTFVVAhhNVLOqytt45QTuUoOhtEVop5m8EbH4+OZ2oSq3IKFRmte/1sMAyBNMRsutCYmnKuDyQD/KX9zZDoGHMjmoqFdEMWwfXcUekaoNPFJXNRyewaeM29xuupBYXHzF+Ta4Lw1l5z3h0aEbaPVWRh09/z63Vz7/RHz757Vbrv4i759jggMrlmLPqrlAOVExmfce24E6Q/2oU5Eg6BwE7WNRWVmO8qfqfxFAaYdnvwCry54yhoseN5QtwtGG5bN+bu935adSpPGEKozZEFNrH8o6s6DjmEP4bxkOlsEV/WQczA1ImCQSyX6f2WHjsYQy/9FBbZJeKnpiu62ZbQG1pU/kgnvk8JJ09AtnoBOSQFCMyS2GdmowsNJfDwTzLyA5o8uMPDCvXMK1O0XqEB1r0wlMSwNApELBLnac+OE5aQ74MzlbFOfemL8emOZJWd+5w7OKqZ7AAY4BzsaSw9XuqkA3E+ghCtYGEm60z4F/aWOtlkj0OH5sdksHGHVUB5/LEyCY2GBwrG5q2RiqDvi1b9/NICa/PoTqDZ/wR/DfHA953l/oefvtq7ZbS8wpU6pW+mi7F/jjWJ873Zv15bBRWpLSSD7rI2TM6SkdRJ5sAkYOI/xTO8qkg9YTGcddPS0J4OZV6YVPlEOD2gpo2JWQlJSToETRPzOVR4d4FgYvkDO9JwyCYgfioaHn67DGx4Hci0Hi8Xj3INdgOkZjRIVpMUYxJ6So15IkswCXpKIpYGeWzy0kgktM/Ze81iaJf+Uh4ZXuvNszN2RjTDpLk3Rz1qcaVVh/8Hp/+LWh9FkQyZGbrzrpUANJyLHaL/D2DtPoLtJfurlJZtCm9uDZVRB1JnxYsviHMLP5VfO7NNZdHZCg7Lb/R0p0fhXRr+0nCWIzmDpvkfuJ4R7qi+8hz8mkPkkNJWUFn6ZCLK1m1FkVms5gaYwcYkwiA==", "layer_level": 2}, {"id": "e234ddf3-5bea-4e28-b7a8-2705c4806c15", "repo_id": "94f96537-e6cc-41d0-af2e-17841bee1253", "name": "支付通道配置", "description": "pay-channel-configuration", "prompt": "开发支付通道配置子系统的详细内容。解释MchPayPassage实体类如何将商户应用与具体的支付方式（如微信支付、支付宝）关联，并管理其启用状态和优先级。详细描述MchPayPassageConfigController提供的配置接口，包括如何为应用启用或禁用特定支付方式。阐述支付通道选择策略，以及在统一下单时如何根据配置路由到正确的支付服务。提供实际配置示例，并说明如何安全地管理支付渠道的认证信息（如证书、密钥）。", "parent_id": "cef51d4a-e713-4964-9d91-96bee7cec865", "order": 2, "progress_status": "completed", "dependent_files": "sys-merchant/src/main/java/com/unipay/mch/ctrl/merchant/MchPayPassageConfigController.java,sys-merchant/src/main/java/com/unipay/mch/service/MchPayPassageService.java,core/src/main/java/com/unipay/core/entity/MchPayPassage.java,service/src/main/java/com/unipay/service/impl/MchPayPassageService.java", "keywords": "", "workspace_path": "", "gmt_create": "2025-09-22T20:37:23.5037217+08:00", "gmt_modified": "2025-09-22T21:39:22.0475841+08:00", "raw_data": "WikiEncrypted:p3LgfAL2wfc7BiQuIBFPvn/gylzwB/pBAZrT/TavW06qa/rrZY8c6ePohynhHR7mKPCSufHLOdkm1aZDwYiaHZ46MeleajiHyWWJUHGNf43HChhE3wq8E48ZidzMeIpxddYdBOcjx1RghnVmpS0nPkBkst8g1BBnDkR5oAPcFJlqkkvfMs6S3D4w8TFC/RW3sQCri6SJy5uqHL1tbEKCN0bjsDY7Zml/xBL6Ax9jzdHB5f5+JsC9POAxMO48vNI8nxep20gG45sWrI1wcWAMT/6GNRAi48BXAIeeTmp5Q7rnIR2E4Sz3m0s/3Z2+BEf8G8ODYPfkQ1aVmm8HU9ZoJiUXOjYgqXGGNxhnFM6QlCrf98T92a8aLLok2gDrUy4eDkltn46V2myxPjhsRSJ6n+WcQQ9zXU5ydOCeMBtLlQWeRdAS+buhR4OjxOcZ1cXJZkBXqopqoMa7FrjJpE2sq2bOvuDXHz7jO4WuZPsTRVoJ7mBIeoLnk6cijQHgHejlNxqiqha1l95E5nlaF3M3BTHhGsDEtUgs1E9cVByvf5nAYhUkPql93DcALpIRfD6z3V5hkKTOwG+meB8UBpQ0eyvdzSwY4uLFhKI1whw20GPhhbfSXFAO4epQE3bva6lSWeBdeujyH7XZsQzWTENrwKVdY6J3X39ZDV9OLUB7v2XCNBpPK2SUhgv0TlonWtbFerf9LU9xlexqyZQ+OcQkDLcvpv6M8oulMtXUB1PdS44gsQDZOFCefT/yUyTEG5K4/3FErlLXXWTW6iYISTcoampcP+V5kFQzuthiwzZ6Cf4u8j+iodpdfvmHCsgN4ZqZf0qBTloHlFJlWDShTUY5zQeZxJQq1PQRN8quR/SBpGtJ+MTFpEpGDiYN8zGGBqlZqNb1oGmsFhagOsY868EhC9VbjKzX+/a81K6qCnPnLKvaVZGCjza3SyKFeUKoUBH8zxng23frtEk2IAaPrTeyKZ8hdi3xDmGQc7eUrR91c02/o1SE8yvyIUlGg1uDaCWxKI/a+jcOokne+E+TC+EWxg1uGqMxMoEoN/e5APHzDQqYWSTs6iKpzAHc7/ZD9VXRzVHnd6YbhFsisUiWKmsUMgIZnzINpmxf6Fmgor0ihyMbVvHWh1EXHV89Pxln2e3CcdiviqkzotLocJQvYfd9jg6329LRP5WjMAZM9u/hYE3ryPxL8/n118Q2xmw2Y9HftXdKgO6iZktiDUWqU1jJLXgz2/zWNJwj8wxbNXaM9eGwCK0nJV7Mswh8E3TtYhD0blQnnSNg6jBjDEcIJ5RsBq1hAVkJMXfcP/pin6CzszmUuQ9hFP4KRHT99GnxK1Qc0ki/5+kB3+8xHt4OMBRdjgjyIY/mYaSmgYQnDQOynu9zb30b7YbRy3NuRiW6fzVA333RplS7AGhzl1MVqESkEMciWkdHYCRLsdsvUuNBn0H5uje0gkl7R7iBwUOHYBCv", "layer_level": 2}, {"id": "67d56238-1329-4c27-96f7-320b6ff44c7f", "repo_id": "94f96537-e6cc-41d0-af2e-17841bee1253", "name": "商户管理", "description": "merchant-management", "prompt": "创建关于代理商系统中商户管理功能的详细内容。阐述代理商如何管理其下级商户的信息，包括商户的创建、配置、状态变更等操作。解释MchInfoController中API的设计，如何实现代理商对商户数据的访问控制。详细说明商户应用（MchApp）的管理功能，包括支付通道配置和接口参数设置。提供代理商批量管理商户的使用场景和代码实现示例。描述商户信息变更时的通知机制和数据同步策略。", "parent_id": "3fa8e6c1-d858-496d-8d38-efe8ff173028", "order": 2, "progress_status": "completed", "dependent_files": "sys-agent/src/main/java/com/unipay/agent/ctrl/merchant/MchInfoController.java,service/src/main/java/com/unipay/service/impl/MchInfoService.java,core/src/main/java/com/unipay/core/entity/MchInfo.java,sys-agent/src/main/java/com/unipay/agent/ctrl/merchant/MchAppController.java", "keywords": "", "workspace_path": "", "gmt_create": "2025-09-22T20:37:43.2341591+08:00", "gmt_modified": "2025-09-22T21:42:15.2276345+08:00", "raw_data": "WikiEncrypted:3KjeyWIVLPDcIZ9hw3jQxw+hboGqaP3xGVduyP3Q/lhhgXzobx2buh/sLt52Npfz1L/LZpRnLQV2CvVe5GBJCnsJG5oeGupzIpf6qaeWbZbF+gbl2nM+8VBE+pFJfFJM5hDUhuyhFVRmL+Gaj8eipPGVt+Kh/hysEDAOMPen6IHKq7owTc1iNyifYUAYTaVRMzaLUOUbTq7LUCXhSMt1VjKaghE+tplb9T1E7Cy4gs8wjeOF2h/Z8faoKvIy41ScJ0vCCtkV62t8Djo6aacoOXc50o7gEscVV4Or8+RIp9pYRPCBV9OOMsXbk48lhnuwGVgtzQurKlgjAGFuiNuveNyF0OHFczz/dfQC7pREYP1F/zuJemuZSG9lQJYXboW64HoAja2M9+t9yvm1jpDzr2iceNsz5ZXNnFmxj6FqUlVJBjKedbN+QRueZe5ycLaOTlbSIUhbKTOmudfRFxA0/JL1/Wvw7M8pvdnlJCjH4so6011o31KyJcZBIcaZmy664xTGNWy9jQwvwxFKPX175fDsHonZuNQQsKw6AneS0+DaqQeRW0XfPltnJBPMR8fJN5KnE8K7QYklaQMdsDe4IMRUMx+IbgwS9APuCkm0glR4jRgjD/nW6ayJ3Eczc5vf0l7rCsGMqLiXJSl6hiSrJto4m5QGBSQ64gjEKpm+l3x7zElLhGzQb7xo8lsSkNSwPOsyAzBb2w0fwSkWmL0mQFiDRHV+xNr9WX5qSxtygfYHWjlvdRv9oqoXrkLZVH7uwAxvh1nlky3kjar9mZTI51mQ4by77aFayOuNofRChGd5k4OSm6nFkCpWIBHGBWVmjQ/a01AuDTG3rfT2uLsjZ6ZlAREtY+ej4Tz/p2FfLxztaSe8JlowHDHa/HmhroUQob7ELRM/6YwxjpPtUv7nySqXUq9mh62+tdCz7JmSB9zVeNzXbaBaw0L0d+i1WXJc4evbWZtz/m17DjoYLP6Q8uu0ya323SMDZ0yh90hiq2VD2fVXkbdSrXvEJe0eoBeAX2TFMlIkDAo/nL1J7b9wIhxhSyoj5W6AIiRmUfB2nmKOS9OJYg5a/S12orXd6A1KUgQghyK/QtoczC51oH0jMJxmBgfcBvkgB1LOeO9D3SG89MgDBvt/89Izm4OjYgRymfKO8aRbTGHIaAJ2XGQzpb7+z23Prl1CHQeG6nCvQvUhAv3JbrKP7w3iF5ong/QvR2iI2Ab9fpiSkdkx5mq0OqcAZaeCgz0iAulPm2XaKw+LCig5i9RhTAoenXTAv0CZd2Sg2QXBecpfoCVhdKZJ+rlIjmOBhvN+E0H0Fgh9iW094J+lQ+J/4EnVmW13nphdKmkQqn4tOZyLSQ12NYIKetppMu1Gx16cBPxP7QDRB3M=", "layer_level": 2}, {"id": "5816d61e-13c8-47b5-852f-5cd4075956e4", "repo_id": "94f96537-e6cc-41d0-af2e-17841bee1253", "name": "系统配置管理", "description": "operation-platform-config-management", "prompt": "创建关于运营平台系统配置管理的详细内容。重点阐述SysConfigController提供的配置项增删改查API，以及SysConfig实体类如何存储系统级参数。详细说明配置更新流程：当管理员通过SysConfigController修改配置后，系统如何通过ResetAppConfigMQ消息通知所有微服务实例进行配置刷新。解释IMQSender接口和具体实现类（如ActiveMQSender）如何将配置变更消息发布到消息队列。提供配置项添加、修改和广播的完整代码示例，并说明各微服务如何监听并应用这些配置变更。", "parent_id": "b6853ca4-49b9-45e2-a077-b5608db8020f", "order": 2, "progress_status": "completed", "dependent_files": "sys-manager/src/main/java/com/unipay/mgr/ctrl/config/SysConfigController.java,core/src/main/java/com/unipay/core/entity/SysConfig.java,service/src/main/java/com/unipay/service/impl/SysConfigService.java,components/components-mq/src/main/java/com/unipay/components/mq/model/ResetAppConfigMQ.java,components/components-mq/src/main/java/com/unipay/components/mq/vender/IMQSender.java", "keywords": "", "workspace_path": "", "gmt_create": "2025-09-22T20:37:43.4245597+08:00", "gmt_modified": "2025-09-22T21:41:29.99413+08:00", "raw_data": "WikiEncrypted:WmYz76ZuIeWtQgFK+sghtQMh7EKu3X0disbrzmS1z/ZAtNHMaTtz5bCJL5wwiIvtF6U5Ly4Mccz1nAgJr7FJtk6IpDF1CbrAUXCw/3Dka5eGmskV7huOyrm9WiFnEsx2i0iEw09it9F0OoZ7S4OLeu2ga4D9qL9OHaRmdEY+IN6cghJF0vaylUbIg/UYZWpEvOX5pyhdlgltGSFK9og0vuiwYRmrpsxrr4AMmrkpTorATgs60DsD4SjhNVwoX+aiy0gz748zdv/GV1cJQNmLJUxAHP6AYqpEhLBYy7P062E7/avG1RQyTN0i8Rgu4H9KOnp/wuiDH8sFD1TXXm1UH4P1vVaeQ9vF1nQXgiVNwZdPwhffLW5eQh4wioy2R9euisaRP3u8r1d5dg1jWfy1kR8FMZLCWBhyFP84dz+V9tOeAPZtCf0h++y7qUSi1iHficT9IP9eZdsQD/c58DS84l6a0dxNkpTY6WqZOcHllWp4s7eW1/OTt6BGLy0YtSx1MATR7b2kwbpOdRYaRdX7T3vwBjavPerqssGBFlWOg4c0VS6qygu8SMn9o2HrM5rV0lF+FPmvaB9RezL6Zis4tflm4tmf+ceiak8dG9P+j+rOB5eIGWXsE0sN+Mi8vLBnjEQD4pqDfE4DL1tyHg0QwYBdfrxkEUQ/DietUNKsALlyeULz75jyC0WxbbCP6+VV1cGNv57JH+t8LqtM6udefX6W2sn4Ozx6a6tm2OxUFPyIE1Shs5bLf4WiOei3HJRAo88ATMQDMYvqtIi72+mPffbU3f1eBrV8HgIrdWiRm0eqCLu+Rn7GeaB/GG2yUV31bvHzozCfhumaNpZ/54X9G3jlGePqtR5++ua/FJgUJIaqAbFsYmN5j+KJfVbyXEk3/ZdILz0COLukEAfgjE3KAiQtQu72tYW+RJs1eHtu5ugSV5fJAOux+ENnK8cGy2PuTYXdBZgDRR6Au+Hca7ntBHA6UiTH2M6AT87hUvFetj0av0jvutKR1BCjlBXaKT6+llyZ43+dV+QBK+9+g8yu3pBCj8OIqt7+RxXq4uJOmhQVGbXnY0vXuHToIVerlOJ6ibrdCLuSqol8aaupkn2POdWIP5al/gOxzLk3gWH9HWqoqo+kBjHafWMrbDaEqtGt/Zu+l6ehSQBZSD9UMzFUbf5xzbc9oUFwaGz2K/KSmwqBCqMBJXu0FDTZR3WYe9X9oBgQGbKBnBa4qzr/6S4X7G/iVw19RwStCVexzKvEK7mRzh6hGkF6TXS66kOmFXGTHuY7I+9f6zJfQl45s3/lJFAYwn0ZGSEJlVYm6JBZDsehwZEtG+SA3uHlZhw8PJHBg51GhBus2oKygjz+SzdtsG2TgHhxAvLep2x3t4zNByYhlzs/XJNjBC4wQ/RC6g3ShaSCZy0Nma63FpxDzlQy0/8R4ACOnFzRjOlAeDuFyYMd6pC65kSZadXp4oZAA1vQwQxoKSBawLwsNTCYW2UD3A+kcYF0ebTJXGqY1PwYmOsxUNbtbrPE3zvUMRgS0OTpgFqW4T5OoEMXn7nnHaTfp1zWS9Hgngb16Cn+ldbHP+Ap+X9AJAbfcvTz2JKXX1h6WSSBC4B+o1td4HALyHEqThxwQC7+hxEQvRDQ27/F6b0PRvCBbUdPKeXBzE0l8fxX9NOumxX8iTgeQisk6bjct6p+0PVJj0Wvb6qdDQXgBUI=", "layer_level": 2}, {"id": "2afe1e51-356d-4a16-b6ce-d6de6a7c9bf4", "repo_id": "94f96537-e6cc-41d0-af2e-17841bee1253", "name": "关闭订单", "description": "close-payment", "prompt": "创建关于关闭订单功能的详细内容。深入讲解ClosePayOrderRQ请求对象的设计，包括关闭原因、商户订单号等必要参数。说明ClosePayOrderRS响应对象的结构和可能的返回状态。描述PayOrderProcessService如何判断订单是否可关闭，并协调调用相应支付渠道的IPayOrderCloseService实现。解释本地订单状态更新与渠道订单关闭操作的事务一致性处理。提供代码示例展示如何处理关闭失败的情况及重试机制。", "parent_id": "97769763-5625-4e00-a0ed-ae7298840dd1", "order": 2, "progress_status": "completed", "dependent_files": "sys-payment/src/main/java/com/unipay/pay/ctrl/payorder/ClosePayOrderRQ.java,sys-payment/src/main/java/com/unipay/pay/ctrl/payorder/ClosePayOrderRS.java,sys-payment/src/main/java/com/unipay/pay/service/PayOrderProcessService.java,sys-payment/src/main/java/com/unipay/pay/channel/IPayOrderCloseService.java", "keywords": "", "workspace_path": "", "gmt_create": "2025-09-22T20:38:04.725362+08:00", "gmt_modified": "2025-09-22T22:12:27.0397847+08:00", "raw_data": "WikiEncrypted:Neahi4pUCSila083bO4Ca1+W6xZydKZsLNeYOEfwdkS/e0zLycYUSh0eVBAOGMEEDAZ1HYGjcCHjcfv8VIDVof3/sUcUt4vieQ6gi6TCvBIF8ztaU1Xy0n0p1iNKZ259woWYE4OY2rpx+YN5Za0LoCjqIqKGuC5fiTgw6KGLVC4ue9XZebapzFd4fw6qyQsYSW1x1LYcroCiqXMa/qA6CegeEVz2Z/wCUWDqECwqJ0iOBZ5zhDX5zozHLh1HVMiS4eFWTHOGsAbJam8abH8t7NZQrZ7grbSSIuTtTXItTdOvNhw0ZUZPCT4xASFN/+mlCsnW8Mcg6VIjWUd7rl2L50boc7fRMUdEJhze9CdQzIjjXF6mjxM3CuDnq15G15HCh9ToPohcm5RzRiJxEwiKPf1W+dE4/+tvyCk5dxR9Cq9mP5M8tEXXwgbnpEm6FvkKw/gwAdBkPIrADELsA00CWSwirj4iKSgR22Y1mGpKcY7iyMJf32bmZvRlzVWHzEzjkq0yQJefuogLz10X4ADuVkbmROGQ7iddeLL+Tw7RuA7AzBi7wDzHwShub6zDO0w8Rp6KRlk7KvbRuhX+KhEgN8vsSTGGVl7GKOZsd9hzamYp1E+F8syNwYvyLxugwojkJtoBGuxxopRIiAhuWt9nBB2HPVyI5oE+y785FckEog5kiYYrMrKms7VrMwfQk+hbWYih0n2M1paRCNQR7d9x3MpUEZ8s7CHW9Y9kBrZ6KqSLd/B0saMkAgUxfU9ZXgwM1eG1MnubgnGrzi3IQAwY1R80KUh3UNbcSsjxgd+FXMW88e2g9Y/8VpcCR37axEtV60F2mIiOX1Fxxub10lHNK/sHBo2r7YAT6z/rNDhx0flBzgzGdPzx8PFpRTyYe7v+mmNQugE7Rwyc577NWJU0uP9m/p/NbUEmwj2zJ/k5/KWFo1TeIXPRlYMBYflQdDdB/TUeKrjmruWgChiqpDhWEVpyIDcak3VOyIFLQt8r0Ew5erK2ROTWhYlPE2hnro0P0sJdnwmZcp+JuNpBApWGRPs1bEWMBd9Sdj96ok6/71dBhaJ7iMMUA2u0qleEYeieGHIBgdZEdbRENgw9aaBwMnXDmoNte/n/+b0o4lCsXodgq4TrZT4565p8M08or/MqChONqZVQzwNDUYs9afS3D0ENszlhZHDn6M7wAHR+OS8JYALj241zT6PsTP0675nvhQAb/n+szbm23bDaUAzgvQpvNGvnlx4yyTrYeNQdiQySp5xT8PFoPaaLx8+kQYqfPlAlNUcomXQYnkKSRzpYRk5aga5m16OmVwD/4ELtexuy8kg+OOXIZEt6knXElW9tCm36N693ApNVNVcmDJ65B9mTVjzj+vCDpRKf/hOHivQ=", "layer_level": 3}, {"id": "f9df9f6c-743d-4cb7-98b0-3d9636ece7e1", "repo_id": "94f96537-e6cc-41d0-af2e-17841bee1253", "name": "关闭订单API", "description": "close-order-api", "prompt": "创建关闭订单API的详细文档，重点描述POST /api/pay/closeOrder接口。说明适用场景（如用户取消支付）、请求参数验证规则和状态机约束（仅限未支付订单）。文档化关闭操作对库存、优惠券等关联资源的影响。提供幂等性保证机制和重复请求处理策略。列出可能的错误码及其业务含义，并提供前端交互的最佳实践。", "parent_id": "20390a93-7ada-4338-9006-b06206c31398", "order": 2, "progress_status": "completed", "dependent_files": "sys-payment/src/main/java/com/unipay/pay/ctrl/payorder/ClosePayOrderRQ.java,sys-payment/src/main/java/com/unipay/pay/ctrl/payorder/ClosePayOrderRS.java,sys-payment/src/main/java/com/unipay/pay/ctrl/payorder/CloseOrderController.java,sys-payment/src/main/resources/markdown/doc/api3.md", "keywords": "", "workspace_path": "", "gmt_create": "2025-09-22T20:38:08.2881678+08:00", "gmt_modified": "2025-09-22T21:44:44.3995658+08:00", "raw_data": "WikiEncrypted:Neahi4pUCSila083bO4Ca9maazG3zoZzy7XIE3n9MMaJwOqdKx2JvfQmSSYyoFmrf0xIjiiT+FIqzSID82NVM8MlVh3PcvmLBdVOUKVUHrAFLv6JjEAzAaHOZSE7fVVEHzrdskZtKlCPQA2YXWhYwBVmEhFP2pCGHDgJ/uN6B2qdyq/ZBQYcVa+gNNsGz0CA1WfqzX9uxBjVIQXb755cxjADCgCntl+GZD154y/NeDzd3XBypgHEGzMxb4B95GfsrfbAzsVuWeb2kTMl7iwLsuMR07Jns9/uTJI63hnbx/9jNL+31tEY44KLa7rONCdyclYMWdlTR5uXz873ciWjXiuKAPaYU42U6SYBuVGVQZC9yFNPVGBOVPEefgCnk/PzQbpwQyS4dmITjOyG4wyvHCLtu+I7513vvl/PLiSmdjrMmvER4RqMCGvU1b1X5gbL9Sqp/tDVFmLQgYZFhk3WAG63Intv7SVhdsA0bI3ISmKT7Eznnk4VvvrjFWloK57gJ72pLQKWQ79c0xSCKWpCDZ2PAKJidUZRb5NOCrLi+Oe0K1Ygo8MV48fESPXLwBPh3wIw3tbl5kVuCfDhjVq4L7nuc3udjC/xtKCcncIa6jBclSRB4SXgbMEtgq0KkCBcF4HXJlul+VoZvWvdF22C+tQHjUKKNiL9m7C0PUprCHQ5YTo+vSGBe9hvpqH/OOMV/Qy1TVKJ+f5lkp1tKKlrL+KHbj7IgCufKwOHxzTm1LTQkE3S07c+V/7JcMAJZmyNAPFnLHL+NE3o5KmBXa5duBewki60xZ4NKmETT8PhRgssHJ9jxJxQvbNa5xBcVEAsT457iO3bFHqKV0Kq7uq7JshKLhqXM5vTJP5HdE9oXoeT8FMP7vx7Tn8pvA0SJ0g9ChtIds1VZIGZb5u2b3/C7SBlBz6A0XItVPCJus8VOF62hEsNtNPf7gMsEsEoC5OdfQT4LJ2Q5rk2JY4SgKQs0qVZqufqWe+L4oOmMIp8OfCPYvB/0eMhA1gZK2MaUXpM3ZcrBeaqAZ57RgqxEXRdWAzJNxm64zTDl9aSicbMFb2XGxHtJJ8DBmRcuGef8qhT/MARrn4fhYZjBlibvp6kmZE53ihEtMke5NN7xbw3mt4PApM+aMcXLygttYZHPXlvB0wNSWFKUr0VrrMkkJLbKw0Dym+XNSrHHbakLgRoYEs/5hDnhr49Ty4rqPdDNr9Q2E36AC7duZOJ12AxO2PtAkInIT1w0uiJsn5hpySosPk=", "layer_level": 2}, {"id": "fbad6118-013e-422f-ad22-a5e992fdba11", "repo_id": "94f96537-e6cc-41d0-af2e-17841bee1253", "name": "退款回调处理", "description": "refund-callback", "prompt": "开发关于退款回调处理的详细内容。详细阐述ChannelRefundNoticeController如何接收来自支付渠道（如支付宝、微信）的退款结果异步通知。解释通知的验签流程、数据解析和状态更新逻辑。描述RefundOrderProcessService如何处理回调并更新本地退款订单状态，以及在处理失败时通过消息队列（PayOrderReissueMQ）触发重试机制。提供安全防护措施（如防重放攻击）和日志记录的最佳实践。", "parent_id": "918d1aa5-5ab9-4cf1-baee-3cb7f20be143", "order": 2, "progress_status": "completed", "dependent_files": "sys-payment/src/main/java/com/unipay/pay/ctrl/refund/ChannelRefundNoticeController.java,sys-payment/src/main/java/com/unipay/pay/service/RefundOrderProcessService.java,components/components-mq/src/main/java/com/unipay/components/mq/model/PayOrderReissueMQ.java", "keywords": "", "workspace_path": "", "gmt_create": "2025-09-22T20:38:17.3795682+08:00", "gmt_modified": "2025-09-22T22:14:33.8549335+08:00", "raw_data": "WikiEncrypted:IsXrw8CHTmYocng0pZ32k/CNa1MKBZqyprDaVfopK0vrBzCEyiHlpKbUis22Ci123wbhd8dmVyTxPAdMZtEdO2or3UG2c/xyjVP1KC6IYOrzvBtTkOWcrIKynhq7+w5WRiM3uMJZG5kDIEAngYaNTWYCulFYjfwGQbChu6SaIHdwzdSv3U9J/k5gxa1+42Sg1oQZMgQnW9hGb/VtCDl1ZdbefiYTn8hkTIn5Tw5I31r6a77pEmyVLC6lPveWkrkibgPTmYBZv+2/W0y5pMIITRcaWXyet/Xx93svrhlqWtF22GRuH0M9l95gdCBZqwr4xyOsmj4sxAwv/jbPR/0CSovF9GiUMWjxJtctcSc73b/SABGywtKNnmYylBc9Zty0YyQuNZ4fbFMNbiw0DIZw9BNhenKidO0/6TJubGaC06gYTlBtKqTI8WaVGiPVAfFBpqiQDLbDj/KiyNuPWg3hcjF5scuXQESi/DJpjNppcaFB/2YSuOyM1rQ4FLJwsF8THcFi+nCn2vBwZqOFZoKqtKdPtUIVu+xHPKINmWwGNyMl5Tf5QiNAd72+5QjH/K9/yVEX2YJe2Hkuz95M/OAoAQlDbA/FPAllicj5H4vZ0sdMjGVgHOHyIWE8Vf2B+S2GVkkFkMUbVrS/VHIosaSRFNiV3qxrE+fNgD0w4svOyHcODOcyzVOKaF2ZpY+rHdIAPWfbFwQh59o9+vNRBB0/uRPPzdeGzaoRn19J2arpt28KfU4hRl7OURCWAV8ei9so5V8yGUzvezMAJUHnrUQ8ll5bG3x7fdL3QbtrjCiwOBm5+7esz21+cfbis6U2V8TpQvI6UQXKUcVkL1yXiFVg3dgqWTOuv3z3nMEbOQ+OSiTEdbEE8HS8EZyMLDuu0WXPRPOFZ7hT30oPUWUaHilfyjP0VIP+NMcPFuHpag/8rhb5/Ib8iD02gzLtIyx8GBUdk4eK9LlQutDMW0SSb2wV5mSs97GWxCIPCS3ZOZuORDzM2zrwli4Ddpe8+m3WUxCPkvYLhUDMjZ1FZ9v6ZbcQjevaxR8zC75hChg5f5v2ab00H2AZ1RnWEhofCf6eZz2cJBhVfVrvZDr4Bdrrc2lRjYa+oygNGtrtttKObDy+Zm78i6BNEqJ30xWa8W1yfZywPZGRnaJ2f7yaodM5zjXJfkus2O5Lub+38MX55rnJhBqXss7X4COkFO7CX/UHrKH7McRSWhXiXjktXrnHOmdWFsezXIknoDBDTi6aAuYfg+w75Odu2sgrR1rYZIHTRCnOcQJmcyXs8AWAbJJbnrclxi/q+TUcGN/Rr+Rnrs4PJs4=", "layer_level": 3}, {"id": "ab272022-c9a3-4ba2-af1f-68ab02f8cee1", "repo_id": "94f96537-e6cc-41d0-af2e-17841bee1253", "name": "转账回调处理", "description": "transfer-callback", "prompt": "创建关于转账回调处理机制的详细内容。解释TransferNoticeController如何接收第三方支付渠道的转账结果通知，包括签名验证、数据解密、异步处理等关键步骤。描述通知消息的格式规范和重试机制。说明PayMchNotifyService如何将通知结果持久化并触发后续业务逻辑，以及通过消息队列(PayOrderMchNotifyMQ)进行系统间通信的设计。提供处理回调时的常见问题排查指南。", "parent_id": "52c46886-7235-4b60-a9bc-07e0d54bc939", "order": 2, "progress_status": "completed", "dependent_files": "sys-payment/src/main/java/com/unipay/pay/ctrl/transfer/TransferNoticeController.java,sys-payment/src/main/java/com/unipay/pay/service/PayMchNotifyService.java,components/components-mq/src/main/java/com/unipay/components/mq/model/PayOrderMchNotifyMQ.java", "keywords": "", "workspace_path": "", "gmt_create": "2025-09-22T20:38:25.4384269+08:00", "gmt_modified": "2025-09-22T22:14:33.2536417+08:00", "raw_data": "WikiEncrypted:5TgAtbdUXqbKL9lWGANcdSwOA1MweXw44I9d1nmRYwqQsoZRnnRRj2iiu5b760Nx29qxjhvf+i28InaM7dW5dld1rUt2BSZrqbyzV6akxp74Xa17JqKLLpQWy5jGew+L0iT7iE7NJMWrER/7AOg5Kw9MOe3wAOTIcS2sdtN2FSVmlQtfwDBVX7zltpg1+zl3o3yACkqepDsHL1ZsPD5gr8VELkJex86Le0H/CZf0FuImkLOw9KyJoRbggRHiDHajNpevBgOuMKtD7i0Kt48STZuvziEYnvEnegmWH6/FFP+JjQGesvWBu/+wmtlUFKCBk9oinncUJSwym0CMVUtlZv5SLaJDMF2yj4zRXEAs3QGIObEUH5yfXBZW8ro98iOau8XfT0gK9pRQkhO0Dh0gyFT0jRGxV3JngpPGcEgOFianp1XjT39sBYvXe2uhkpEgNt4jl0jzGnDCWTAw2TekcB/2IlE90BQcrodWhiZk2JfmRQaeik9tAiR9Yfn5IuRaOqw4k4X2963cfFDo7TbNX7Fn+L0X0KWwhxz097hQFP29CLtkDdlqpXJw2g3cdUSafkOpBnGmAXOtFRfqWz+HQ5pxHzL+HrHAx9qeHFILBcEhTDwuuAC8GSccpqDLUnYJt8IMOGjNryZdqMTjI/SgDXcVYQtDwaWIo70iBW4uWqottmDglk7nFRIyR4J3BWwc9DrLtjMIQ/Obo3k/bTY6lrQjEKHEfLqRuz9oyo5MkX268CMDyJI7z7Va5Zb33XM+7wsCCliN2gpC56U7fU2Jw0jbjuuYI9AAJLFK5RLOQISHoB9p+sG16SdAIpqH/TACvw98kEj5ssx4L1mS/wkz+nXBCS6iPPf1DkpAO6E42dUnPTvC8xfOyWtPaDzvuvWMoTJH3yHxl5oMrbxO6b/ygkcRVqgZRMrg0gtA/sRv9fzLkFJaq7CA2YrJ0MVAaqI2h5KvG+6LBaBL3AK2r5+KEQCscMalgXf/Km21ZPhUNTp/r8ZlpJtebGbMjjaPILjfZuyl2bwJmM6tYWutp+af/c2rG+nCa2WQh/C/SMnDbYde9Lo7Rb8LPk2Q6GwYmVRv81JL2PxLP9EMu4nZpphT4wlEi8E5IPbyIB12uDLe6POdf3CQmATL7lyV1A+OO9uNOZzuws09Ppd4RX6rJjaBProHfJp4U1A8NOLgrQoKl26gfeTbAYjUtlEllfsia9NQb0i/5wRXzlSl11Xdpq7s1P5hGmi7ARneWk03bAkR7Z5fPw2x/jVcFOuHGS9BmtYiQCjDYMdl/7fQFXQt5kMhQQ==", "layer_level": 3}, {"id": "45ac9675-3ee8-4b0f-9927-cc514e45b49d", "repo_id": "94f96537-e6cc-41d0-af2e-17841bee1253", "name": "分账接收方管理", "description": "division-receiver-management", "prompt": "开发关于分账接收方管理的详细内容。详细说明代理商系统如何通过MchDivisionReceiverController和MchDivisionReceiverGroupController管理分账接收方及其分组。阐述MchDivisionReceiver实体的字段含义（如姓名、账号、分账比例），以及MchDivisionReceiverGroup如何将多个接收方组织成可复用的分账模板。解释前端UI（unipay-web-ui）如何与后端API交互实现接收方的增删改查，并描述数据校验、权限控制等安全措施。", "parent_id": "b421b6f6-0693-49b7-9034-6061411d55c4", "order": 2, "progress_status": "completed", "dependent_files": "sys-agent/src/main/java/com/unipay/agent/ctrl/division/MchDivisionReceiverController.java,sys-agent/src/main/java/com/unipay/agent/ctrl/division/MchDivisionReceiverGroupController.java,core/src/main/java/com/unipay/core/entity/MchDivisionReceiver.java,core/src/main/java/com/unipay/core/entity/MchDivisionReceiverGroup.java", "keywords": "", "workspace_path": "", "gmt_create": "2025-09-22T20:38:34.5058929+08:00", "gmt_modified": "2025-09-22T22:17:48.5533646+08:00", "raw_data": "WikiEncrypted:TfUUykgSV0NZhhl6ftJ5NIsbZehxshuOYg6Oq7rau+i7rRXaR9OO+MkbrfSmPHvGItVG9JksIhE751wCLnHtAXHU9UXvza6ZP5IaCAf8siC8UznTKOrGXwPtqUbkUfc5+Mq6ZZYpFKUwJ10m4O54Scof129WaX3sMug5T66G9mfa8ksJJ84DsjGv2tJNUHoBeXbbgIAjHDinhEPllbKtQGUX8xVBHkl1r/d3B0lqtL9OR1rhuNnUaii6YdVNHTqlll6/4kRroiNNQkBz9fdsuXTLr399Jh4ywZvn8iUGLMa1ADj5W7RXTntZfzDSb6NuPj8Mgl/DsfO/yUSSaL36vkwYZVyD4zwzku5St+0ao5+rEFUE0JTjSFOAWRH6cCnHjSdq7T/9c6wEtwkjjG/bhaXIXuO+IAFJdl5bmFZnZvrf1QokbOginyEH/ePdtOfPVkVhbdgG+gFVp2K0g75kJL9YvMtHEX5zORY6UC2azl17CJVWk94fWCmdx3HbecEhzjRwB7KFTxDhrvrKB4kTlKYLYFi/S9BYfAA1Mw675DCZkwPMxA80kRCDH+aYfWqHIlUUz537JBHBT3KLyl6eiZQUYtlySuV0KHy37SRBdhy+sCA8kA6aw+NsPQRoUr26P0+eIZwQCrYcw5k6eEB35yc4UXFuAO6zVKmn+dqKhfEtM8r4DCwPvBfTNwzNznzlbedQYRSbWOQcGkR7y+qXWadkNv1xfJSVoTDsX5vBJ7WKAdkV4BGQRhDnx4mSs2OPP2cqQCQABV0QnxIYl3c4LhSmqIZ/IpkN2kiZ2Ilof7FjPR4Cw7kgH4at95OMFlF9Lf3HJbktDDDrruJl6sSNBCHlHAlCW4HBYe1XQ18ycw4j+NcKDufOZDZmvkWEjwiKixV60wLiv1+6b9hjtrg8pqpwsCaQPMlPqJcqy/NEwxjOiAK/HyKBHfeuxRntdADQVF8QIJ3GFh2LUdhLP7TynAl1Ms8ZbRzJ2c6r8lwM9u48ylInwLQvSOosbaeEKfeM595JGSd/etVmln1aBaTqa9ahL9AWex7EyZsQAPFUn6/Q4fM1TIdaizszUpdfW4+GL1yWXKeA04bpbMJ171VZoXjaUmNc284JsvVdxCrIXKdtsyqn6WYObChanBrFgxXPlAEfiAvEGvGdPjDVyOniqdHn8wd9MtcY95n3VBjD/3VoKrHcbqJdGFhx0aMXO/CzZO43hjfEUSjDD8Bf721y1XaNczUA899BJMhgfNvTZ728fzZLbGNzo+SsGCyAWTpUIydsn+pgbXxj2p+A7DYm7mUfcWMlOipV/8noOfYyvrdXDHl7wFigEDLV5BwAoxo4cjR1vszg4Z6uLIwnGfjtpaancylHkuntxnHoTJyew+zULqFfTc+cGHnFXPbdeoooNwdjazcxogZ/+gfEWYHV5g3Tn2zJGgOd+yYqSCfpmVUzPetYTqBN6Nfbznm83z2G2Jejkh0fogE9QpS84jQiJrB1EFLkF/5u32B2nonjlZE=", "layer_level": 3}, {"id": "6c28c636-0567-4d46-9564-c10d609484e7", "repo_id": "94f96537-e6cc-41d0-af2e-17841bee1253", "name": "微服务架构", "description": "microservices-architecture", "prompt": "创建关于微服务架构的详细内容。描述支付网关、商户系统、运营平台和代理商系统四个核心微服务的职责边界和业务功能。解释它们如何通过RESTful API进行同步通信，以及如何利用消息队列（MQ）进行异步解耦，例如通过ResetAppConfigMQ实现配置的跨服务广播。阐述服务发现、配置中心（如果适用）和API网关（如果适用）的设计。为架构师提供设计决策背后的权衡考量，如选择微服务而非单体的原因。为开发者提供服务间调用的最佳实践和错误处理策略。", "parent_id": "", "order": 3, "progress_status": "completed", "dependent_files": "sys-payment/src/main/java/com/unipay/pay/bootstrap/PaymentApplication.java,sys-merchant/src/main/java/com/unipay/mch/bootstrap/MerchantApplication.java,sys-manager/src/main/java/com/unipay/mgr/bootstrap/ManagerApplication.java,sys-agent/src/main/java/com/unipay/agent/bootstrap/AgentApplication.java,components/components-mq/src/main/java/com/unipay/components/mq/vender/IMQSender.java", "keywords": "", "workspace_path": "", "gmt_create": "2025-09-22T20:36:07.7231824+08:00", "gmt_modified": "2025-09-22T20:50:34.8147766+08:00", "raw_data": "WikiEncrypted:m4/EFfcjYGEJ3/XH1cca9dmgdsLCTTi+/lx9X7r2euVGbycyG7T5Z8H2sYQpRGl7i+9tRAOK6EXBQf6P0ojNwmM4i2OJ8V0IRnzvVRWcKcC1ZGfL846v+EIm3caqHBxSBhBD7PDDSoXZ6fi46Emq0EHBjMsl3zzvvtL1RnAPs76VSF96J43SF8tTS8N15Hu1V97rrP2xzWvqBAUFQHL7PHErWLVTZUaHM49nWCqw8kUM+GSaJfxe4BtBQbZYqPLCchOyqjbIORs0Wu/+4mkfgOqm5g3PZ+zodTKGFbwjRDo/VQb4YO83vgi3cQLG/Q/mWyIprROayzuwoTDBewYXEF/GOpy6mzLnGP6wZ+MKbR06ou4fDMObMMBfu8zI85PIUxbzl7sc85JI/RajA9vHFHhNulK9rwg0q9+ktRSOjzgZJfboEY4mcIfR8CaES+hCkcjhiAqsngUHc9YoL6rB5yITw/pDZ9VWMbSVYfnpyYImfWzRAgmhh+QixepPAYAE+GoXlwS+Uf9EnPBkOc07n0RYBlDSWGeusfBAd0AtQHETfaKrW46jxkpTd9E6pgJkWAg5cX5vSJuekKPhNcHJpV8YWPZPsNIpWTdAwt36SSyt4HHoKPaXiEwpY4ae5lWx7V2vodcjX/wDYWtKqBnoxs//ISfOYGKTbzs47hgjGXv7ce27+AQ8U47+ExMhnkwAHpunXcjQqPXb3qO+fYks6L6mbwgI9ncdzKZTstN6qgzyKyZ+AdOUEQF/9LG8um5mMDAIy2JWGqu+ngSiTKxZAR0R76N0d2Q5zqjen8EdpQsV0NOPxSqknHguznySfu81MLb0QOSnBefMlVRb52c4DE9FOAmOqWBR5SSRCJikDCVVxd2DoELpThxVcw6mdWQiAlvJVGSTBrdnK/rWkBOcM/5cOgqhsq1r79/s2wS4qneNcTyom48AaUiUEXeOvwisG8HWeKpZ4+q9+XnaPxTNpSSUYRV12mLPoufRY2oUu3mlmpq8iBANqAfwGtfe2BUy63Yhyvi3He9ZPzks/Lzl6UWbyon9X5g/kTUEH5UbseRUtBGG6lUTUj98hNfx2zQyz8DQWWog+5ADeSN1I3e21WCgnjVytwZrMYPmv66/SD4KQSgPxvW6vf6Pr0LM6xSt69hxOMvyt85lStdQm4zkbEYLVTQ5bsfSXRn9ATpVXm8N2CrcM542LkGHfSbNr4re5ecHfduMb2iQBsLrCgEeTn64T3b070fMnPnNCpu2YocLixWNfeI0dSxCNEisD3T9OFU+YSC4OuUw5C+zgEpD+YxZtmStfj+VWI0kwy6e5y0El178wnIPGKmp0pPTMH6eybLInwlhMBLNQ7LYMxkXd39oH2pHvhUZdry58EPSNuf/7f4Db8QJzmi9U3EoYpzVz9N6YFDvCDsjXGPvpGO6EZsAaQR4ufigUIXH6dHZrBAA+0zHg/SwNx1Y1VQYjJgjLbqGQ5RE8rsctslLO8iOFiIQ00N/y512O/Zn5i3O3zpXbncfRVbupfUYLZcQWPB0cVdhFFkcSDegKlHgVRclACI9Q3XKWBY+sxlrB/vN7DkMUeHHS9AKcuI9KH8PNBnVY5TnEX66pc6DX0ZqW3y2IvvToUqjpkaPcYp0tkCX5MjHzdPzWdSXN6ZlpkWFcRpGpbc/QRmaH0xNTnyiuKW9yCnI7YqXLw83CVw/c2wmtuVvqDMj6izeSp215sDYWZxtFxO9PYxbwN24qJKrfW5paQ==", "layer_level": 0}, {"id": "3fa8e6c1-d858-496d-8d38-efe8ff173028", "repo_id": "94f96537-e6cc-41d0-af2e-17841bee1253", "name": "代理商系统", "description": "agent-system", "prompt": "创建关于代理商系统的详细内容。阐述代理商系统支持多级代理模式的业务逻辑，包括代理商信息管理、下级商户管理、分润计算和报表生成。解释其如何与支付网关和运营平台交互，获取交易数据以计算分润。描述其特有的消息处理逻辑，如通过CleanMchLoginAuthCacheMQReceiver清理商户登录缓存。为开发者提供实现多级分润算法和代理商层级管理的技术细节。", "parent_id": "6c28c636-0567-4d46-9564-c10d609484e7", "order": 3, "progress_status": "completed", "dependent_files": "sys-agent/src/main/java/com/unipay/agent/bootstrap/AgentApplication.java,sys-agent/src/main/java/com/unipay/agent/ctrl/agent/AgentInfoController.java,sys-agent/src/main/java/com/unipay/agent/service/AgentInfoService.java", "keywords": "", "workspace_path": "", "gmt_create": "2025-09-22T20:36:24.8806265+08:00", "gmt_modified": "2025-09-22T21:19:21.2828252+08:00", "raw_data": "WikiEncrypted:gTiQk8pR90rzooKLiaXVuTjAE9rSWPzUd6DZOhNw8LGyB0NW5NQJPcIFJndbHLzRxRQc/iU8EEPOWX0tCt4qmovFqLATEzQOiY7IMYbcQsqTCCdbpEn13kTAmNOVwN/OwCoOzmhj/WVqDRDR0aQWg1oazbeN095c5yTzsQRMj29HTFJ1XfenNsRtPEeD3mZqmanqXMp5NleAuYgS/+K+GvUJDX6nqMdpj4OjYwqeAD1eKB5cqENjPU4a0rrKXp+Ih9jxuXG5rEgQUvUHjiDFODcxDgJtsmOjYzu27neP4pJBn4aiDXmrUwEEZOOBIQqbdFBOEl1TtfG6y/IIDV8DoHYdv6ADLs3Yprzne6/yJetI480oGhCPrBrpo/MP4Ns3sLxe1ZtN8+sSW+xI+yT/y5qp284lq3AH3WeP0IDJiMlvq3YrKiHnnxB8jm3B5mE2SsESDzDz+e0ziB8yyJ5p1gwWny0AdB8azBgtbmUDqGhHT+ck9i4DCJW9Usg0crxe1Ccxgu8XqnFsBAyZU1faVpGmSrnKfVpfk7ykU1P9cs7ZfuhSzX9MYzPLYeXn3Yh5OO4ykCCzYD2vt8ZHxZ3i3DYZKQqZ2ARalVGgBOxarflNHF4V3YMjDeUz0XG9tN4GU84lqSDns+wVlyDCPImLkFmmPgwipBHJuhMs7fd/f9hwuIO5y47gBpHwL6l/juWryjEB9GMCd9ryybyTgVpDvmeCwR5OiyLmFFMNZwkrNQ9ecJeyZkFMCskbDcn5/9vfACz8N4WhK9rE6bfJ7BsioqzRo2vQsvm6PrnVXRnO3OxlGZzHl77zZXOh6NV+9yztVMKhNS3vYWW4gKpdjTYTmA2QjTb5H5pYm0gP1hLdkFveCh4oGYEOloE1dKz83X61bWuD++4jLgiX5VvIUZiciUowzlhbtZDB70U2rSRbTg+Ep4Y1ndLfMqxst2SxCCt3A7bOAKoed/Xi5I0c10+caEKZimQsP75Ou75xy4OaReQqFjFYUaeV5jP9bdiagJi7J98A8i0F9LzBbwX9FtIjEtB88gvSKJ+ePidnV0JvfLW7QhEfHie4Kbr6bp4rudTYgUAmCZicFSBw/DHZCV5jUeHWclJsiHncUdZFUo6SKd3OI3mBkJC63tGFw48b9DNerLrjT7WPhdzOLyuY1InfLTi0VfhKaMjSB4owlrnxJBgEDboYnmHSd0uy5uFwOJ+1rp48g+zTSLJqBz266RvGBCreuDm9CzP31JJQmz632XR1y4SrGYOeiit0rJFgkQ07", "layer_level": 1}, {"id": "3b7fffd3-a40b-4535-b50e-460dab2ddd51", "repo_id": "94f96537-e6cc-41d0-af2e-17841bee1253", "name": "商户前端", "description": "frontend-architecture-merchant", "prompt": "创建关于商户前端应用的详细内容。介绍该应用作为商户日常操作界面的功能，包括应用管理、支付配置、订单查询、分账管理等。解释其与运营平台相似的UI框架但功能范围受限的设计理念。详细说明商户特有的功能如分账接收方管理(division模块)和支付测试工具(payTest)。描述支付通道配置(custom目录下组件)的实现细节。提供商户前端与支付网关API集成的最佳实践，以及如何利用测试工具验证支付流程。", "parent_id": "776d5083-fe44-4ca2-a556-e434cfd87497", "order": 3, "progress_status": "completed", "dependent_files": "unipay-web-ui/unipay-ui-merchant/src/main.ts,unipay-web-ui/unipay-ui-merchant/src/layouts/BasicLayout.vue,unipay-web-ui/unipay-ui-merchant/src/views/mchApp/List.vue,unipay-web-ui/unipay-ui-merchant/src/views/division/record/DivisionRecordPage.vue,unipay-web-ui/unipay-ui-merchant/src/views/payTest/PayTest.vue", "keywords": "", "workspace_path": "", "gmt_create": "2025-09-22T20:36:39.2715341+08:00", "gmt_modified": "2025-09-22T21:22:04.6576902+08:00", "raw_data": "WikiEncrypted:rJ/rIw0gVau8jPGqKFBAsAycLDB6WVqwg825dG7e4d5D+Ts3pcY2M54ekVRkJKk4ybHwh4+cRKPleuqsvIhFHzszkhaJXlGzcY/Topd0rISe05akzk+pXeTpo8zHjHxugqbyXWalhnKnYQbQzMQNHih8hQ5h2Tun5OOULo0QP2lJR4GItw0dnW5Ylod5Q1t9Bef+iO/WYX6Y5bScIdvDUZ0EiYuTmtSpJasuz8jy+e26TOdLWXg1FRZ/tS73ATGqjonydmnyCosjVJUJj37mZjlcn4n8dxcnHnqCG7L6seNjG++4tkh0Ckhxu7QKTf/y4+p8sBg6WjKpJIVHny4+CVmAl3QwSts720BdatAeshbVPQHBgP/wesCvPgGbPJLj14IwxQIIHnWEIMzShMueq/jiwe1vsV1r2CO7sI7yUFCJxpu6Xr+x9KzpBVt6530qWmfPpJ3XYVvaS53JoeCG5vUm5tPJtKtdXB0tHgZL+s3UxE+WYUVwzmkIcoy4fF2zWoTxjmg/50LzUrvLjstDUpUkALckAEHiIbafEs5Y++Ut2S9MpNt1gw8lMDxJwWpcAIUlYnDiuaqxg5QAxG9NnnsS7hz2M8SAr0WLr36EeAn3Onad5cZMYDKQ2tkuNkUDdySP/NtcZrGOdPLXtDeWMoeBNmjRRNz6aPHXuU7LIMKzb3S1Jjf1o/zQOlxBO5loIH+iPnOziIRYheXnU5f+EKVOcuiAejMu5RxRFkqrrsKBfV7kyrTIz+M+G+fnQj6MdxctoR0E41GuUN7GOCtD7SjaCYKIu65FXWRCSGM6gIZGMWtA7gecctSZOie335DiIJnHSWP4rUP8OZUGIBVeZMA9vfRSmIyuZALiQkV5QUcnyeTfZjoZAag2R98tv8NvNAMjy9SRSFRpmi3r8n5xFchdk4NMr/JEtwEbjYymg1M6f9/BvFLLRum794vqoBnToDCH9nf3ooXlXa9nARX1dqM1x/dmaQHXKl23/Xh1wn02vLuD8jSk8mouSc28KuhOJsN7/o4sEpOUhOMlkSV6nmsR1F7mGJXU83IWPmQ7hobRWvSeKz9A9nu8UD4y84sh3gaUKTrxmTCBwi7nfALruhZk9SDsOVQ+ntWAn+/AwpICgIVEGxkIO2tUeTXxT+Z6Wdh9KQTejolsmVZIlYucb4vzJcRQ4zcjLcx4nQB7WHNwNjLiuwXZybxmLwPXLyTIekR3zshyHUxiD6eKz0TXZsSUyOo3nHjHF1RphcF72Jv/qPz/AVtx9RIqtFfu3cFekBYhDjgk8cTCxehfITlKSsJhB0AKR6dBFCboLmGfLm82IIZss00lsl8IXj1XzQuwnnWEHVX6ENPwLfY5G0Rw1VU8YIRA6g8MabFkenLtExfUnQN5td41uGQHl33JChS0Qa577R752BPR+H4EUp8gvlxHvVGwucTOLXT46T7iws/AmDUALOZ8fYNm23plhFUY", "layer_level": 1}, {"id": "26e4236f-41c2-43aa-ace5-6fd2c1e7c4b6", "repo_id": "94f96537-e6cc-41d0-af2e-17841bee1253", "name": "配置管理", "description": "configuration-management", "prompt": "开发配置管理的详细内容，重点描述动态配置更新机制和系统参数管理功能。解释SysConfig实体的设计，包括配置项分类、数据类型和作用域。详细说明通过SysConfigController进行配置增删改查的API接口和使用方法。深入分析基于消息队列的配置同步机制：当运营平台更新配置时，如何通过ResetAppConfigMQ消息通知所有微服务实例进行本地缓存刷新。提供配置项的最佳实践、版本管理策略和灰度发布方案。包括敏感配置加密存储和访问控制的实现细节。", "parent_id": "323cc3e9-9355-4b42-b596-ce729a56c076", "order": 3, "progress_status": "completed", "dependent_files": "core/src/main/java/com/unipay/core/entity/SysConfig.java,service/src/main/java/com/unipay/service/impl/SysConfigService.java,sys-manager/src/main/java/com/unipay/mgr/ctrl/config/SysConfigController.java,components/components-mq/src/main/java/com/unipay/components/mq/model/ResetAppConfigMQ.java,sys-payment/src/main/java/com/unipay/pay/mq/ResetAppConfigMQReceiver.java", "keywords": "", "workspace_path": "", "gmt_create": "2025-09-22T20:36:52.9705448+08:00", "gmt_modified": "2025-09-22T21:21:16.4475452+08:00", "raw_data": "WikiEncrypted:aC5ZtUyEKPSxjzg//aVllPgfgMGT84WysP5aU7jVGgMDPSKvWZp7xsAQzGtWF2TB4Rm8ZVH4zR7QDXSHsvp7Ge7gTByBC5WDo4ILx6SIJjf3vF+gwKXj2J6IUMf1rQnrrmHFlvZzzwqdL6SrAIIvynjGCscVqLxREycNx6lK+WZwMiW5lJFyfK3e7wjlzOYPEV8HsrKlmj7YVVDz4i8tIW+JPPAmkbOQ/SPGCKAI6kQhxD8mfM0oaAbl4QML2FKoqG6QhayogHtcLtz3VqtRaI094wj7L1elTEpdeBbs5k6l7LNkAMxYCM9zMhj4BiRo5u+aAy0YbvnHK4JvpxIQ3m3xjtyMTMhc2+PhiwwwQR8NUMKrQUFUz7GSTXoOanlDOfPcYe7e0iECoGNXVAmGW5OglDPbO2MXKGh7isYDTG6I6XTPuEDQ+d/HPdOV4kmLzBJt7L+H6hwqfpA1aOZ46J7ZF4H8nORsbzu5R5cy0HXjQH1hC3ELGtDze9pAAfk3CylaYmf9V+5OafcrzfXHsFmKio7bbyW/KLZRzvAzRcsmQxcdlhXRk9YG5NJ7M0AP0Gv/9ZK2BvlrvkvSIzwWV2JZObpU0I0xoGaxcwc9uuyWwZNnfbrGj6CS1qP3I9qC3IqOixAupUpfoy2FNM6TSfdvoXcDR13cnXmzebdSVqG9mOk4aQqxeXQQBqE8EBks6u48b1SE1i7hfcrub5seTaeJ2IR/b51PFEdQpar53KFk3XQWa0sXvOQXabySwW+8cliqMMZIgAESqDtZj10VOcbpPRunjF+3/KQu2IDWjKeJvx4N2l7FBW2wFL4AXcFnG2qJ1az43A2K8Xg9Pk+LHJ5YbPG7hK5YGA9C9cq68081z+fS6Xqb+7dpF6xQkf1xEcHT3ch36f02qTDfwYdyBdfVe7+RZ+ULqq6rVinLlCHNxYLmT3l7Uixbn3Up/zeXvrKAXS3vKS8zjGVbY0+QMlbkapyP69tifgMboJrooeUOUEw0k+k/4jRJw6kYhEoYJsv0q5GLgi65FNx/8t/oMfXNm8QNstXW2Jxx0ISzKuiR6RYBWoGpV3QPbxxeDjL858nVcN1uOtYS/JVjMbeangMmKI3+s5J+JV5Jw0rZKQd6DHOPVjl1U56jd+ssorSvSuWdgRr21/wVjkMfDk5zNtgAj9ssStOVkvGfR28TBW78jN7q4ppWhqzF8W8ADteZC+0x6KPYpU/+TY4YrAVSJiDXEfjxcQRmFJheGgsZF/LmmWd1XQyISJBO3ZZkrkt6zYFbjDU+N6A2Ui4eaBvwCtlSQQCXIHEEHawzVWlMNZP4UZyyusxFby95pLwWnmw+vmPYn625F9R3TsYz7rPZaMLRVXstGDkiaLyHP5WjTl67LJXKt87+Qfwm20cMrvxAwaQs1xC+Cs1xIB+OBwCnINJUPHzru/qt3yP3nk02BVw+NeWUE476csYQRqU0h652g45OtTz5hU0UA/zrGpSghYWofobDFHt4W3iOupEG5kVfNxiYAk7OlLhnPCtzor6IoXtllXTemaCDFJGWXUcO+j0EPNnlMNp2vBpfBxlQ6uAmIWaTtSGSZL6/MfDvi0TqJB+Hc0iMvy9x54kNyhi81Uq0ZgN3r8ci9A3kdZqlSnJYvoO8z8WWb7pPqOy5dlyD/hkK1Fn6aeZVBRzMsemJUKL4FHM0cQ0bnb1U9AnDOvuthxfDURBGE9SRVJAiRfEH", "layer_level": 1}, {"id": "2901e53c-d906-4f20-b47a-ed0032869ede", "repo_id": "94f96537-e6cc-41d0-af2e-17841bee1253", "name": "查询API", "description": "query-api", "prompt": "开发查询API的详细内容。系统性地文档化GET /api/pay/queryOrder的接口规范，重点说明如何通过商户订单号或平台订单号查询订单状态。详细描述请求参数（QueryPayOrderRQ）、响应数据结构（QueryPayOrderRS）和认证要求。解释不同订单状态（如待支付、已支付、已关闭）的含义和流转条件。提供JSON响应示例，并列出可能的错误情况（如订单不存在）。建议轮询频率和性能优化策略，以减少对服务端的压力。", "parent_id": "fa5aad6c-5218-42b9-ae16-cb022c852b19", "order": 3, "progress_status": "completed", "dependent_files": "sys-payment/src/main/java/com/unipay/pay/ctrl/payorder/QueryOrderController.java,sys-payment/src/main/java/com/unipay/pay/rqrs/payorder/QueryPayOrderRQ.java,sys-payment/src/main/java/com/unipay/pay/rqrs/payorder/QueryPayOrderRS.java,sys-payment/src/main/resources/markdown/doc/api4.md", "keywords": "", "workspace_path": "", "gmt_create": "2025-09-22T20:37:00.3598143+08:00", "gmt_modified": "2025-09-22T21:23:15.5297381+08:00", "raw_data": "WikiEncrypted:l9Ho0Yh0r5bYH9QEePEhke3k2RN8gG332YdAL7KtHYbPchW5eM3k+qEo35lenQE28FSn/SJv9S48t1pB6HBKvlpkKHarECNoPBgNj7n3MhVUUaJbXB3paHZoDi8VCyRKlLq3MLxEPYMtkGyTGtj4ca/Fk+hb9EMaRJrmneoopbHZCsJnhLIgtVu2JJdC94J8GLmpJGgkqSXN7penL7lKkkiTJFT309ywPfIEpjydatMgIPNzEeT+vYcCU67vGyWt2I1V9oXDaYBCMg4Qa15ITN9uBSTX0ZYolTizRVoetyXupnb6+zSoiZi0KAuLleYOweBPF/nYclLMRsUt0rPlqj9KvxIi2OwOmU/tvR7z6MHI1gbpmySd7zHRjh6KhzEkGNVeb1C18/9/bAJpspyOZYECwyD59ygBOMJhbpih5XpgofVcf5lMgRyBPUXQb1GSyk07wxDpyXBQjY1FSB31r3Jetgxj/hkLvqXGEUGH2YJjc60WViwb8RKdjxMlrGwH26MR6y/iBXhBz8r7bsDNolW1EHVEBqjPgazy6j4v45i7n4dmy8NwsF3COAMRj3V1QFFrKc/DUEg4oVnHNQImWVkWShvqoHnpEmaTXfZJAcVBZ89BPx9nWfnU5sHXRLpnjH3Mue/7NCmZGLakncJdEYMzttuORNhocaNoEakD2rxL1lPY6PU4MKDJTi0LJ1rysc90gnAZvKVQdstVLxJuLMbzOA6+tWy5ugG6n4T0RXJYpzP4GllrhS5Y3b8DJr92FZ8k/pvNQGvktp8t2ZzuIpA/Dpavg8hh+g+ovMJtvC/DVCGvZeclQi2UYLsC8T53Bg28/tbgubDQLVDRvqUIM3POWK0n2MviLrQlRGff+Ea3pr2ZgnZE8q3nczjvfKTwLeDy4G3TcL5bzeJy6rHBnnpw2BDHpxwJS0Ovwc7IqUpAD0158CU99irR/F2JLExFQ4bXrknc71bli0mUBDSz10q39RZkvKvDjlYnCrmyALAmanfbz7pqdwY1JItR3MExYVpvHywAK8+0jTAb/AUS19GSkOteCH9VgH+oaMRvBTJ/ECLTWLSQb4UoaSSlOiET9iwGDhY6ggocTwp0cS6p+WWtGGQpgy5QUFYpg1ECDPzChBDbp9tKnJHVyNnSeCTBJZ+h733A8pcfHuT9YBWjUq2lb4NwMY2XgRHKGfzmR34DGSMRNJyLK7ZBD5Pmigy9g68nMrUPJOXfJUc6yTRLn8NncfpKQ1yR0d5qThiVaVczKLLLuqilTq1cuDxCuoazVlM+XS5b2foTJhZP7bcaGVWGaz6FA8tymfWJMW/cZuClxygkPLTn9IEdO3viNxcbG+uPswdOvhC9i9j49fRxjUcsY64tdBvVOSia8QJg7ATTLeycVLa0YHIhbHmBbktd", "layer_level": 1}, {"id": "b421b6f6-0693-49b7-9034-6061411d55c4", "repo_id": "94f96537-e6cc-41d0-af2e-17841bee1253", "name": "分账服务", "description": "division-service", "prompt": "开发关于分账服务的详细内容。详细说明自动分账和手动分账的实现机制。解释PayOrderDivisionProcessService如何根据分账规则生成分账请求，并通过IDivisionService调用支付渠道的分账能力。描述分账结果的异步通知处理（DivisionRecordChannelNotifyController）和分账失败的重试机制（PayOrderDivisionRecordReissueTask）。重点阐述消息队列（PayOrderDivisionMQ）在解耦支付成功与分账执行过程中的关键作用，以及如何保证分账的最终一致性。", "parent_id": "9dc2cf0f-4dd9-4667-9176-27002bba3dd6", "order": 3, "progress_status": "completed", "dependent_files": "sys-payment/src/main/java/com/unipay/pay/ctrl/division/PayOrderDivisionExecController.java,sys-payment/src/main/java/com/unipay/pay/service/PayOrderDivisionProcessService.java,sys-payment/src/main/java/com/unipay/pay/channel/IDivisionService.java,components/components-mq/src/main/java/com/unipay/components/mq/model/PayOrderDivisionMQ.java", "keywords": "", "workspace_path": "", "gmt_create": "2025-09-22T20:37:21.2881062+08:00", "gmt_modified": "2025-09-22T21:46:02.1463651+08:00", "raw_data": "WikiEncrypted:TfUUykgSV0NZhhl6ftJ5NDqw4mFi1c7WFcduCmhxnOz71M3J2bbbVqDpT+HcaHRuQT/rSjKH24mh7rGIYpGMEFqMBHDUAO3Gel04LW9zUr360I0EznplgTae7+1tUizUXoKav+F8ouKGSQ+4IctHI9VgboIyKqBjtcIrmE1MolhIYHddP2hAw7s6TuoK7Dyu+/+m9bcPRAU+gcSmXdifQGOUGAm4qn2LT/Qq/zKsP5vlJLIk9yLouifg3rjWwMli7iaJhZJ0jpr3Ig1IzwxvnhXjdeD/tLDoaQPwEbNK+2yXPrgBCavOXoMTqn/hmlwV2VF0Wuqtvsuvc9V2YUZvC/5Fl/5KhDzGl0loMVAsUSyiX7au0+w4IeEw4ysRoYxxb++yxlhIgzp+ZaK3qzO+baJJW71NcdbfUThs4+py9Ia/HnHbXkocVjaYLPep8ohAKKmcZSLwl5KGQnfR+tQz+WQt1HE+NSYhgyKwx3sfVGn9qrJGZb1jmXCWgBIKCo7W/E3AlhXLMgwMIP5k+800f8xV10I64eycNd8gIK2jFq7ssZKEANdhxK6lTkZpYW+1REnWVXo1yBiFc2wW+KfmZ2yDSM94jL8HVq63WZ3NtG4tynhOG75GEE9fg4IrNHr7+Kdq1RdhUj1jkecT9JZ9smZMHP4x58PNmj31Ss6InQQKDHpVocnCAAs1kNIqGHfGU2Cnyg5SlRHCjcyAVcLVcqoKukC8ABhfZGfAa2LKdU+k67FjQkNXiTUBVBw+zyhZ9XTITnJG/BiTKwzNVbwfz7xrJYdSaaqBC1WoJarMccKmTUUpa6iqQ/BudHLpXJi1ZviMlXJSGL4JXTtXdHmBgeNNZUEnCIWVXrFXnXOQkOlUfOaSX4plj+Bx7Re08JNq18qnbCfWe5ZKRHvcgCuGYaYYH/eZPNmgzXP6AO381G9ixMvK1B/A0rt50bql2/eiz3SnACMdpdYcc77vUtPvXDGvCUdRpA0GZa7D3mzQNy9AgZ+IbAjL1lAxV3hc2OEe+ggvT1L7MVc90UCSWwLIQryUeYXdXjwAFB45HwmmZ/zaN8htVTQqF2KhuWnDYzYJ7V9WFPFCJmc/bGsmMZxzvbaQDKLPiKRaw1/49IzTYnHaeq9rmwnKrGI/rcF4tg61Ra/Z0G40yrJIIPfiDUtyuN6DlvjcKz7oZJ6Iel/L4VYpfpyvcrmuI1wL59W4bTt9JT6OlRJubIrT1kF95QR9YK8dBs6FmDvhVanDgQKYlWYmc/MezfBNk5uArG1VmGzLm1P6JI48HFmRbek727fIMhDjiUTxTZ0YPOldym2FWyD8ltUkc/b3gjCKX239li2kAmj66e2AWDkMpaTJRpTajjpUzk563FdG/1O5SzGz+HF9gcEurQsjcC0YLXuI2m37ZTMpFIT0x9BDC4ZR3q0k22JuhBlyXIRhkEgRonCaTr2FjwKl4O89g5JqAwRyGS84rQV4aXZfDxWp14UU23fHIElUaWn98f5xMFG7jDj6hCCHmWeyGYuEWya+/tOTHdjYHHPgOlSJAaL8qWbhRoYKCw==", "layer_level": 2}, {"id": "002f1827-c350-442a-ad5f-5d6ca2548e23", "repo_id": "94f96537-e6cc-41d0-af2e-17841bee1253", "name": "订单管理", "description": "order-management", "prompt": "开发订单管理子系统的详细内容。详细说明商户系统如何通过PayOrderMchNotifyMQReceiver接收来自支付网关的支付结果异步通知，并更新本地的PayOrder和RefundOrder状态。描述PayOrderController和RefundOrderController提供的订单查询API，使商户能够获取订单的最新状态。解释消息队列在保证最终一致性中的作用，以及如何处理通知丢失或重复的情况。提供代码示例展示通知的接收和处理逻辑，并阐述订单状态机的设计。", "parent_id": "cef51d4a-e713-4964-9d91-96bee7cec865", "order": 3, "progress_status": "completed", "dependent_files": "sys-merchant/src/main/java/com/unipay/mch/ctrl/order/PayOrderController.java,sys-merchant/src/main/java/com/unipay/mch/ctrl/order/RefundOrderController.java,sys-merchant/src/main/java/com/unipay/mch/mq/PayOrderMchNotifyMQReceiver.java,core/src/main/java/com/unipay/core/entity/PayOrder.java,core/src/main/java/com/unipay/core/entity/RefundOrder.java", "keywords": "", "workspace_path": "", "gmt_create": "2025-09-22T20:37:23.5099378+08:00", "gmt_modified": "2025-09-22T21:49:11.0144308+08:00", "raw_data": "WikiEncrypted:1TcXmWRnDAgI41cGfWTUk8v9rxANqTtZkXZY5A1r5WPAFaMbhI4X/PITKzX9Rh7PNK8eOdDLenn8EAldBKRq+B3qOW81XqJEBdSpmd3TQbbfGVsTRdCi1AYmjY/jdk5jZMrJPagD9EWJ0JjJ5tQSvL7RwG2P3CM8xyucV3weDF5OtJkMX8jvOk+12ao4Kxg4GDNuQS+yar+B6k2h2nYu44dJ+ii91Alii+xwGuXqeznrhdQOnb+FbmvZsx2SsgS8dxhhdEfBMunwR8gM9MzMIpMPMkGDauJkHoz7b6rTavGcZibNs9yZNvvKyTrN9bP4OqLILQRHruxzA1oEhx27NmDLmm4frjHmRhrIVcTNegN6zPPh5DRXYBpfeecm4QXnXGomyQuUo7MUR0FS5QYbMWgdXkBBcJfuzdAUAR7dw6jEOsJKKnG/FmCeHnsr0HUIcDS125SxSTpIikr1fL2LXTXnkUWuRRLP2KBMNx6LEqL3+ornNOk7V6RvuYazZyIgiQA1A7C7VzcAFoOI8KYf9+hmw2LQ6Qt6caxkIAqoxm1adhJRA8Dwk2FcjaPUcjv0AyKOn+EsOaIWmgAvjD3JR/nCxyxKdu7d/Ua+2t6xpLHE0JHJ5tbRmJMuvz9lPNeGUle5Op+bDbOpVlXjU2NdDQY8v8f2gL3D5G7bqCkiUmwSjS2vfdM1hBw/o+DZVwFso/+6R/oy4aq3QOd24shYROtPRJX6HRYZG9Ya+ge9JRwB7UBF1Utjsc6yxZroCmBoInFU+X9x8r6RHCPIIPtfswjUChoRsh4++2k39JA91p3DCk+chRAtMQ3lS2DEBCel9rDXcGZlSv1Fn9SUwcfIpR3uX1l1E09l5cv0JBeKycnCRvCnR/9hTf1LcRdl2Q6c4ox0OHWWPPDNZx1ZYruseb6rKVMejBW775UPuj11Kvl7ktyFWLPo3rc9QiJDD/A3wMobmD3lD9tMIeoyXOR7unvyXoT160/XY0xOMV49nk4kg7HJMA9RNe/OX2C5r7igoyOVMIFbBUh3BZvJLKSSsV8CTnZPdTNcWo3RNAes4hneVzVc7sTp7eCwV5DSfxi61VWhbYzvyJFJ1m6Gul/5ssxSokpsyrzQQMA+/L/77BBxQC2JPrIsNYvJIIkFVAakZSsy3ns418lAjB/Dg01wWGOXOqxNjfSSh/gyCcqcBW3dWr/8Ej5VSHdC/i44uDr2I/VlGlwZIihhcedX4wr7r9og3l+kIOfRsVMMTObN+YwExUplHAWwqOi1KQyiZDYTI3CvT7dL0ChDQ9BEoCk0CpMrNzN4k6AqrXAIg0WQUrmU96ZtmvmdFtHbwfS/7QjTM6y+NoCazTHNxmxbgemzGCq9k5eh5nk+nlirooJo2y0c/0Z7S2E7n4n06RoKjJoRYFKdT5PdRcNB1Tz/D4lyrdFREdUTKx+hnEnSEr86TU31Jr1l6ZLkznnXIN3WhgpgmYeKAjjp4vxM14CAiqDWxcg5pcOIZJId9lB4zK3i18jqSvXyS+EwGRBVid5FIbTW7GM4nD8qoE/IV4hi+2cEjA==", "layer_level": 2}, {"id": "fd539ec1-f638-4212-9ac3-c62dd26de122", "repo_id": "94f96537-e6cc-41d0-af2e-17841bee1253", "name": "消息处理", "description": "message-processing", "prompt": "创建关于代理商系统消息处理机制的详细内容。阐述系统如何通过消息队列接收来自其他服务的异步通知，特别是CleanMchLoginAuthCacheMQ和ResetAppConfigMQ的处理流程。详细解释CleanMchLoginAuthCacheMQReceiver如何响应商户登录认证缓存清理请求，实现分布式环境下的缓存一致性。说明ResetAppConfigMQReceiver如何处理系统配置重置通知，更新本地配置状态。描述消息消费的可靠性保障机制，如异常重试、死信队列处理等。", "parent_id": "3fa8e6c1-d858-496d-8d38-efe8ff173028", "order": 3, "progress_status": "completed", "dependent_files": "sys-agent/src/main/java/com/unipay/agent/mq/CleanMchLoginAuthCacheMQReceiver.java,sys-agent/src/main/java/com/unipay/agent/mq/ResetAppConfigMQReceiver.java,components/components-mq/src/main/java/com/unipay/components/mq/model/CleanMchLoginAuthCacheMQ.java,components/components-mq/src/main/java/com/unipay/components/mq/model/ResetAppConfigMQ.java", "keywords": "", "workspace_path": "", "gmt_create": "2025-09-22T20:37:43.2414798+08:00", "gmt_modified": "2025-09-22T21:47:50.9721282+08:00", "raw_data": "WikiEncrypted:IaaHBhg4aKVK2Z27STIpfzypcVAvFEu8Nhs4TiSra1bB0Np5vebA0s5NVHKdQ5GLNLCmRP6XRucadTPxx1D84iLqvg6SYOPCQsPTWPXRryw87n1MuVd6OVOG6xG9+zFwMbcCRdk+wQfu8j3wbgNVZBeBlkpjdsd1OBWIsU+5DhgU7Ww4Y1PdBX4CE7uRp0AfC8uiMZ1F6AaqGTaSqBNq542CMlerQhWZFukb6l2YtJCDOAmFpfD1Jh13rGz/iOmWuB5au14qXrv4bvBEEK6RVWpsQQmsCpgRIBVnSKOu+7xf4pziSJl0/Kjpq9Rh+aSJVQKKhwTYtNHBMx8RaiTvzgDk+pAnjGQjBjuLGwatLo1UZhfUGq05BEDiow9HbG2Ig7wdXcBjv277yUIhjMj10uN0Q6dWasvcYLKTL5cpY6Nop7sLuEcQq/eiRcZ38Q2yyxI14N1YAfMsuCkNAq7AKvMyzt0KNvcEZUtQXdFbkVwa9uk32nydjcoKfTistyrMdUz9jhMqBUiR4DyuLxxhEzvMyi0U1D0Ad9tf5kTJ5JZX2cImoZDYVkULRyt2IPIvtjphaH0KpgjVu4u+5eW3RoANB+6YgK+IQn5UlIXLd0LtnBV1O91b98HiHJkAKdZAuigb5RJ847pGCMElfM/Siyo2wQaAL1NU7uKYggBPcTSbRBUW8lPc5siSamtW9SY9ybdEC0MII1w7yYLb8DpMPPJST0FPLP7xOM8lH1KWF83SvCswfeyn1JAWF0QIomobNgAi0lNOR+c3xGZbG15fHGjyD5tX+t4TAx+cTPvm2FvWYS1JQLSLtrCKGasSC/D7K9eIUUjV2JpdGR3AvtsPu8EOHEVX9hj3X1t3U0JsoeKFK+4urUc2uaUT8/HemCGFvbGcY5c+HiYcD3ItbttbM55YKdv9wNBe4EfNJ7WYuaE4y4OFYKzBnTrIlnBiWTzzj1FOmgkGkdn8ytgLw5ulsTbe0KEkWnj4pX04MY4y9DrvS2BV5bkIN0nFZtMESAwWuJYmHm4DWtj7u1CFtFGv1S2DL3ZplsFXT+hL3JgNpVhzYFyfaBnl8oWM9xflNGiSjuN0jUSVu8YBKZhr57xklQZnJhi7y9El/qIGW1aFIQ9dFXz0EV1Ilngp4/3oqNQJi/BgUrDfK74YFySTzcUbkyc1IbqAJI6pC2PE0izi6/nsyzstBhv8/vi2N1BmzVpK0KSgBghNb0bYKq/7UL5+WHoblmPMh5pNemVPKPF6x6ZTheMqzjnb/65sbiC9WHrwT7JnEhePlDYHv1UhKdSWIgXoVQlwY+EqMws4OtJ9q44qLyK3OkYrdF7BNk/20wUttOl7EZOYLchcWGQ3hlpPxaPXUGh6VdEVNV6EvS2r0EBfWxO0vr1+KDi/Ja8cvWK3UhcLV/2Bp7sX50oig5+nPQyZ/J6fa4bv3OmyzaZiJ4Wp3sqM47Y/QPo0vr8RCVllNfE60FEfxx4+lJK5czMDer+zEskD0DL4uFEKx03LLr4=", "layer_level": 2}, {"id": "7719ab59-8840-4057-bc14-052362895d84", "repo_id": "94f96537-e6cc-41d0-af2e-17841bee1253", "name": "系统监控", "description": "operation-platform-system-monitoring", "prompt": "创建关于运营平台系统监控的详细内容。说明MainChartController如何聚合交易数据、订单状态等关键指标，为运营人员提供可视化数据看板。解释SysLog实体类记录系统操作日志的结构设计，包括操作类型、操作人、操作时间和详情等字段。描述系统如何通过AOP切面（MethodLogAop）自动记录关键操作日志，并通过SysLogService进行持久化。介绍如何通过application.yml配置监控相关的参数，如日志级别、监控端点等。提供监控数据查询和日志分析的最佳实践。", "parent_id": "b6853ca4-49b9-45e2-a077-b5608db8020f", "order": 3, "progress_status": "completed", "dependent_files": "sys-manager/src/main/java/com/unipay/mgr/ctrl/config/MainChartController.java,core/src/main/java/com/unipay/core/entity/SysLog.java,service/src/main/java/com/unipay/service/impl/SysLogService.java,sys-manager/src/main/resources/application.yml", "keywords": "", "workspace_path": "", "gmt_create": "2025-09-22T20:37:43.4298125+08:00", "gmt_modified": "2025-09-22T21:50:40.7715921+08:00", "raw_data": "WikiEncrypted:WmYz76ZuIeWtQgFK+sghtd2gOuM588yEotExcw5y+9odW9oRrELMSIXjCIOg6Pl7NkEKOJQprI9o7imRvT4S1+GIlA+8y8fJRR5t6AR4W8efZHImIQq+7X5LZhkp+DTEKuFrwtJODMROTHQohYeFoeAdTYHLejWWXq5uzFKjCCoQKAgNPVrEXFboEwp6LxGA3oKKRxI3saIG0cjqI6yAzKEPwtLpiIHuYrcao03lNcY3hLQBmpMf414AOYakID8AqZakcDW4FIuCIXHIu19RJYkvpi+kvL1DulVigqnVqGwIqGjZSoGgyXyTJBbJ1whmddzpNDEVfIq4eCPv61navWG1sTLpA7DWZ7lUDv/ICSQNsUSjWfb3g9fIiXnI1hixwrzbrjsd97d3gX1vWxVffc/QtDaCzhgLGGuyYi8abkEDK2/H2EUYX2I/ZH2HalS6oLfu7VP7eLATJNofHHneYs7GdfXmD4Uyi3gOrRkFRDlmZ0zbCrjzdkSP3v26djMLMPx2RWTfjt3PtLCWoSmJlG7swUpug1Kq8YwbkPfi2bD0KjQ4T+IfCIAiMRdPhPnHK+4HTg+uuh8KXTzbz8/Ewg7txUJf59i3afsVwhbJOBJblbUwfRPqdSpDAfkPa1xOV6MDoLQXcq/50dte8SmIkDU8ATTjYtt7aM/GP5Fiq9BZV/8um4qc5veuRqVtDVqdx1l12yj4yS+ZluwLbnYssWz02euuDSz+6b1aOq0MkAEF6OCFxRpug85c+83C2LSZThEjPV0knPmsfd8xWoMyRiZXEQHa6yOXTU6MbJAiXDsXwo7bqq0fMOtP4IhcyiOuh6mmk/PNOfzCLAqgMvJ/YmQoNW/sXhioMqeEsMt+NZ90pozZbonCWSWxWqXb9Fk3tZxrxwpjgQeM0R4BRDRcaJC4BY5LEBSk5HS0YZ/q742d6C/EARXG8ACxcGkmKL8BJZXWZ9UzBxsyBOYvVbeEfqJmTKvJDr+xNgvY9x9Ut9Y+ypzz8dI70mmwHA6ncGd/PLmzseDvpvTgknxhhJIpH3KcQ/iVtC+2c3o1YO68U/C1MqO6T3Qg3uBJTCxhjRjZ1dhni/3S1i/iS3pg15sjIquJGwDS9uFatjREj5wTtLEWITSzeBgJYulGWLaq0/0t0x0noZfSJIVfkFhdlTgBJGzypJJZ2r9C7sgpNJcGiLMLsNyPvJytgM1fyGF4rIatoV5t6BjPVbwwRRmtjBRZUpkInKLmX+IMH5PZV8rBvCntge+YBWTNucT0gB6xxooJ+/UvZvfFUZhwEysRVweZch2J2CIBeLgee6CAP5kP2HNwhJiLfoasbYpECKMYGCay3F0Wl+cakMEBGtpbU2LCsyF3XlawBTSFgX4cMLIpy0f9zdKD2tD9wousV1TO5MTTpev5vrZ/nPVXnFMm35HIQvFGPU/OF2dVGB+394apZLa5XCGzsNyxJaNljYsEXRZP32lwmYcb6CrRJTty98zNc8rWzFt40SrXI73yBmaWXYM=", "layer_level": 2}, {"id": "3ef1d678-997c-4368-8394-6e9ec7cb5369", "repo_id": "94f96537-e6cc-41d0-af2e-17841bee1253", "name": "支付通知", "description": "payment-notification", "prompt": "开发支付通知机制的详细内容。详细说明ChannelNoticeController如何接收来自各支付渠道的异步通知，并通过IChannelNoticeService的实现类进行处理。解释通知签名验证、重复通知过滤、通知内容解析等关键步骤。描述PayMchNotifyService如何构建商户通知内容并通过消息队列(PayOrderMchNotifyMQ)进行异步推送。说明通知重试机制、失败处理策略以及商户端的确认机制。提供代码示例展示如何安全地处理支付渠道的回调通知。", "parent_id": "97769763-5625-4e00-a0ed-ae7298840dd1", "order": 3, "progress_status": "completed", "dependent_files": "sys-payment/src/main/java/com/unipay/pay/ctrl/payorder/ChannelNoticeController.java,sys-payment/src/main/java/com/unipay/pay/channel/IChannelNoticeService.java,components/components-mq/src/main/java/com/unipay/components/mq/model/PayOrderMchNotifyMQ.java,sys-payment/src/main/java/com/unipay/pay/service/PayMchNotifyService.java", "keywords": "", "workspace_path": "", "gmt_create": "2025-09-22T20:38:04.8330659+08:00", "gmt_modified": "2025-09-22T22:16:58.5869324+08:00", "raw_data": "WikiEncrypted:lcOTJMVsDrhWtJI8Bt/2JkKivt14RR8VxatIhaDIX1p+8wjECZv6TyO4Y0b5pNXse/WvkLmlvmrRx3bNhu2iEGgkDDHpwxGF4P7CJ3m0caIOpTNkGdNP1+WZyiw6C0OKCCMSVSaAuK6LhjCKNE00cX0eq5XwBjJd4WQLirUOXfNdczj/FgHYIMS5bWi5DZjW7iVSOhTAirk24N07o3sBkKmmsLNb+JA1QLVTTtv4L/32Zw9l4AVbEbCoy98KDsdtZx2Aml7JzxV8yttmg+b/jOQtgGKnc/YHCzN9Scctq+TuBYpNfinqUEykD0pmThsjXM1z54ctfYixgCaoOlQYSr54ZnwkA5Pz8oKDPDyjDyEMuPxefj+FcQi5Q3w10XvtOkqeOd7HmFYbF7KedY9jknUA1y1YCJq+4GYuGH0Y4sacs758Q7KHwao6psqG0TPDUR/YtQp+JwMqa5XFx2cnOWMtsRRGN7ewtilkT7cR4QTdZUkFSOMbOW2QLs4em78eY7dCl+atnDvj2+BREG940m00DEU6Kpu3dDzk8B4zEUZv/wsEtI1R8P9wfJOWL5CU1/1GttWDhmSZmEp18iqoVbUwAHV+IbmFWQ6gDsJT6Wm8wyJizaJ519xBCcOy7rtM99xO3e1+tq4nWhM1cL0FUFW2ZJQx43vLtoitC2VRn0/jxasxaIRFi/B/f0GLrTge5IxLQlrug+ZvGYKvZ1ik1qJ2SxgiGLXB5X1Wxhlfw69VxMyEIQgWdJj1lGExDjqjll85GLmzP0SK0HFwYF8su8xWAfKNjqTR53kBrRigA6Un6+60XBsCj54DksCaIInwNHRYUHkYCePAOr64TB5Mx3ZBTeWX5U/EMzTCVeBRggv9nyjHSEZv63QHSRc7Vft4g6Q8ZVUOAAK/xyzCklSj6D3/EC7g0dQv3yXhkGfximzssDVolbH98wkFyR3djTuOpVynpNxwBhBx74cR2xvfNuFgcrxbGFkMRmBcdaGQ0CEFnSrctb1cu+J204KSumj1vXODQKa3EJX/ZZpluI9fe3WHoVLkqF4yKCHEfjfBpmAS1h179vXAfXDPoP/lDvfUs4RHtfQfcHHL/uk+6BzPcMPy4uTp25fgiLGH35Iq9dfMgPe2eUsc/TvPcIS57UTInE5hfS7IYQoBGIM5kOIzEpRPMQ4QmsAvqZwgoOHUuUAy0jcu/CtQXbLj4FN9aL/c779hhlf49HIYk02MH3njDg8kQpnWyr6gVK/iMiBNhIh6Ok2EDPPbN3kDCMv/n/7Tl8Q7sKGJcK4/mi0Aj84bEu+V/RK3wnpbu4M97JO8pREB/5Ts95yAp0sEQDn0atQd6gQ4vX7aKbkMy7lsbk25CYS9m2pErPBGRvj9Bo5unCcpAIKBCtEpfbSxXJzxtVD4W3e+RoliwuWEwZYWvJw/dKGTvEeMcWyx98+VAPmFkC947IrWiVDG53vO1uqR8m7a", "layer_level": 3}, {"id": "b7ad8bfb-297e-4d0c-a07b-4a8f50e548b3", "repo_id": "94f96537-e6cc-41d0-af2e-17841bee1253", "name": "退款API", "description": "refund-api", "prompt": "开发退款API的详细内容，重点描述POST /api/pay/refund接口。详细说明全额退款与部分退款的实现差异、资金退回路径、手续费处理规则。解释退款单号生成策略、退款状态机和银行处理延迟。文档化与支付渠道的对账机制，提供退款结果异步通知的配置方法。包含退款失败的常见原因分析和补偿事务处理方案。", "parent_id": "20390a93-7ada-4338-9006-b06206c31398", "order": 3, "progress_status": "completed", "dependent_files": "sys-payment/src/main/java/com/unipay/pay/ctrl/refund/RefundOrderRQ.java,sys-payment/src/main/java/com/unipay/pay/ctrl/refund/RefundOrderRS.java,sys-payment/src/main/java/com/unipay/pay/ctrl/refund/RefundOrderController.java,sys-payment/src/main/resources/markdown/doc/api4.md", "keywords": "", "workspace_path": "", "gmt_create": "2025-09-22T20:38:08.4386783+08:00", "gmt_modified": "2025-09-22T21:52:06.0940742+08:00", "raw_data": "WikiEncrypted:IsXrw8CHTmYocng0pZ32k3/lAYvpK9kottRD5hMFQ6crxTvQJWMx5Z8qXW5TS4S7zi2Ol4uXkYhV4SzoGPaggofPt6EeA59AQqJXYcb9iQoHabjhMJ40xnbFLC1l8tidCEL+WipLHVPun8slKACbmG9k6Lsp/jvQQuYWs13zr3SytSBsZC3XUKMZlrp1TNggaNdg7H6jvroLiGxRa0pX38Dw/qbllooJjlXr00k8dOb3Z8+O1jFXZW1k7M5AoOdhO/BbzTx0IPZOJCxoZJGdWFYGK/PAVskHGslS3CEN8Xs9Ob9LabUhSv28RtAKCjSDJDp3ja/HAMeT3DPOdjxlByrMU/4u0vAa1r2lQeYPLRe8/tklg/t3RhWp5wxmnR1bDeX6dNkOTdCav1TByY8+BVH952H+nAsxfDaPUCs6WcU5/EFQ9VyGC5MB33gwRBFXT+0+3piqBDW9ZOg0IYZvhd1AlsgjxMbbXZHK4ma8HwDeOD8kGnM05xEf4nmzfRJEIQ8gSzQzp4f16VLfxSeFSHdzlCoVzOikmjXJSEke+76WsA9bg2UH+nz2wDndZwEGC1jhq6PvSyy/dqmCR2MbClPbQGHs84Q4azIZWSNkVfiZ35tqN0UWWCK6uvTkxqWgdFZidc6usfxrYF2SNgdoRdHUPtipJRnf97c7RinHK2X67SyZnLYWRgUQHAHMTeaDfwRevOTX9B/ixTjMT7ooRWyJAH0Iu+C+pTu9RJI44B9g2n0TtWypAswOGynuLkgaTo3p4+TWK/j+TEaPDiLMgTD1sIsvlIOqqYyEgnqkXGWnT5qGfa1VTERt+2ohSQM2/QfNpT5FJN5cEmB7cuEVI36vta7tVrmaJ4pHBiO3/TdgPdvlCm+MP5rj1LW8jixQVnFyEBi9J49inD7QgCXWtGYUjQffaqBtHRsBoc39DwWgtkIRRZ6epEkd2vjk0h/yhKgWcRXTqG2czZrkvDKC0h2iMZavtT4Qxlum/UJNM2tqJtDEJ0TCjXvdiTTyXFSS9BtwXO7zOpax1+twzH419fdCupRUKZwE12SEr5+3+fp+N0IvmDayjaeFVpaNY+O5b4U7Ej17UNMtmgfqMqLvfTTlcS3F9yZqI98A13LX9O691cKJbeExNv3vynB1YEGDC3C/V/4F40f5UhQyRpO0acNSsG3dfglEB9f44wnygw3NZQL6YLSPqZTkpacZN7P1", "layer_level": 2}, {"id": "cdf28e0f-1f1d-4128-85d0-9f2b9cad2ebd", "repo_id": "94f96537-e6cc-41d0-af2e-17841bee1253", "name": "转账失败重发", "description": "transfer-reissue", "prompt": "开发关于转账失败自动重发机制的详细内容。说明TransferOrderReissueTask定时任务如何扫描待重发的转账订单，包括查询条件、重试次数限制、间隔策略等。解释TransferOrderReissueService执行重发的核心逻辑，以及PayOrderReissueMQ消息在系统间传递重发指令的作用。描述重发过程中的异常处理机制和最终一致性保障措施。提供监控重发任务执行情况的方法和指标。", "parent_id": "52c46886-7235-4b60-a9bc-07e0d54bc939", "order": 3, "progress_status": "completed", "dependent_files": "sys-payment/src/main/java/com/unipay/pay/service/TransferOrderReissueService.java,sys-payment/src/main/java/com/unipay/pay/task/TransferOrderReissueTask.java,components/components-mq/src/main/java/com/unipay/components/mq/model/PayOrderReissueMQ.java", "keywords": "", "workspace_path": "", "gmt_create": "2025-09-22T20:38:25.4435494+08:00", "gmt_modified": "2025-09-22T22:19:10.5308572+08:00", "raw_data": "WikiEncrypted:5TgAtbdUXqbKL9lWGANcdUmtTJCwhFb6Buqnvm4plzYxNzi7llu5oDAnZp1MdEMZLG5FsF/FrP5CsLRvXPY1pg2TpJQSP1VUww1bVQLe5/PXUtrqTJ6I6rE2gPp+EQ4ullZmBu5NSFxsocsqWGAQ7GXBWBLbarL0Ky5c+JyBK73PNeBlZkSc9+joyvoUVc7QLL3GfrTGO/wVNqbUAjy41CU5mjPOYN0qk1a4qcWR0dkENr+T3ooq+2wz4aOLfr9xjfsy6CYEouKnrKBMkrmbsvbuKc/wsRtljBN27ErwERsunKqHvJZ3e+q9M69ZAf5+QeNFeJhFXBdL45tyit3NZeriqgaN3gVXdTAMM9oSX8o/+o+L93Fq3UPH4MQs76oELo1Sny5QzIcVYZi2j35AVsR0jqsh5ajxAv90sDEg8ECRUQjvhwaxoLzu682yXnXltb+bxQz6OJl0PhTLX2W9nrHaSnneXF26xwXX7JjPweIi6TRghfg95DeuY/bLihmOXhsE7uNuuxjyYCAHEatEEYuNAkQZm4HhHf76tVZARmNwOYZMP5MatZz/j3jrYVCy0r9Li+2sAteztZrMmbV0Rc1isY10GfFqgHQLyVESd81RiIIisv/rPqvyGPVPfw9asMnS+vbNRerD2RTHDkkNFz9qkGC2OSr0Yz5YXyWLYTtCiSUPck1bq2BV0JBanhwtLObCGVzFzraaTy+Xtd3aW2zCNI3FtNcjSWAx9S+O8tZo172ZkSwM5KeyAdvv0r3N35CVsYxrVXP1cajUgiDgJBoWiiPR+pcB6kGvPLCp4Vg7pa/o8H9TUN8N7qSuth7fJ+akblT7GsR6kAuZxZBh1p8xlSoSZ16pM5XNl3Y34W/ijiUD61rD1RRI6m8xwYbkJX6zKyugKN4WAXBu01kDBQ6JOPB1f/D8O70hx5tY+wBvYMDL5pL6dQ/tGKMdsGEAt6QHRWWtsrzw1L4TxmhdV+sRMxOwI1U4S8pqZotkH4PpdzVathdIcT7KmHVd267pyxW1aXofcwFumEnp8TXwkWMJ4ntS1IrZ0Dg1byWQlbxu9mD51qnCHzBfwDjmWmHfhomJem33py7UJ9qq89UNzrRCtO1PY5aJDaEdg8fpU4rkvOwmW6FwrGdMJhZm61SGgDLx7HwxijHsT5/DFzE6PNCgDHIgBpdXMLJ8w8YD2avZQffFJxhFrJMwnQOUulE/UipoqkV0E6YffCCDwnYu9wOycFJHJYeDdFp3elSDKRw=", "layer_level": 3}, {"id": "776d5083-fe44-4ca2-a556-e434cfd87497", "repo_id": "94f96537-e6cc-41d0-af2e-17841bee1253", "name": "前端架构", "description": "frontend-architecture", "prompt": "创建关于前端架构的详细内容。介绍基于Vue.js的单页应用（SPA）设计，以及Vite带来的现代化开发体验。解释四个前端应用（代理商、收银台、运营、商户）的用户角色和功能差异。描述项目采用的组件化开发模式，如JeepayTable、JeepayLayout等可复用UI组件的设计和使用。说明路由管理、状态管理（Vuex/Pinia）和API请求封装的实现。为前端开发者提供主题定制、组件扩展和性能优化的指导。", "parent_id": "", "order": 4, "progress_status": "completed", "dependent_files": "unipay-web-ui/unipay-ui-agent/src/main.ts,unipay-web-ui/unipay-ui-cashier/src/main.js,unipay-web-ui/unipay-ui-manager/src/main.ts,unipay-web-ui/unipay-ui-merchant/src/main.ts,unipay-web-ui/unipay-ui-agent/src/layouts/BasicLayout.vue", "keywords": "", "workspace_path": "", "gmt_create": "2025-09-22T20:36:07.7282257+08:00", "gmt_modified": "2025-09-22T20:49:12.1867064+08:00", "raw_data": "WikiEncrypted:rJ/rIw0gVau8jPGqKFBAsMR2XDSFypkKHZQ6YBOgiEB18etPNKW/AKXxDO5P260N8cfR8zM/FPLMKEziA2ENABoOhzgUM7/f+hLCdYVp1TTIZzPk0DV1rzn5csqZf/k9Hv664PqgkF7eC3FOM+f9vwBwIpjb3Q1ukZatEMduwYhlrMWi6vN0RsU04kweZ6YIijIt0DPzlbY2XM9CE3gFNarSiOAQLRNodz1v+jh8mpX+VCTzxHlURzwg5la7b0VhCh1YaU//KmQS7vCzN/SeyCdkGMyiS1+w/uGkeUZLui/ZtN0arQHybJSBB7HaSuZaaTvSoEk6UGEXe88dkVo92oXbXRZ2v1j4CxMZHCo4tFJ/tpL2jqYAZry/+oNKvqA9hQsJNkbCqF+mfFtMNQlK5juiE6gV99I9ecScKkEI7yR6VloJ7HBoNKKmjW4/bSmv+emK81jfaB+CoOcbF5cuOzb50xVz+tcpScQF7Rp8IX2WrjOiVmgOWfhLCV5d6IbUwBax2g9xkJw8KR20l6bntzWfcY/+bl3AiuxP4oSYu/PDVJJo6+WNsvb9xGI5F09M7JEcHIIBzd2+pGzyrL1ulp1K4Z/2Hs/fq+A051w5509quxCHhEHR1qr8q6azZ1rsLUFzLlkp1aBxWEfVYqDo0VhAl/4QJpDNmoG0sJTJyGzR/TCleZh5UjH5C+dL3lgUaFtiZthRzY3yai1Nf8pcBQoqdeeYrDQGcTf/ceX8B1OSbUDBXHSMqtB6QmAccEZPJB6jMsY9MJqQPRuMGRLIbca2mok3ubwX9qYFpZKnWXSVBCyNHlubBGx0udNLkwmP6YwFMDaA+pSJNgBv9T6GqybllMv2/zh2al7+Cx/+5JSdBU5t8u7ZIV2DScu1T8zNGc3MKzugQvuEo7jO5M9kgx1HXc4vKexjjczxrdt5wfFI6wJpwjBkqZArQxaXgR+rTFJbHXQ30qpVu00zkWBnFC+DALRxIaDH76V84HAMTVD9OMdRMdgNWOc4TmzJ5z5Vhl/TZ0IZ4mOLLdsgJ+yqvsY9lU3mh/ZUqCYkduX7VcML6JuKgMhU+VPtWMskU8m6o087shEE727cbnbGF7IQsvGac60miyP+BnAVenammtlNr+oVmWOLKZi7wnVRmo2rIbonu20c7MQ2Cf3JHoKuCMNyOL/EIV3hp/J+k2Re70yRnkYPnSzfQHRpqRIajKod7QmO/cZ5NPTDLyXgDsla37uBUaNpdlMTD4Tg4bqAp/Li/03XkpIxFuM/E7ubjmIY6etDhVMlVQMSRphwTLv5OfadNEr8oUFde6KJPSEWRJV6yusJPIw/nnqlWkbfOCr107VzojQl4MPiZcgw4IHHe6zKc1VH/b9BC7lg7IVAHir5EYpPn6exRgA2vzfRHdKE", "layer_level": 0}, {"id": "7786555b-1204-4b55-8a50-0a9a6e5ea0c5", "repo_id": "94f96537-e6cc-41d0-af2e-17841bee1253", "name": "用户管理", "description": "user-management", "prompt": "创建用户管理的详细内容，重点介绍RBAC（基于角色的访问控制）权限模型的实现。详细解释SysUser、SysRole和SysEntitlement三个核心实体的关系和字段含义。描述通过SysUserController进行用户生命周期管理的API接口。深入分析WebSecurityConfig中的安全配置，包括JWT认证流程、权限校验机制和接口访问控制。说明角色权限分配的UI操作流程和后端数据持久化方式。提供自定义权限策略的扩展方法和与外部身份提供商集成的可能性。", "parent_id": "323cc3e9-9355-4b42-b596-ce729a56c076", "order": 4, "progress_status": "completed", "dependent_files": "core/src/main/java/com/unipay/core/entity/SysUser.java,core/src/main/java/com/unipay/core/entity/SysRole.java,core/src/main/java/com/unipay/core/entity/SysEntitlement.java,sys-manager/src/main/java/com/unipay/mgr/ctrl/sysuser/SysUserController.java,sys-manager/src/main/java/com/unipay/mgr/secruity/WebSecurityConfig.java", "keywords": "", "workspace_path": "", "gmt_create": "2025-09-22T20:36:52.9749842+08:00", "gmt_modified": "2025-09-22T21:24:58.3886692+08:00", "raw_data": "WikiEncrypted:piKNT601qxwAxFbFc+bbN9Jvv04A2h70W80VDYJEzNkP3Besmabio04H/3ACBdD5ZXABv04OfWGmsU9mI7L0Gh1BVkXhMzA9S0bqA2D41V2I7fI+uS28J2DdkjNwUpLL3NA4DFb7SvZrZeO6JDj/yScodNjQOwM68I/UmKJUzhEw/VwfbhYc75JEj1AM29ZUC6KxNPvWxLNMw+51LttiLg9s0DFwhAnWed82F1Kp0hACcc8X90Pcd5+tyOkmQa0yyt+mUE39lzQZQSh3uOjW7YrbuntR9fr31M5z5vyQtaAVr+tbW9WwPPauRmhxpOD5jFl0nS0WgD7yXtnjgrEIPDZLk/Gjx2Bk+EotYK6EugUNEuvI1S232ZuTaNaj1wyIgpy+O+Zph9sValbDjDduePWMmHV8Bgi65kj79FDTqGdYYvrzYmrjOdTcFF/tjpqn8y7AlLZtBZitsq2Y2uFGE3WqvGqIgo5VXD8v7Xew0LyLvw8TOXslXk+w5J6frp6ydo3UQzTmdlyIXceNZYaG1FWghFA6kDRN5/rCNmZI0hxgiu0hTN9aNYu4L55lUjXLA2sk9njLqavSngWz+NY9GedmKKm5BgL7Tb9cO5w1u/P31A60gKe5/fxh6ZY9TfDAoU8I1yIXZmKY2TxFUdEaggvWh5V8QmKxrE98BskA1TaDbCiuoe6IaYncg/b+JVCFxl6IgRVPcTZKJb5trBn6caUG8f7H4EC+oPMMwXIjU3uoxWSxNOl3IkQ2ZsjmnV9wjl5n+1LZeCw21iHFplFBx/4hABynvqg9LrdmwndiAkNIt0S0Q7EqxpeG1FWv5SkanAZZGjjsxNCwbfPfSUj2Mgx8Xzq6VV7jRqON+B6lOew+gG+xLRqrAoOj1W479rWX+S6+9gnT0kxNa/i/XFaC/MqXSa8Tq8TpLT2m2yzrpcYyOHiw19zABcFdOmgiUqPFnnO4PzBkxzCE6TbEG2uKcw5WSSYGpgj+Aq3zcuLo0st+5cpVVrZAnCwmHj4m6VkCfj5vS1wVi0RUAXqAW/Gc+JfAYL5rOLZBpudo2USUYC2CQmD9zsopazUHUfoSmbcEDZMmMwOVvAQ7kUL2/CKB5nqT+R1nVCNiveVwQnruZ1aDmU+tnomqzUU3ztAnEKgAnYN6nnR9yQla2jzXgo30VaALhOuPrVzADuHdP58Qs0n9bi/s8AFHp73YG6cDTQ1aHD08/GPwp6k8LmV5ky5stdNTK8s+mWP3YboNNtOOMHk/Ys9KCWikvskHiv7WgArX0ik1otimKV+e2juKJmJue58U4aSRdu7CQIke391QsQomx5N9wBz4tFtlf0gP4uoKiN6eLnzBDVxCXgEIBOcKajET86YOZmH4QbiHtLNOQAthSA1qvz+dZIyAKWnCJuO9pjNMCcggbOHik+UJHzHvccG7csBknPFKVEk4salQsjECG9HAbe2V3LypJBUBrdF6xtdGsqHt2exDjW5WS+++UR4HKFpksp+w+kIMtOLveLMScvJzJgaDFdpRrBDtog0RlnOVBh7zAekaDoyUF7z7rCUdXi8xMhbQgJaoAYqhzR6ncZLmAT8Fc04OpLcfD5Np", "layer_level": 1}, {"id": "c336f589-5a99-4f78-b9d1-fca139739953", "repo_id": "94f96537-e6cc-41d0-af2e-17841bee1253", "name": "渠道集成", "description": "channel-integration", "prompt": "创建关于渠道集成的详细内容。阐述系统如何通过策略模式和工厂模式集成多个第三方支付渠道。详细分析AlipayPaymentService和WxpayPaymentService的实现，包括API调用、签名验证、结果解析等公共逻辑的抽象（AbstractPaymentService）。解释如何通过配置动态启用或禁用某个支付渠道。为开发者提供添加新支付渠道（如银联、PayPal）的完整指南，包括需要实现的接口、配置项以及测试方法。", "parent_id": "9dc2cf0f-4dd9-4667-9176-27002bba3dd6", "order": 4, "progress_status": "completed", "dependent_files": "sys-payment/src/main/java/com/unipay/pay/channel/alipay/AlipayPaymentService.java,sys-payment/src/main/java/com/unipay/pay/channel/wxpay/WxpayPaymentService.java,sys-payment/src/main/java/com/unipay/pay/channel/xxpay/XxpayPaymentService.java,sys-payment/src/main/java/com/unipay/pay/channel/ysfpay/YsfpayPaymentService.java", "keywords": "", "workspace_path": "", "gmt_create": "2025-09-22T20:37:21.292117+08:00", "gmt_modified": "2025-09-22T21:53:12.4293401+08:00", "raw_data": "WikiEncrypted:xY3VEUQCIIVBr8vVlQSD5EyaVgY3vDwAh89F6HFBR5cnZ1LvkqHZRFUvjQzMzTwu9l/y9angeh8GoNNSxBF88HNt3fvEYeDJQ3G7/+FtOSeBn37Yxe7WF4A920SWEOq2HbioQy8Q2afYn82Lk3OYpp55INyRpV75dX9OgdvMu7Xr+wt6wdSkRQFYGcDJLTvryoII3TsNlfjaGdYQmNk9I2NVjOfvy+++kUEvaI0r8kikiICRT2dsyE6LmN3KeeVBYb1eK/OwqOp2KQ0LhoTrSLZH/SZs+1i8Gu8v8HR9jWNu6ePr5wyRHeawh3l26HNssL2fJOG6Xsy9pSIvttK4VXE6xWnwtIqKTpEi1NXX1j/DtpjJo9qX98V+M9+VDXRQoz10vYd56Ks+lPeKzr7/fpj5S4rgzwllg9GS8tpabVxCkZ8pr54n5atFwe3MyEo0L4xhiRcV+KNFSFm9HYfVNJZZotrbUbx0D9lm3CGjDXlqHENC6A2DqXog2+Hvx2PhItTbC01GwDDn6gJ1mNXJ/1wRBg8Vj4hKG3RNrACWJvHVwbj9dWqyOBIi9MNfqgYNUm7s6ObiIFwqbsxuKrKR/a8Dp8B9GDF2ddbzVcHPqhr9SdGSm9YVaH4yawnqLfc9QAxT7PxfF1EM6yyVUHrvc2JRPrnx+LBcfEjsPV16nOOM+QF/wLHuWLQ9szqrx/YwQpAWBwxcqrnCVUCl/15Ts8CWz2MHsKkNdBMmjgXhC6dLImbZCnvzvHh624cE4NW3/faU7VfLWwdb5WUwINdpU+NbaQIDYrLzOVjbx2tZW4R2WDYvipiUkqb0qJCwp3p+4hkZbUnWZoJuiqxSKXdLh03ENI9NiGsqSK7l0DMn5itbOuEGpyM6txQcUZwKsUFRGDDKJt6rf9wSkD2B/fFZrh//mXonIiqlz+CBo/IM47TmtKwFTpjsg1Qqnj2x3i2G3hl3hrlWSSj0Aikv+R9cb7JzxEcBQ7QVkfjU8tIosW1BaG+3qLXi4hN4nLMgsGDutwTVDNjibV7G/XmI4PDxtkt/9GI0bWHuBITmRaQbeMPnC+GAd3xbD2BXYECP9hsTUARvacecDNs9TxTMByECidVSn7KM//nZB0srslDM1/eQCOEMANmm7AgGBkD7k5yst3gxmJyR4M2+2rTPuukIoj12ndF30anDM+NFABwYT1pkXakrkJa17Ayx3eDSEZLc+0diyStg9J9lCMZS5NKM9M+NAOtbTdIXoE/By+Z7CjN41bKMNsoRpWYyATdOP9NiPEb9eB4clkTb1ysPx8hlz2mv5ysPS7IsxsN+qqFyzkyd1vK24yqwjF6/smN5zat7ONkP7Il0Vh+XPYTvUYINBvpLYYf3CxN61I9crVglZ3ELNBS8P14ToZk5woyz4sXMnHze94yctaoKK4PZ9eyHCBptAb99HNLAuMmMCzErTem01oo2ULtnsv+3CbWGOY8b", "layer_level": 2}, {"id": "b4004a4d-0175-4cf4-9807-952112404aa2", "repo_id": "94f96537-e6cc-41d0-af2e-17841bee1253", "name": "转账API", "description": "transfer-api", "prompt": "创建转账API的详细文档，重点描述POST /api/pay/transfer接口。说明企业付款到个人账户的业务流程、实名认证要求和风控规则。文档化不同渠道（如微信零钱、支付宝）的限额差异、到账时间和服务费率。解释转账结果的异步通知机制和对账文件生成。提供失败重试策略和异常处理指南，包括账户异常、余额不足等场景。", "parent_id": "20390a93-7ada-4338-9006-b06206c31398", "order": 4, "progress_status": "completed", "dependent_files": "sys-payment/src/main/java/com/unipay/pay/ctrl/transfer/TransferOrderRQ.java,sys-payment/src/main/java/com/unipay/pay/ctrl/transfer/TransferOrderRS.java,sys-payment/src/main/java/com/unipay/pay/ctrl/transfer/TransferOrderController.java,sys-payment/src/main/resources/markdown/doc/api5.md", "keywords": "", "workspace_path": "", "gmt_create": "2025-09-22T20:38:08.5011817+08:00", "gmt_modified": "2025-09-22T21:54:36.191734+08:00", "raw_data": "WikiEncrypted:5TgAtbdUXqbKL9lWGANcdUfqP+lAWNpSuRjI12kKIVUK3D9IxlUFVW4Y4Wxnt6JxqipA70LgAtTMwztzADx8OHTWOJldEXpjYjymeSIitYL6hO1gYCfcedCoVcAS0yAW95nqpbhWZi2seGXmGW4oFbvwaIhP44YSS0ARc/Apk98pBXGaMzEveI76pqWgka2EMkipfYFEJa/r0wSDZov2VK2N4mDKCfSxAdkmMe2FU7YM464Ak1K94iWAib+RkBVpDaaWHMFdRzoFftNtaD8FmtTzHbKXjJOiV7LJeUvhUgjeHCslaMvMG7Yf8w0Lgfl1EodBa+ZlcdjkrLjjTEhTygbKnxglhD7vC9TuM7+3nO6+xrvFeRy10/EKfCEE7Omfo89BmkOMK46K071wEc2M61QdgPvMIKQTxsqTvBFgJmf1OLS4HbPWnBviq/AhaN6mvF0SSOpLrUXQLvXwNjiar9D+Tpw3PAXpcYGvpN15soF7iM/VAuqLwqLk15S+7AbD0ndPUiQjlUanxNnWObQYqa39ItTmhzaU+6SexhwTw7fbQDJBhp/tjO0+6+V91K4GxJaoSKW+J+c5s0lo9GEgg6JvYTrimQEvFRg3xyS3LrheF7PTaj43gvrRX7fVW/RV2fZ4ssGWD4WsB+HjwVd0UxZvUqI0aakIp6gA0I1uN81yt/97fu5jak1z3ccis6SAup8zxFLtQtgA/oCclf1Guyj/Rm9Mlw2kNWY3POBGBxJEMhXfWqgspB1OSQoeiCOe5C3dyHJbSt+wMZwAufIFJMXbcW9Xe1eFZmAIMtUiY2NC5G8zLF7waQLupncr5rZXmlXjB/8Oj2iLiR68WZK9B+GyG/4f0eutggJhdqYtZOZbLsvt0nUzBuTkJxitencitLu6bjLUSYnFIbHUOyc5R9na7pKzUwUfm3NkH06OMNLY3Ant0Yr4NWB00so/jYPtIQ+kd4FxbWFoCBHrsbYFA9bkGyUbSfg2Hq95szXrf/XQEm94FwxBAxM2XZBd6K31bJMKgWa2MUc4eJDps7i/zA2Mhn4WLVfvppH/ocwsXmhFwxyuQAbRRB9N8sXN4R+orm8oDKLdHyP2RyNQNsTWKFIaXn5WwlzomqnGxMmMz2G+HuvsKd6GfcSBw8loa8rxGG8P9VEAciIC65hXr+Mlmicv2iGRBGsYPL+7xMyT9YZSiEonVmeFupTR7DMk2mcm1R91UYXZ3gW1z3lnhmksYF88fJcF5iHkbBQrDl+EqtQ=", "layer_level": 2}, {"id": "323cc3e9-9355-4b42-b596-ce729a56c076", "repo_id": "94f96537-e6cc-41d0-af2e-17841bee1253", "name": "核心功能", "description": "core-features", "prompt": "创建关于核心功能的全面内容。系统性地介绍支付服务（统一下单、查询、退款）、分账功能（自动/手动分账）、多级代理体系（代理商管理、分润计算）、配置管理（动态参数）和用户管理（RBAC权限控制）五大核心功能。解释每个功能在系统中的业务价值和实现流程。为开发者提供功能模块的入口点和关键服务类。使用序列图或流程图来阐明复杂功能（如支付流程）的执行步骤。", "parent_id": "", "order": 5, "progress_status": "completed", "dependent_files": "sys-payment/src/main/java/com/unipay/pay/ctrl/payorder/PayOrderController.java,sys-payment/src/main/java/com/unipay/pay/service/PayOrderProcessService.java,components/components-mq/src/main/java/com/unipay/components/mq/model/PayOrderDivisionMQ.java,sys-payment/src/main/java/com/unipay/pay/service/PayOrderDivisionProcessService.java,core/src/main/java/com/unipay/core/entity/AgentInfo.java,sys-agent/src/main/java/com/unipay/agent/ctrl/agent/AgentInfoController.java,sys-manager/src/main/java/com/unipay/mgr/ctrl/config/SysConfigController.java", "keywords": "", "workspace_path": "", "gmt_create": "2025-09-22T20:36:07.7346821+08:00", "gmt_modified": "2025-09-22T20:52:04.018025+08:00", "raw_data": "WikiEncrypted:luoNp8LvFa7zGThvIT9T4gfD6KbzJyTAydbcRfVv0b4BXBAF9G/unmbHb0x4cm5f5JJeO16NqGe5C5EiggSB7gQlzwKXaiTA2R8y+zVR3guyx3s5VLFB4JBkSh6rQpvjjBp4CCj9QO5lxOTKkMXZAAhTYrfnYbhZTJVKWdjcf+Xj7Y8WlRwu+UONkZ+Xf1raekT2LjjK9zjO6exxZCTagg2KMw3vvaHhNxCpJqREEMvzb0mX3WafZwVtmBSXPaRK2RZRF4TKAKWelMJVjG1e1+OikR9yrTrmy5DtBSB1jBu11VChKFIrofF/Q0Bpy4vnzhyrXr/n7J6QSxgNy/CGI+K8KqwWDjsR2GPgB3NHJHPrpMGT89WQEdFiDstTMMo3mOwrt6x5BffdEeMHZkTiJU6YEodEk2GZsVeZqJhSq9cP3FK4kU9xONW6+gZoS1FtZiZ3RC6jm7a5eKGp4g55HPlE4+xgrpuLZzz4PQ4HL+VAg+hbU7rhrp+yUsOUAlGCcM5fw7MGDRQbs+V6SzuQJGoeBKvZPMd1Ut/c8OW8vYnSbX+ousBVcaa4QciN1/dP5sxR4BCd1fMQdLiZ0cFCD2YYcYtot7W1whvKkRga2tLJ83f5Kxs4AEPkg1MFHqTObeUMT2SarnYaU3Xxpj1ov+55xwQCwhn38DnFrcvv2pCCw/Gq3q4VGqWuMOLmpuEBEJggOHzVAIjDkMTbbUfwnHcONeNo1eeLD7zs1R13TCORJqztyBO5if3Atrjfi9e16l7Mf3dZ/aRZKwXCukYbqfUIbCuTmyt1oAehrgfDKzB2K4P4NiHeLTqVtL/vo3FHOMEtd4g1zNUjdwyCMflMjfyRd/1D0/iaj97DJKvFct9aVRUrj0RA4gEYUhlem6qpbEbV0RVD7bydkp0mzqnSwLf3N1EhPVObh8u400VkefEMVTmpBXSvMIhH4G6gl1VKb/tZ8OPX46U4cQBKmpqteRvUULwr7gp2YMWD0xe3FrQSgpHXfNS5KOk5j/wWG8mIWDtqpgEdOyVhgEtq2yC643bN0qnxylUBxG5WoL8CkTM+lhiXv86YVrW2Ju+mwU6QemaXld9S4Vn/pNL8jjyW7821zxcCnfAPIqO+h2RSdN9non2sP2CRgroyzvfO99wq06f5qd1q2ZAs5PLrnEm02v9k8gk92+d3CDGU39noXuFX38x3Buflrvc8YF95EC5PlYUmvfCCRbjChxX3uCosYFpcw/hMAxCYvhLCysTiPhs9iL1ez9O+t0DO+tbwlTNYvS0vF0EicCCwYFy6M0VZzh654XTQdq99xi5a9hs69NDzGJ4F7HfChkI9W5C2QY9hDapwgosvSI1+iZin9uLhliCKyhpIrartYmZL/026ITvrXC029cguQ6VLUBxjdvWrrdYXOUOCGVSb3o0iRp3dE6jrN7VTVDcPphaJg9QMyorAkkJdxc/7mmh42CtggdD8/UraLDR3pzF/3yoCOBF6pjR0ne8C3QFaq8G6nebUf/VwSNxgcdxuD3O8YdInLCGywk1ftv4/e8u4yAv9H0Wkj7oU8l3uRst9BYMj4hds72jfOiF3VYF8anqfSnQ6KkS71Nn/5+FXimASimLBoD3g1Cv8e1UTjBfDRktf2+gAKZa5ALCQFu9A+7fY+VT/rScI96FoTcCZ9FKXVBwSk9GlQlNDivp1ixqGoD9FbchD4LlLaA74jhfRmoKsAkzr1nnbxA/qXcV5hktvhq+OEg0jqaPrH7aqYf6KIDNerOWsnahcPDFCxFUHWW536RjgUChr", "layer_level": 0}, {"id": "dfbc8a23-79a9-4c08-9c57-06bf79332dd1", "repo_id": "94f96537-e6cc-41d0-af2e-17841bee1253", "name": "数据库设计", "description": "database-design", "prompt": "创建关于数据库设计的详细内容。全面介绍核心数据模型，包括支付订单(PayOrder)、退款订单(RefundOrder)、转账订单(TransferOrder)、商户信息(MchInfo)和代理商信息(AgentInfo)等实体。详细说明各表的字段定义、数据类型、主外键关系和索引策略。解释数据库范式设计和为性能考虑所做的反范式化处理。提供ER图来可视化表间关系。为DBA和开发者提供数据访问模式、查询优化和事务管理的建议。", "parent_id": "", "order": 6, "progress_status": "completed", "dependent_files": "z-docs/sql/init.sql,z-docs/sql/patch.sql,core/src/main/java/com/unipay/core/entity/PayOrder.java,core/src/main/java/com/unipay/core/entity/MchInfo.java,core/src/main/java/com/unipay/core/entity/AgentInfo.java", "keywords": "", "workspace_path": "", "gmt_create": "2025-09-22T20:36:07.7391004+08:00", "gmt_modified": "2025-09-22T20:57:54.9090486+08:00", "raw_data": "WikiEncrypted:veTYwq2y4io5qXerCTrkkBwdhOAmcMQTz+AJLOFORhNFeFTAN7xitXLxeRr3T1sKrh0z/6NFLuxfhi+XJSCnWrbsWuoVhwzPJ6MLBMyuL/0XA87He8rSvQ6NhjByvej47w5xPUAWRWs6ExBx0zoCssrZyxUEjPe9eL0mBACKIO4/EAH5f36yEo5owBHBetUw/Eh6VorcKS6SyoiCr2K9FDCiB3oJPS1V7wmG99/hBgOKQVddOPJFOA13jURkjIQKKZ4BIDNLS5HFYmcdUGmLk0bMjPB93u4gHpOIsOkOQ9iXERESkZMeZRqbT0uenPH9fM4TTqHkyjk69jh8ilTPb6pm1nfNxZsFOgtDEz2uScrerJ8/uvS+x0YHflLv1EcF7UfofpyfTI83tWmOMRv4cIrRHlTfLB0M+OuNRK+LTcP2zEo7Wx1h/8uTl++o47GGM/JCsp3Xcjr3ZJ+EydbBkSY+F6I1IM+XuE3AddNIa80aCi16tqOWh2bTkTMKGkZep5DTrhNBMQoJ1xw6Vz5/CBYYT353XHw5UzHXiRwY+KG8H9KUrNKPlBGGakM7x20Ecc9mbxg8jKymkDFVYKbLD6sjBsnEmfkAZ4SSyd0KVIhmxyE7donwUHLEMilWnW7CqZDRvHta3cn4E6+7yBK0Bs1Fa2JfolvFmrPDhI4WTTAFVcwXStGk7OwFGTsAqpfqBSqB+YkATURlo626ZU6olPSv1NGkektUComlGU4K9zaDg9lfpgD1HPXWW/HY707+ygG0zFb6TF/yZoKdfAeFr+zYsuAgp8HkDN+9+DP05AWqBZ/LBx3MG/GjrU14THJApvMoT3d17CF4rXiBpZSPTq6fVg+a3rXzzVbXegxy1udB5fhIvgYabLoGnmDlB61toyq6z1aXxAIfeng36MVSsdPlI7LhCzxcDkQjpg+SzrFMUAgV4m6sanfV7xXGXqWHHTJJreBHoSp8c9otYEmVgqB1qD1tbeCij8IgjTq/0sSIwl3kuIqDyZeyIF/2WFwuPOvOtrscw45bZiu2pCzmUJr9RBdNQCbTl7C9oI36StEYvXK8dJFnEFQbITTGx89+1bJzPlMeg0UgiG2kI4Cl6qheD0rItK3CRSMxdm9QL1Oj2xnYAD8qbHaffAHRll7d35BAz5ObqKE0ezsxih6EksqZrnhiDsIXAuImh3fKNJA2pvZlnyr0jMz3BozrowMb/u8KhcFaCpHTI4wT5BsCitKpnLo5mE7Sa+yr8LMplKp1gMJbsCEFQKbb6ugY9Ah5j35nKac+cmJESkABtNWyclCC64dPRVHi19DwLm2FyrIbVZNqK19hdaqK8wIBkA6lIg1Fq7ZVMx/nAjl5sSHQlPTv2YGiTPeSVnhfnrsBdjercTZvy03xa12LN/qH0DC4", "layer_level": 0}, {"id": "fa5aad6c-5218-42b9-ae16-cb022c852b19", "repo_id": "94f96537-e6cc-41d0-af2e-17841bee1253", "name": "API接口文档", "description": "api-documentation", "prompt": "创建关于API接口的详细文档。针对支付网关提供的RESTful API，系统性地文档化HTTP方法、URL路径、请求头（如认证信息）、请求体（JSON Schema）和响应体的结构。特别关注统一下单、订单查询、退款申请等核心支付API。使用api1.md等文件中的示例作为参考，提供清晰的请求/响应示例。详细说明错误码体系、签名验证机制和安全要求。为集成方提供客户端SDK的使用指南和最佳实践。", "parent_id": "", "order": 7, "progress_status": "completed", "dependent_files": "sys-payment/src/main/resources/markdown/doc/api1.md,sys-payment/src/main/resources/markdown/doc/api2.md,sys-payment/src/main/java/com/unipay/pay/ctrl/payorder/UnifiedOrderController.java,sys-payment/src/main/java/com/unipay/pay/ctrl/payorder/QueryPayOrderRQ.java,sys-payment/src/main/java/com/unipay/pay/rqrs/payorder/UnifiedOrderRS.java", "keywords": "", "workspace_path": "", "gmt_create": "2025-09-22T20:36:07.7440648+08:00", "gmt_modified": "2025-09-22T20:54:59.9862186+08:00", "raw_data": "WikiEncrypted:aJsVvsatVVT+Oax9nwx6A6Ka1Sy3/Ax3fbdr8CSPsFxZ2EWp6i+cqnOOrevL+QtbDx7fZdvtUwLW1B9c0o5HgBwthjoXEjzqnggTFYuhpgCwl3TO+/q7yx39ry0LTEZXy7uySjNvRljbzN0Hq5HytWfe8S+7HvlrQ+BVvzxppsGwwDGXvFiuLFQHnuDqbZYKFMhVHvelVhp9ODRKlXJLXjySruTV4OY1uPgKU28Qv00rOic6TOVj2mwTuFim9YhBh4cKdQjxhHRTeQ80Mq6NQuL+d9TtsIof2QwtDC+/tzoNjdQIs1M0q1gqfxf/1aFdHrFhgFDzECYwgFio1mibcREZChXksfxc3jPlbdNzsIY+jePMbFrOn9D2RaRWDwivgyKohT5dvogGCeDk0G3a2xZuhTvj0UZwivgKnjUJI1111zu6wCNiNyj76pcCvfnwCMRLbplk6LRItSCMhrO+XalVAy3VYt2xrKkgclZkMoH9bT76vLATOsUaDNUObyoVe7pzlYvaJZf43ZHU1eCFP9fk3cBG+pUTM5Z6f0++Nrf2b7wsb+sOk1TnJ6Q0+zbvdyBjU4T4LyHk81XX+HhnghMO03bMe9adu/A4TPtNhvYbigWwxbUwiYJB+1L2yZTlEoMTtyp7Dq2qAhi8NT7xY3bT1meej8ydsir110JUiVCHAb9zL5jjiRyGnfj/hFOedcdQ72c6Zb2EF+ShDLJCXF6wv3YArbDZWgZm2vHhA4VKuHin2UzF5Vyvp7Pn114ILAL6T1BIipaRRPqCN51DklQtsPXz8O86a3g9odqPZIToPI+BF+x0lbIS5N8mPmZHKRN59B8k4oiRyFbuvhcg1k9O+Vpei4Jan3KH1ibtmJh4Rg43DOWrZ7+jQ/HO0/OarZYXZQkr+Eb9CR5Lte06mkctzgc3wmy5HvA4dS5fosbJeCRSDgmGwDIfdjBnbkxXZG6/QyFXx2NDH5CWet/HlBwTH6rqe1r6GAE0VeXoLLWioKObFgkx9zi8IgMlZb7nS7S1BIk08n+l2vteELp5FF+LaQPMECPPBQyhBNcoBuwtotkaIXyeGoSJC9MQPGxGjzwU3eAMeeoHSZpIAxs3z1luGuYjZZ5aTEm5Q3LPPQdec+Zcuh3QYmTJUN6kC5Tj8FejnTU4ViusrPZzJFa0uYhaYfRsOHdctFju0GUIkd9Y41XKhosr6oNL6qbUmgX1W/LSeOkeBZNMXK2BUrq6EhLfCTLoB3vUJChth0Mz3TVPyNNKzOBB5jNsr2ipXDPqeZ3yodyRS7b6iLfulKjnAfw8Li2U3MgnUauYcu/HIMYdweTIY6E/sHiYVVJexNjJTFRjJAQZMK7KFXKOYwXC7JkM4sCKlo3892nJtbGAI/L2dRMXygFFn8srBcQ4iYwshhX+uhqYs/87M2OQofPKc/OUWTqfAaU0UCcNB03QKE92H7y4KuFy6NC7gFUDO78YDfjBisd9odCTma5UZ3GApnO19J0/POlmaQk4gxzQJFJRv7WZQl0s/mKlEiDAKGsz", "layer_level": 0}, {"id": "ec1607f7-2606-4bfd-acfc-daffd521aed2", "repo_id": "94f96537-e6cc-41d0-af2e-17841bee1253", "name": "消息队列设计", "description": "message-queue-design", "prompt": "创建关于消息队列设计的详细内容。阐述系统为何采用消息队列（解耦、异步、削峰）以及支持的多种MQ实现（ActiveMQ, RabbitMQ, RocketMQ）。介绍消息生产者(IMQSender)和消费者(IMQMsgReceiver)的抽象设计，以及如何通过工厂模式实现不同MQ的切换。详细说明关键消息类型，如支付通知(PayOrderMchNotifyMQ)、分账指令(PayOrderDivisionMQ)和配置重置(ResetAppConfigMQ)的结构和处理流程。为开发者提供消息可靠性（持久化、ACK）、顺序性和幂等性处理的实现方案。", "parent_id": "", "order": 8, "progress_status": "completed", "dependent_files": "components/components-mq/src/main/java/com/unipay/components/mq/vender/IMQSender.java,components/components-mq/src/main/java/com/unipay/components/mq/vender/IMQMsgReceiver.java,components/components-mq/src/main/java/com/unipay/components/mq/model/PayOrderMchNotifyMQ.java,sys-payment/src/main/java/com/unipay/pay/mq/PayOrderMchNotifyMQReceiver.java", "keywords": "", "workspace_path": "", "gmt_create": "2025-09-22T20:36:07.7492769+08:00", "gmt_modified": "2025-09-22T20:57:14.465542+08:00", "raw_data": "WikiEncrypted:IaaHBhg4aKVK2Z27STIpf6MOgaBBdRsXdYWMSlDRdHYgF/X4VcZ74rdPO0+91H4w7MIyRUmpVFLeBfvGPTbJATiSrsf9qxCXsc55Ntscc/SsZIibwY/jID6UEiQr+ObHTOFyjkR7UHRgg7qJpxCz/ha5+iKpA8JTR9u3FEUFDuOnfWqacGGQ2VOG8C+g8g2Lj9jHfhf2S8e2JFkpSUIpfTT5ye9rhail5LgLJHnsHgt9tEBaNEeSWJ3xa2SNw8La+2n92dXIxNYudDHwzaUmV7XuHO3gaP1sT/AYplMf9giA+AYsFmV4naCvFgEFk+hZntP6H+RD8f1OqEHxQoNCAB9tKEYoG1jj9iMhjftdT6VKtaDQRU6+ysa/V2MLQ3aQXO7r4RpBKXMbMyQBJ/y2co73Z9asKaUGkLbdmfngz2TGdCUqjqgjGxUXKtbP+mm8bBZIMD48y8mIOeTTYdqC3Fz0NKwNycDfvD/wnn7RZURC+COCJeITZ6R3H0e/ekuM20Wt8aM7qRxwLyRJ4gE+/zibP+8or6gEJvK+3s0P79GtaFf5obUb2zthcTQtw/qqEmSSdBn5nyFYRbLWvA4gImSG3mIQeTAJgVIjXh60qarPx0lxsy1NlvsG4KLu1HdfY1NchFTSMbCezlETONsVHgr5IiolD3rvBa2N9ulR+eQhQgN4t9B0/IsoZTtlxKQ5eWdIJBoSWA826jDm9B71iUejrWo/MbNMRlRSjlRJhHVCPoRibyvY4YXpHN28Og+lNGe9oVDrkxsGV59GQ8axwRQx2OtAFF+PNydGDcwLoF87zm/TadWtGE+pLIiqhV07rZYMFMaIxEnSZAlZ7okwS4W0B+XFCimCfIaKOR/LRz/XuUxbi2L16fNiSPkuIDKWoSptYB+WxIZBn7fAePWSSCreHtS1rk8npQTsGeWldsvaEuro1RN49WMpXc4cQn5n5Asv9ShoYo5EyP2xe+2tZe76GNXb2SidSk3TS45aMmIPNz3VwhGemAx9BRJw9Kyg+qMu9KEHKHO5RyUjOYHu4GsxK2w2GRxixrTUe50VgWPM/mog7gKMBJ08CnoRd8bpY/EN/tq4IjUaN12VDzSlrS+WBYlorszK4oIGnqxAGfAOTDnQguFPWHEF3wQ8VxwqtwXppiCPRAQffLZUMTKyj/IJQU7MbhhJFy5aAzshP0MjcNwZWK/Ptjoik78eNbJU08DRcajGMOq2aadV1YFmpKdo2S2wQQ65dlz7P8420rrSu1LSQ7ztgfcoG8t4DO71JFHY4fkb9LaY8uvr2hA5e3spU/fwd6M4sm60wBriz2D5YhI+roTt9HTREt+x5BN2vslsSLWcc4eMgUvRO+q1nuLZs3oz/X08kioBARQimowfcHxAL9PE5Bl4MK4j8El9Ou0ca4If3nZsV7+dRRBHp+0H8SLVvhiZFQVTmMRzEGqtIv1h+7izPEX5+faAy2oQFfqRVRcZ/3bk9mzqZuQu7poAe0aTyocNOGxPsemXITbJvEGT1D2taRUFc4GaMx3c9rON7jJ0AXp5W/fe4CyWSUklgEBz6POTnkqZ6DhLRqzoHaSwqxXybyGw1kWjRyl5WnFrXbypHAKa9uBd+QCJLg==", "layer_level": 0}, {"id": "a9242397-20e3-4fba-ae31-1b29deaf6e23", "repo_id": "94f96537-e6cc-41d0-af2e-17841bee1253", "name": "部署指南", "description": "deployment-guide", "prompt": "创建关于部署的详细指南。提供从零开始搭建生产环境的完整步骤，包括服务器环境准备（JDK, MySQL, Redis, MQ）、数据库初始化（使用init.sql和init_database.sh）、后端服务打包（Maven）和部署（deploy.sh）、前端UI构建与发布、以及Nginx反向代理配置（nginx-unipay.conf）。解释不同部署脚本（如deploy.sh和start_tar.sh）的使用场景。为运维人员提供监控、日志收集和故障排查的初步建议。", "parent_id": "", "order": 9, "progress_status": "completed", "dependent_files": "z-docs/deploy/deploy.sh,z-docs/deploy/nginx-unipay.conf,z-docs/script/init_database.sh,start_tar.sh", "keywords": "", "workspace_path": "", "gmt_create": "2025-09-22T20:36:07.7541075+08:00", "gmt_modified": "2025-09-22T21:00:17.8212133+08:00", "raw_data": "WikiEncrypted:0IKfLNOWe9mZfG1jVts3wxq1Ie83+Lf6EMuycQSN7s3NFV5ooa3Scq2B9C67gTKq2J61WmIo8uwzopEI3o+rx0S+EmjC6IZGaO8NNNGQkkV+UGtWUfyxtN/OQwglXihotRG1z6RSnK6R8619L+YDdNoe+lWjtkRK8bcCEftfNis8X9hcGzY/PMO5t8Hy+bRE8UAg7J/+4ZP/4jXkWzgBToewBUR1nUh6L8MOb1Ki3yjquIFVldOT4ILRS3diAyx/LTnr3M96U43DcBYWSf/GOp79RsHn7dY5GuTDSZTCJHhGQiEMsIepqtmNFU7iK4I3c2P3vhvyHXIy6DrYusz1VDCYcKamxIXi0/H//m6Y4ezPzY6f+7IrFnRtmx/8Kk9nJUZ2Uv6mpgdGb1DP7ecEb6C+KIA9tFPY7yUHj4reYbScw2tFsqVLaIV/5/qnY3EQrhixxQIBsVjGbWiFvfVEj+54eZv/6RNxrS4xAlB5dB6PBFxx93WKYl+jKdR1wHpTJYCxnMDQijUsSR44oSzM3TNKpUEzF9fd7C3/qSyJgHpmw8/fFGU8n13mvFX/4udtjJoMx1KUUqosrBpBoH7WAY+JVyxOdpqWubMAgKcdYAI8dHGUPGnQ1BLFb9n3PzTakIw/sDRT+LaSR95V1O5EqWElax6ybPC01h0+Mx6CGU+rrRQapIF77LuROMKiqkp4yyYnidqyAi/NkAKmCnT3EehkoKyGeiM8g9HY7g28756WuElPb5cHAvnh/08ctGjcq+Jt48648Z/p7ETg+RRgofdqyx8cVHJVBX/lyxTkUjYdsSZQgnO3fnr3DxusV5nFG2aqPQh4QfeJ0rWGbzCv4nVTx0mG+oakU8EQZLTN/8WAUoQIb1Wx1uOpFneRH1NVdiHjvVrpAUzo6CgTiWrQ3ckRbUTyeBNx8IXWo22zvGR3BTzPesP7XAMPY8n0+6ddX60rtsGnqVU8aFLNBUkTdTbU4rN/+uwEzvr+na4POuMDYRSYaczDnZd3p4HVOPvEb3xH1EU7Zjdf9cyJxq+GdAYkP3lLOWRq4INkzoW9kMPsdvwTs7/WV1fWmTFqB+eBjHxllwNKyFuT4Yrxd/RXNlkvg79mN3BGvcJX1ynro2PK/oNc220jCoNoZ3qNSLygUKilyHoDQEjZZVR41n3ECMJJZqLTLseVj21ctdj8KK19LAdX3V1jpnwJHT/sW+VjC1XnytOiy+6lnjOXoDIGPnFQO7PAq4GMtsruLymQ53Q=", "layer_level": 0}, {"id": "610fc44b-97eb-4c3e-919c-21d8c2d45a65", "repo_id": "94f96537-e6cc-41d0-af2e-17841bee1253", "name": "开发指南", "description": "development-guide", "prompt": "创建关于开发的详细指南。为新加入的开发者提供本地开发环境搭建的完整说明，包括IDE配置、依赖安装、数据库连接和配置文件设置。提供从创建简单控制器到实现复杂业务逻辑的循序渐进的教程。利用'代码示例'目录中的文件作为实践材料，指导开发者如何实现支付请求、处理回调等常见任务。强调代码规范、调试技巧和单元测试的重要性。为团队提供贡献代码的流程和审查标准。", "parent_id": "", "order": 10, "progress_status": "completed", "dependent_files": "代码示例/快速开始指南.md,代码示例/支付流程实现教程.md,conf/devCommons/config/application.yml,sys-payment/src/main/resources/application.yml", "keywords": "", "workspace_path": "", "gmt_create": "2025-09-22T20:36:07.7584932+08:00", "gmt_modified": "2025-09-22T21:01:04.7426827+08:00", "raw_data": "WikiEncrypted:F3QgleoEfoy16cQggYe9C/ucQxiwh0okVA48AmODs5RKNS8ZgNngqmnHSFcG/1UaJxx+3dUn+EkMTpYUsOg40m/HMM6VJKNGAEYS5uNAydzCRr9bFwFPrXnaKs4PmtP1wDmZl3Ojsu271M/MFPgZBx85MfsBLnrMmh7+w8AHCkCxpmhYV6oBsluGWa1uDTEnmG1y5q8J6JkO53PR1wBVFXqntbSmzawKeVMkdi8XBnyh1tVJGEXOHyLPNvbiHqe5i/rhrlnmfFDBlvxgBhRyDpesM1NE2zVUjZ6VGG2Hs3YNkv/bcD8TpdpIzb+Y6nlFyYcFWMklNGTTvUmz+GQPK+EpaJ5Q4umd8k88ZQSRQhuNlmHdbLtaNLSDNTo5/ydhmRRddRMBAYfM2+vwcsL/gYhzFKeudbQ6eHsgwpbj61p/vitftBvyXgZCKOzHbww5e1e3TfWoevbIEG/jrNmdhfE+9pTarOfl9Q9kQUt2wz3FdoqR1ZYjJhbHGAmWpoWyAiHGCqyyjuEX8lFmzAujwONOfOvGn6KYEwDlSnKDd2/+qdXzyNdAIzGaBGQcGxzrHqpLiuyTWfSoIgt5496+hryszKt1WnHgI+EnMkeV3X0xyv7vfGtnlbD0Ue5anZBBJYlsSvAjd0LrSr4vNFESLH8DOATT56N3VUhe7Up51eKYiFVRQ6Jr1jr5PGB0Xkd4J8i0FnWFe8zUK9YHbKUx7fSEbNh94lwPSgqQTbrSIaYcC2whutf8s/gjBBCLDBxqo+/Igxmg5ZZAwBQMdOgen+E0ZzPxG6BnbmY0OnXXp2yJtux56fxhj6W8oHfmyJuJHOmOUB7e5fBLJSMoRNBPCOegT244V1Mv4j0iAglnPFLVV4yzDC6YNXEmF5keo+xDENm9IXkxz57rjCy0bvrdIeQjPJHEryT5UCKG36o9UuYqJ9mjiCwaffK8tMlAYBCvtWB/roPyVv9H/M555VE+u/pl/CZ7mYZjCEz9UTGDv3YFDbCmbE1uKpKxW6dqZa3egRMZIynYeECaDHZ3gEKUZ/ObI2CPeJfQLprlbDebLxvAGO1XYv35p5ruHC9NIVHzV23I4LpRs2LPg1lZNXmZsSOil9Sqb0C95oQKTDIRXhQPtv5vB5BAGkZ3BWPj7RoGuPTdk6zk3kqCom2AeTKAVsnx61Cwchnd2v7YB3YnWHpR9VVfztMgXBn3EMFkU+h/CM+zpaAXRVe9wfy7FcUmKWxV0le7oXc2R0voFNnwuGs=", "layer_level": 0}], "wiki_items": [{"catalog_id": "c69078a6-bdf3-4172-b633-81aef3a29dfc", "content": "", "title": "技术栈与依赖", "description": "technology-stack", "extend": "{}", "progress_status": "completed", "repo_id": "94f96537-e6cc-41d0-af2e-17841bee1253", "workspace_path": "", "id": "988f66ab-e477-4a30-99aa-d393d4e7c7c5", "gmt_create": "2025-09-22T20:40:45.7709749+08:00", "gmt_modified": "2025-09-22T20:40:45.7846027+08:00"}, {"catalog_id": "b9abe1e4-88c6-4131-9355-45ab354ef2b0", "content": "", "title": "目录结构说明", "description": "directory-structure", "extend": "{}", "progress_status": "completed", "repo_id": "94f96537-e6cc-41d0-af2e-17841bee1253", "workspace_path": "", "id": "969ed8a1-92c3-4b74-913e-bbc5ed04cef5", "gmt_create": "2025-09-22T20:43:10.3024327+08:00", "gmt_modified": "2025-09-22T20:43:10.3121426+08:00"}, {"catalog_id": "03a715da-a492-42d0-9ebe-c04cfc0eef7f", "content": "", "title": "系统概述", "description": "system-overview", "extend": "{}", "progress_status": "completed", "repo_id": "94f96537-e6cc-41d0-af2e-17841bee1253", "workspace_path": "", "id": "26f79773-56a8-44a1-a496-87f48169f73d", "gmt_create": "2025-09-22T20:44:22.7693976+08:00", "gmt_modified": "2025-09-22T20:44:22.7944668+08:00"}, {"catalog_id": "776d5083-fe44-4ca2-a556-e434cfd87497", "content": "", "title": "前端架构", "description": "frontend-architecture", "extend": "{}", "progress_status": "completed", "repo_id": "94f96537-e6cc-41d0-af2e-17841bee1253", "workspace_path": "", "id": "f6ea9ca1-e72f-49d2-9002-b468a8204836", "gmt_create": "2025-09-22T20:49:12.1737037+08:00", "gmt_modified": "2025-09-22T20:49:12.1897036+08:00"}, {"catalog_id": "6c28c636-0567-4d46-9564-c10d609484e7", "content": "", "title": "微服务架构", "description": "microservices-architecture", "extend": "{}", "progress_status": "completed", "repo_id": "94f96537-e6cc-41d0-af2e-17841bee1253", "workspace_path": "", "id": "9556590c-b951-4cba-b70e-74f8887eee7c", "gmt_create": "2025-09-22T20:50:34.7929683+08:00", "gmt_modified": "2025-09-22T20:50:34.8171499+08:00"}, {"catalog_id": "323cc3e9-9355-4b42-b596-ce729a56c076", "content": "", "title": "核心功能", "description": "core-features", "extend": "{}", "progress_status": "completed", "repo_id": "94f96537-e6cc-41d0-af2e-17841bee1253", "workspace_path": "", "id": "3de88988-e57b-404d-a7b5-8fac6383d7ce", "gmt_create": "2025-09-22T20:52:03.9967751+08:00", "gmt_modified": "2025-09-22T20:52:04.0207132+08:00"}, {"catalog_id": "fa5aad6c-5218-42b9-ae16-cb022c852b19", "content": "", "title": "API接口文档", "description": "api-documentation", "extend": "{}", "progress_status": "completed", "repo_id": "94f96537-e6cc-41d0-af2e-17841bee1253", "workspace_path": "", "id": "845f2b3e-fceb-4fe7-a0a2-4538ee4a50ec", "gmt_create": "2025-09-22T20:54:59.9758719+08:00", "gmt_modified": "2025-09-22T20:54:59.9885173+08:00"}, {"catalog_id": "ec1607f7-2606-4bfd-acfc-daffd521aed2", "content": "", "title": "消息队列设计", "description": "message-queue-design", "extend": "{}", "progress_status": "completed", "repo_id": "94f96537-e6cc-41d0-af2e-17841bee1253", "workspace_path": "", "id": "7762e899-3eb4-4645-b169-a3c616410ea3", "gmt_create": "2025-09-22T20:57:14.4483549+08:00", "gmt_modified": "2025-09-22T20:57:14.4688323+08:00"}, {"catalog_id": "dfbc8a23-79a9-4c08-9c57-06bf79332dd1", "content": "", "title": "数据库设计", "description": "database-design", "extend": "{}", "progress_status": "completed", "repo_id": "94f96537-e6cc-41d0-af2e-17841bee1253", "workspace_path": "", "id": "4d84fb7c-8714-49cc-abc0-45b739b4f01f", "gmt_create": "2025-09-22T20:57:54.897048+08:00", "gmt_modified": "2025-09-22T20:57:54.9120471+08:00"}, {"catalog_id": "a9242397-20e3-4fba-ae31-1b29deaf6e23", "content": "", "title": "部署指南", "description": "deployment-guide", "extend": "{}", "progress_status": "completed", "repo_id": "94f96537-e6cc-41d0-af2e-17841bee1253", "workspace_path": "", "id": "c2f5322a-50d2-444c-a66f-efc045a4a131", "gmt_create": "2025-09-22T21:00:17.813274+08:00", "gmt_modified": "2025-09-22T21:00:17.8238618+08:00"}, {"catalog_id": "610fc44b-97eb-4c3e-919c-21d8c2d45a65", "content": "", "title": "开发指南", "description": "development-guide", "extend": "{}", "progress_status": "completed", "repo_id": "94f96537-e6cc-41d0-af2e-17841bee1253", "workspace_path": "", "id": "e782bdf0-1c3f-4ed7-8f8c-2e1f2826241c", "gmt_create": "2025-09-22T21:01:04.7159849+08:00", "gmt_modified": "2025-09-22T21:01:04.7457435+08:00"}, {"catalog_id": "566efde5-8287-43c6-8caa-675d0d56de77", "content": "", "title": "代理商前端", "description": "frontend-architecture-agent", "extend": "{}", "progress_status": "completed", "repo_id": "94f96537-e6cc-41d0-af2e-17841bee1253", "workspace_path": "", "id": "582039cd-7465-43cd-bf89-6aa97148141f", "gmt_create": "2025-09-22T21:03:00.8235287+08:00", "gmt_modified": "2025-09-22T21:03:00.8490463+08:00"}, {"catalog_id": "9dc2cf0f-4dd9-4667-9176-27002bba3dd6", "content": "", "title": "支付网关", "description": "payment-gateway", "extend": "{}", "progress_status": "completed", "repo_id": "94f96537-e6cc-41d0-af2e-17841bee1253", "workspace_path": "", "id": "624becd0-511a-4d90-979b-634fae7fef67", "gmt_create": "2025-09-22T21:03:10.6836819+08:00", "gmt_modified": "2025-09-22T21:03:10.7081889+08:00"}, {"catalog_id": "a8b7b27a-17c1-410a-a058-55923eba01d8", "content": "", "title": "支付服务", "description": "payment-service", "extend": "{}", "progress_status": "completed", "repo_id": "94f96537-e6cc-41d0-af2e-17841bee1253", "workspace_path": "", "id": "2699efef-5c64-406f-b8a5-384baa85db6d", "gmt_create": "2025-09-22T21:05:12.9064466+08:00", "gmt_modified": "2025-09-22T21:05:12.9342565+08:00"}, {"catalog_id": "20390a93-7ada-4338-9006-b06206c31398", "content": "", "title": "支付API", "description": "payment-api", "extend": "{}", "progress_status": "completed", "repo_id": "94f96537-e6cc-41d0-af2e-17841bee1253", "workspace_path": "", "id": "856baffc-6f5d-4ecb-84ff-562f5131e809", "gmt_create": "2025-09-22T21:06:08.5687701+08:00", "gmt_modified": "2025-09-22T21:06:08.5790114+08:00"}, {"catalog_id": "febddd4e-65a1-484f-9136-623cb352fd8b", "content": "", "title": "设计原则", "description": "message-queue-principles", "extend": "{}", "progress_status": "completed", "repo_id": "94f96537-e6cc-41d0-af2e-17841bee1253", "workspace_path": "", "id": "71d4dce0-8ae5-4eb3-ac19-f3a11896ea48", "gmt_create": "2025-09-22T21:07:11.349169+08:00", "gmt_modified": "2025-09-22T21:07:11.3751921+08:00"}, {"catalog_id": "d7839cde-6041-4688-a449-fd87d4a9e88f", "content": "", "title": "收银台前端", "description": "frontend-architecture-cashier", "extend": "{}", "progress_status": "completed", "repo_id": "94f96537-e6cc-41d0-af2e-17841bee1253", "workspace_path": "", "id": "e6370a0e-8724-48a2-a11a-3063f1a4843d", "gmt_create": "2025-09-22T21:08:35.7314802+08:00", "gmt_modified": "2025-09-22T21:08:35.7581966+08:00"}, {"catalog_id": "cef51d4a-e713-4964-9d91-96bee7cec865", "content": "", "title": "商户系统", "description": "merchant-system", "extend": "{}", "progress_status": "completed", "repo_id": "94f96537-e6cc-41d0-af2e-17841bee1253", "workspace_path": "", "id": "d05089e4-040f-4bf9-81e4-7163e10e5c37", "gmt_create": "2025-09-22T21:08:52.0699513+08:00", "gmt_modified": "2025-09-22T21:08:52.0836652+08:00"}, {"catalog_id": "474e9be4-48f2-4ed7-8ed3-7d18a7fd22d9", "content": "", "title": "分账功能", "description": "division-function", "extend": "{}", "progress_status": "completed", "repo_id": "94f96537-e6cc-41d0-af2e-17841bee1253", "workspace_path": "", "id": "caf88b3a-08b1-4e50-845f-0013e2ac59e9", "gmt_create": "2025-09-22T21:11:06.8184463+08:00", "gmt_modified": "2025-09-22T21:11:06.8412881+08:00"}, {"catalog_id": "2f9b2fcd-7489-46ac-bf0f-7e70ffdb3352", "content": "", "title": "退款API", "description": "refund-api", "extend": "{}", "progress_status": "completed", "repo_id": "94f96537-e6cc-41d0-af2e-17841bee1253", "workspace_path": "", "id": "a2ee7955-4ce4-4437-9ce4-00e6273a59ea", "gmt_create": "2025-09-22T21:11:25.0318223+08:00", "gmt_modified": "2025-09-22T21:11:25.0573281+08:00"}, {"catalog_id": "b6853ca4-49b9-45e2-a077-b5608db8020f", "content": "", "title": "运营平台", "description": "operation-platform", "extend": "{}", "progress_status": "completed", "repo_id": "94f96537-e6cc-41d0-af2e-17841bee1253", "workspace_path": "", "id": "9ebbfc56-b484-4fe7-9d7f-a0fc360d0678", "gmt_create": "2025-09-22T21:13:39.0860001+08:00", "gmt_modified": "2025-09-22T21:13:39.1148209+08:00"}, {"catalog_id": "ac4b8273-8eaa-487f-b71c-c073ef98666c", "content": "", "title": "消息类型详解", "description": "message-types-detail", "extend": "{}", "progress_status": "completed", "repo_id": "94f96537-e6cc-41d0-af2e-17841bee1253", "workspace_path": "", "id": "14eff330-f980-42cf-9cce-fdbfdeb23809", "gmt_create": "2025-09-22T21:14:07.6238928+08:00", "gmt_modified": "2025-09-22T21:14:07.6496867+08:00"}, {"catalog_id": "1b2c50b4-ddcd-4397-9764-7b5660cf6ec5", "content": "", "title": "运营平台前端", "description": "frontend-architecture-manager", "extend": "{}", "progress_status": "completed", "repo_id": "94f96537-e6cc-41d0-af2e-17841bee1253", "workspace_path": "", "id": "8408dd22-433f-40e5-92dd-69222cc396ba", "gmt_create": "2025-09-22T21:16:13.367099+08:00", "gmt_modified": "2025-09-22T21:16:13.3773825+08:00"}, {"catalog_id": "6c31f0b6-271f-4662-94f7-7a7c00581bc3", "content": "", "title": "多级代理", "description": "multi-level-agent", "extend": "{}", "progress_status": "completed", "repo_id": "94f96537-e6cc-41d0-af2e-17841bee1253", "workspace_path": "", "id": "24cdeed2-3c38-46d0-aa6e-ca0344e18fad", "gmt_create": "2025-09-22T21:17:07.1363757+08:00", "gmt_modified": "2025-09-22T21:17:07.1602682+08:00"}, {"catalog_id": "fab1e9f9-cde1-42d6-bcfc-9595bd179692", "content": "", "title": "转账API", "description": "transfer-api", "extend": "{}", "progress_status": "completed", "repo_id": "94f96537-e6cc-41d0-af2e-17841bee1253", "workspace_path": "", "id": "c7ae1313-004a-4926-b17c-6d1962922981", "gmt_create": "2025-09-22T21:19:09.9629843+08:00", "gmt_modified": "2025-09-22T21:19:09.9931678+08:00"}, {"catalog_id": "3fa8e6c1-d858-496d-8d38-efe8ff173028", "content": "", "title": "代理商系统", "description": "agent-system", "extend": "{}", "progress_status": "completed", "repo_id": "94f96537-e6cc-41d0-af2e-17841bee1253", "workspace_path": "", "id": "badb52ec-5efa-4094-823b-ca5a70681a23", "gmt_create": "2025-09-22T21:19:21.262112+08:00", "gmt_modified": "2025-09-22T21:19:21.2855491+08:00"}, {"catalog_id": "26e4236f-41c2-43aa-ace5-6fd2c1e7c4b6", "content": "", "title": "配置管理", "description": "configuration-management", "extend": "{}", "progress_status": "completed", "repo_id": "94f96537-e6cc-41d0-af2e-17841bee1253", "workspace_path": "", "id": "7f5db0b6-b8fa-4159-a99a-b2edfc93a6b7", "gmt_create": "2025-09-22T21:21:16.4265986+08:00", "gmt_modified": "2025-09-22T21:21:16.4499964+08:00"}, {"catalog_id": "3b7fffd3-a40b-4535-b50e-460dab2ddd51", "content": "", "title": "商户前端", "description": "frontend-architecture-merchant", "extend": "{}", "progress_status": "completed", "repo_id": "94f96537-e6cc-41d0-af2e-17841bee1253", "workspace_path": "", "id": "a07ff904-4886-4cdd-b7f6-cdf735fce308", "gmt_create": "2025-09-22T21:22:04.6331678+08:00", "gmt_modified": "2025-09-22T21:22:04.660354+08:00"}, {"catalog_id": "2901e53c-d906-4f20-b47a-ed0032869ede", "content": "", "title": "查询API", "description": "query-api", "extend": "{}", "progress_status": "completed", "repo_id": "94f96537-e6cc-41d0-af2e-17841bee1253", "workspace_path": "", "id": "c52534cc-9cfa-4574-b177-b31fa0d5fcb0", "gmt_create": "2025-09-22T21:23:15.504754+08:00", "gmt_modified": "2025-09-22T21:23:15.53272+08:00"}, {"catalog_id": "7786555b-1204-4b55-8a50-0a9a6e5ea0c5", "content": "", "title": "用户管理", "description": "user-management", "extend": "{}", "progress_status": "completed", "repo_id": "94f96537-e6cc-41d0-af2e-17841bee1253", "workspace_path": "", "id": "d5e83b7d-e68c-4e29-bd55-961c0619f409", "gmt_create": "2025-09-22T21:24:58.3636308+08:00", "gmt_modified": "2025-09-22T21:24:58.3914289+08:00"}, {"catalog_id": "97769763-5625-4e00-a0ed-ae7298840dd1", "content": "", "title": "支付服务", "description": "payment-service", "extend": "{}", "progress_status": "completed", "repo_id": "94f96537-e6cc-41d0-af2e-17841bee1253", "workspace_path": "", "id": "f95e05eb-36fb-4787-b7b5-b29c5f981ad4", "gmt_create": "2025-09-22T21:25:37.6531759+08:00", "gmt_modified": "2025-09-22T21:25:37.6634844+08:00"}, {"catalog_id": "ca62663b-1d2d-4ca1-9625-cc960a5db571", "content": "", "title": "商户信息管理", "description": "merchant-info-management", "extend": "{}", "progress_status": "completed", "repo_id": "94f96537-e6cc-41d0-af2e-17841bee1253", "workspace_path": "", "id": "91eb7b0c-2b5e-45cf-b3e1-34fe2ab83c4b", "gmt_create": "2025-09-22T21:27:09.415114+08:00", "gmt_modified": "2025-09-22T21:27:09.4253556+08:00"}, {"catalog_id": "123bef40-0c8e-481a-8a40-24f973c3fe5a", "content": "", "title": "代理商管理", "description": "agent-management", "extend": "{}", "progress_status": "completed", "repo_id": "94f96537-e6cc-41d0-af2e-17841bee1253", "workspace_path": "", "id": "c7584c25-59fd-431f-b7e6-d95a18cf337a", "gmt_create": "2025-09-22T21:28:08.7556835+08:00", "gmt_modified": "2025-09-22T21:28:08.7682441+08:00"}, {"catalog_id": "59ab9ed0-1544-4013-93b7-2b546edb14c2", "content": "", "title": "用户管理", "description": "operation-platform-user-management", "extend": "{}", "progress_status": "completed", "repo_id": "94f96537-e6cc-41d0-af2e-17841bee1253", "workspace_path": "", "id": "8b731572-f320-415e-aee2-4b80ebc49ae6", "gmt_create": "2025-09-22T21:28:55.1525902+08:00", "gmt_modified": "2025-09-22T21:28:55.1768283+08:00"}, {"catalog_id": "918d1aa5-5ab9-4cf1-baee-3cb7f20be143", "content": "", "title": "退款服务", "description": "refund-service", "extend": "{}", "progress_status": "completed", "repo_id": "94f96537-e6cc-41d0-af2e-17841bee1253", "workspace_path": "", "id": "7ce6248a-5fc4-42d6-b2e8-e5454ba4a5fb", "gmt_create": "2025-09-22T21:30:55.524479+08:00", "gmt_modified": "2025-09-22T21:30:55.5350106+08:00"}, {"catalog_id": "5fd729f5-c512-4b2c-b6d1-bfc8d609f364", "content": "", "title": "统一下单API", "description": "unified-order-api", "extend": "{}", "progress_status": "completed", "repo_id": "94f96537-e6cc-41d0-af2e-17841bee1253", "workspace_path": "", "id": "44aceb56-fd37-46cc-96a6-ba2afcaa8786", "gmt_create": "2025-09-22T21:31:22.1429275+08:00", "gmt_modified": "2025-09-22T21:31:22.168889+08:00"}, {"catalog_id": "3f472633-1c5c-4e51-bcd2-61e622203f7a", "content": "", "title": "应用管理", "description": "mch-app-management", "extend": "{}", "progress_status": "completed", "repo_id": "94f96537-e6cc-41d0-af2e-17841bee1253", "workspace_path": "", "id": "a331d706-38a5-42ba-bd1a-0f22c33989b7", "gmt_create": "2025-09-22T21:33:12.1372541+08:00", "gmt_modified": "2025-09-22T21:33:12.1625317+08:00"}, {"catalog_id": "574d46ea-ffd8-4b8e-a21f-24c0d9111838", "content": "", "title": "分润计算", "description": "profit-calculation", "extend": "{}", "progress_status": "completed", "repo_id": "94f96537-e6cc-41d0-af2e-17841bee1253", "workspace_path": "", "id": "3cfaa846-75b9-4932-9dfe-d352d2bcfacf", "gmt_create": "2025-09-22T21:33:30.4146888+08:00", "gmt_modified": "2025-09-22T21:33:30.4454306+08:00"}, {"catalog_id": "076f142c-a9a1-49b1-b9a2-44ffd269697b", "content": "", "title": "订单查询API", "description": "query-order-api", "extend": "{}", "progress_status": "completed", "repo_id": "94f96537-e6cc-41d0-af2e-17841bee1253", "workspace_path": "", "id": "562f0a65-6ee1-43bd-b50d-9c4fb1ff0925", "gmt_create": "2025-09-22T21:36:05.8296878+08:00", "gmt_modified": "2025-09-22T21:36:05.8557183+08:00"}, {"catalog_id": "c56e8581-79d0-452c-89bc-84ce0e7577fe", "content": "", "title": "角色与权限管理", "description": "operation-platform-role-permission", "extend": "{}", "progress_status": "completed", "repo_id": "94f96537-e6cc-41d0-af2e-17841bee1253", "workspace_path": "", "id": "2fb22635-f226-4a62-95ec-8b3446fbef6e", "gmt_create": "2025-09-22T21:36:21.2843777+08:00", "gmt_modified": "2025-09-22T21:36:21.3082856+08:00"}, {"catalog_id": "e234ddf3-5bea-4e28-b7a8-2705c4806c15", "content": "", "title": "支付通道配置", "description": "pay-channel-configuration", "extend": "{}", "progress_status": "completed", "repo_id": "94f96537-e6cc-41d0-af2e-17841bee1253", "workspace_path": "", "id": "5237ed5e-145f-4a20-9d97-170566908058", "gmt_create": "2025-09-22T21:39:22.0237419+08:00", "gmt_modified": "2025-09-22T21:39:22.0503375+08:00"}, {"catalog_id": "52c46886-7235-4b60-a9bc-07e0d54bc939", "content": "", "title": "转账服务", "description": "transfer-service", "extend": "{}", "progress_status": "completed", "repo_id": "94f96537-e6cc-41d0-af2e-17841bee1253", "workspace_path": "", "id": "c01fa7ad-d479-4894-a4fe-5af83d39bae3", "gmt_create": "2025-09-22T21:39:24.1106685+08:00", "gmt_modified": "2025-09-22T21:39:24.1251372+08:00"}, {"catalog_id": "5816d61e-13c8-47b5-852f-5cd4075956e4", "content": "", "title": "系统配置管理", "description": "operation-platform-config-management", "extend": "{}", "progress_status": "completed", "repo_id": "94f96537-e6cc-41d0-af2e-17841bee1253", "workspace_path": "", "id": "e359f432-7ef9-4a4e-a6b3-c546fd4acd35", "gmt_create": "2025-09-22T21:41:29.9864405+08:00", "gmt_modified": "2025-09-22T21:41:29.996714+08:00"}, {"catalog_id": "67d56238-1329-4c27-96f7-320b6ff44c7f", "content": "", "title": "商户管理", "description": "merchant-management", "extend": "{}", "progress_status": "completed", "repo_id": "94f96537-e6cc-41d0-af2e-17841bee1253", "workspace_path": "", "id": "e92b5628-8813-4eda-9e83-1e9c81f615c5", "gmt_create": "2025-09-22T21:42:15.2060591+08:00", "gmt_modified": "2025-09-22T21:42:15.2303591+08:00"}, {"catalog_id": "f9df9f6c-743d-4cb7-98b0-3d9636ece7e1", "content": "", "title": "关闭订单API", "description": "close-order-api", "extend": "{}", "progress_status": "completed", "repo_id": "94f96537-e6cc-41d0-af2e-17841bee1253", "workspace_path": "", "id": "ee4a84a9-b193-45fb-96af-1d7d6cf12b40", "gmt_create": "2025-09-22T21:44:44.3768696+08:00", "gmt_modified": "2025-09-22T21:44:44.4027369+08:00"}, {"catalog_id": "b421b6f6-0693-49b7-9034-6061411d55c4", "content": "", "title": "分账服务", "description": "division-service", "extend": "{}", "progress_status": "completed", "repo_id": "94f96537-e6cc-41d0-af2e-17841bee1253", "workspace_path": "", "id": "29b6cef6-e23e-422f-9436-bb258a595ba5", "gmt_create": "2025-09-22T21:46:02.1391424+08:00", "gmt_modified": "2025-09-22T21:46:02.1484757+08:00"}, {"catalog_id": "fd539ec1-f638-4212-9ac3-c62dd26de122", "content": "", "title": "消息处理", "description": "message-processing", "extend": "{}", "progress_status": "completed", "repo_id": "94f96537-e6cc-41d0-af2e-17841bee1253", "workspace_path": "", "id": "01c5cedd-e7a6-478a-8f7b-2f0935d56e2f", "gmt_create": "2025-09-22T21:47:50.9641652+08:00", "gmt_modified": "2025-09-22T21:47:50.9748496+08:00"}, {"catalog_id": "002f1827-c350-442a-ad5f-5d6ca2548e23", "content": "", "title": "订单管理", "description": "order-management", "extend": "{}", "progress_status": "completed", "repo_id": "94f96537-e6cc-41d0-af2e-17841bee1253", "workspace_path": "", "id": "f6bf9e8b-e8c6-4cd5-aa6b-ce5b72cd0ec5", "gmt_create": "2025-09-22T21:49:10.9919941+08:00", "gmt_modified": "2025-09-22T21:49:11.0168271+08:00"}, {"catalog_id": "7719ab59-8840-4057-bc14-052362895d84", "content": "", "title": "系统监控", "description": "operation-platform-system-monitoring", "extend": "{}", "progress_status": "completed", "repo_id": "94f96537-e6cc-41d0-af2e-17841bee1253", "workspace_path": "", "id": "5ef2270b-d24b-493c-a77e-425b5005151b", "gmt_create": "2025-09-22T21:50:40.7641754+08:00", "gmt_modified": "2025-09-22T21:50:40.7746229+08:00"}, {"catalog_id": "b7ad8bfb-297e-4d0c-a07b-4a8f50e548b3", "content": "", "title": "退款API", "description": "refund-api", "extend": "{}", "progress_status": "completed", "repo_id": "94f96537-e6cc-41d0-af2e-17841bee1253", "workspace_path": "", "id": "f0fc0516-a1b7-4d4c-93ca-2ab8b155b88a", "gmt_create": "2025-09-22T21:52:06.0877447+08:00", "gmt_modified": "2025-09-22T21:52:06.096338+08:00"}, {"catalog_id": "c336f589-5a99-4f78-b9d1-fca139739953", "content": "", "title": "渠道集成", "description": "channel-integration", "extend": "{}", "progress_status": "completed", "repo_id": "94f96537-e6cc-41d0-af2e-17841bee1253", "workspace_path": "", "id": "ecdc44cc-d44c-4667-b0c6-5c77e7d09ea8", "gmt_create": "2025-09-22T21:53:12.4053337+08:00", "gmt_modified": "2025-09-22T21:53:12.4315906+08:00"}, {"catalog_id": "b4004a4d-0175-4cf4-9807-952112404aa2", "content": "", "title": "转账API", "description": "transfer-api", "extend": "{}", "progress_status": "completed", "repo_id": "94f96537-e6cc-41d0-af2e-17841bee1253", "workspace_path": "", "id": "5f00cbe3-b405-49c3-94f0-362cf65a6dac", "gmt_create": "2025-09-22T21:54:36.1708097+08:00", "gmt_modified": "2025-09-22T21:54:36.1938993+08:00"}, {"catalog_id": "1788d30f-d310-4d22-afa6-eb221da78333", "content": "", "title": "统一下单", "description": "unified-order", "extend": "{}", "progress_status": "completed", "repo_id": "94f96537-e6cc-41d0-af2e-17841bee1253", "workspace_path": "", "id": "95997cd5-068e-4c1f-905c-5d7612eb1631", "gmt_create": "2025-09-22T21:56:55.393892+08:00", "gmt_modified": "2025-09-22T21:56:55.4171412+08:00"}, {"catalog_id": "b4d0b4e3-8e9c-4717-8d39-d38cbf15d623", "content": "", "title": "退款申请", "description": "refund-application", "extend": "{}", "progress_status": "completed", "repo_id": "94f96537-e6cc-41d0-af2e-17841bee1253", "workspace_path": "", "id": "8aa790ac-5f42-4b25-8ea8-a7e7092a6b19", "gmt_create": "2025-09-22T21:58:12.7074673+08:00", "gmt_modified": "2025-09-22T21:58:12.7308528+08:00"}, {"catalog_id": "4ef29ac6-84b7-4031-a953-aec5213f6db6", "content": "", "title": "转账下单", "description": "transfer-order-create", "extend": "{}", "progress_status": "completed", "repo_id": "94f96537-e6cc-41d0-af2e-17841bee1253", "workspace_path": "", "id": "28f8a8c2-d45a-41dd-894e-546f8c38ce36", "gmt_create": "2025-09-22T21:59:44.7262949+08:00", "gmt_modified": "2025-09-22T21:59:44.7525782+08:00"}, {"catalog_id": "44518afc-6469-4e7d-8802-8fced7e8d8f8", "content": "", "title": "分账执行机制", "description": "division-execution", "extend": "{}", "progress_status": "completed", "repo_id": "94f96537-e6cc-41d0-af2e-17841bee1253", "workspace_path": "", "id": "50409fde-ddab-4aca-b782-cecd0ce5c90a", "gmt_create": "2025-09-22T22:00:55.9653673+08:00", "gmt_modified": "2025-09-22T22:00:55.9911315+08:00"}, {"catalog_id": "cb06d92a-8459-49d1-9a11-0e87325df449", "content": "", "title": "分润算法", "description": "profit-algorithm", "extend": "{}", "progress_status": "completed", "repo_id": "94f96537-e6cc-41d0-af2e-17841bee1253", "workspace_path": "", "id": "daee9033-a65b-419c-9203-3e2ec38f2a70", "gmt_create": "2025-09-22T22:01:26.2965651+08:00", "gmt_modified": "2025-09-22T22:01:26.3194676+08:00"}, {"catalog_id": "87fe95c0-2775-48bd-9a55-0ad8ff4aef31", "content": "", "title": "商户登录认证缓存清理", "description": "clean-mch-login-auth-cache", "extend": "{}", "progress_status": "completed", "repo_id": "94f96537-e6cc-41d0-af2e-17841bee1253", "workspace_path": "", "id": "145a1e49-68cd-490f-8e43-44f986a4b888", "gmt_create": "2025-09-22T22:03:29.3633436+08:00", "gmt_modified": "2025-09-22T22:03:29.3916461+08:00"}, {"catalog_id": "360a1aa7-38c9-47ab-841a-1814cd3ae0a5", "content": "", "title": "商户信息管理", "description": "mch-info-management", "extend": "{}", "progress_status": "completed", "repo_id": "94f96537-e6cc-41d0-af2e-17841bee1253", "workspace_path": "", "id": "95804084-1ac5-453d-bd92-a8ab299c88e8", "gmt_create": "2025-09-22T22:04:04.1926922+08:00", "gmt_modified": "2025-09-22T22:04:04.2163157+08:00"}, {"catalog_id": "29596eb6-ac05-4e07-9b72-b9514d48e73f", "content": "", "title": "支付查询", "description": "query-payment", "extend": "{}", "progress_status": "completed", "repo_id": "94f96537-e6cc-41d0-af2e-17841bee1253", "workspace_path": "", "id": "ad1e8852-3497-42da-ba70-68ed7<PERSON>edef", "gmt_create": "2025-09-22T22:06:03.5293702+08:00", "gmt_modified": "2025-09-22T22:06:03.540133+08:00"}, {"catalog_id": "446fda45-5ad3-478a-aa60-ab45082c1837", "content": "", "title": "退款查询", "description": "refund-query", "extend": "{}", "progress_status": "completed", "repo_id": "94f96537-e6cc-41d0-af2e-17841bee1253", "workspace_path": "", "id": "fe533d12-d180-42b3-b767-db1c48d4386d", "gmt_create": "2025-09-22T22:06:05.7501525+08:00", "gmt_modified": "2025-09-22T22:06:05.7788392+08:00"}, {"catalog_id": "e6442f1c-e8bd-4d4f-aee9-0cc3134f99c1", "content": "", "title": "分账结果通知", "description": "division-notification", "extend": "{}", "progress_status": "completed", "repo_id": "94f96537-e6cc-41d0-af2e-17841bee1253", "workspace_path": "", "id": "07ca4c56-0dad-4888-a101-8efc8bc715e8", "gmt_create": "2025-09-22T22:08:11.9835358+08:00", "gmt_modified": "2025-09-22T22:08:12.0091284+08:00"}, {"catalog_id": "8e37f058-c119-4b64-84f8-9b7e5ff41245", "content": "", "title": "转账查询", "description": "transfer-order-query", "extend": "{}", "progress_status": "completed", "repo_id": "94f96537-e6cc-41d0-af2e-17841bee1253", "workspace_path": "", "id": "dd3ee873-3fed-4abe-a6c6-6782c201bde5", "gmt_create": "2025-09-22T22:08:19.2643242+08:00", "gmt_modified": "2025-09-22T22:08:19.2742333+08:00"}, {"catalog_id": "907701b8-a606-4a7e-98e3-a6edc3767588", "content": "", "title": "分润记录管理", "description": "profit-record-management", "extend": "{}", "progress_status": "completed", "repo_id": "94f96537-e6cc-41d0-af2e-17841bee1253", "workspace_path": "", "id": "202b110d-a3a4-463f-87cd-a670636953be", "gmt_create": "2025-09-22T22:10:10.4625204+08:00", "gmt_modified": "2025-09-22T22:10:10.4884563+08:00"}, {"catalog_id": "15f52f6a-6c1f-44ce-9ca5-711c10f5e6b4", "content": "", "title": "应用配置管理", "description": "mch-app-configuration", "extend": "{}", "progress_status": "completed", "repo_id": "94f96537-e6cc-41d0-af2e-17841bee1253", "workspace_path": "", "id": "1979465e-bc1c-4566-b5b3-3183a802cbc4", "gmt_create": "2025-09-22T22:10:58.6260982+08:00", "gmt_modified": "2025-09-22T22:10:58.6501046+08:00"}, {"catalog_id": "50fdea45-f53c-48f3-9caf-c2ed64516890", "content": "", "title": "应用配置重置", "description": "reset-app-config", "extend": "{}", "progress_status": "completed", "repo_id": "94f96537-e6cc-41d0-af2e-17841bee1253", "workspace_path": "", "id": "654a0096-939c-458a-a3e0-48849eb784ad", "gmt_create": "2025-09-22T22:11:50.6646657+08:00", "gmt_modified": "2025-09-22T22:11:50.6894288+08:00"}, {"catalog_id": "2afe1e51-356d-4a16-b6ce-d6de6a7c9bf4", "content": "", "title": "关闭订单", "description": "close-payment", "extend": "{}", "progress_status": "completed", "repo_id": "94f96537-e6cc-41d0-af2e-17841bee1253", "workspace_path": "", "id": "f8710b20-b1a0-405e-a229-27c1c6b7e80e", "gmt_create": "2025-09-22T22:12:27.0189339+08:00", "gmt_modified": "2025-09-22T22:12:27.042578+08:00"}, {"catalog_id": "ab272022-c9a3-4ba2-af1f-68ab02f8cee1", "content": "", "title": "转账回调处理", "description": "transfer-callback", "extend": "{}", "progress_status": "completed", "repo_id": "94f96537-e6cc-41d0-af2e-17841bee1253", "workspace_path": "", "id": "b2e7f8ec-28a4-4927-9c0a-bd7e6c3d37f7", "gmt_create": "2025-09-22T22:14:33.2303432+08:00", "gmt_modified": "2025-09-22T22:14:33.2564831+08:00"}, {"catalog_id": "fbad6118-013e-422f-ad22-a5e992fdba11", "content": "", "title": "退款回调处理", "description": "refund-callback", "extend": "{}", "progress_status": "completed", "repo_id": "94f96537-e6cc-41d0-af2e-17841bee1253", "workspace_path": "", "id": "60d280b5-8429-4844-b079-e24d18e1d3cb", "gmt_create": "2025-09-22T22:14:33.847449+08:00", "gmt_modified": "2025-09-22T22:14:33.8570915+08:00"}, {"catalog_id": "3ef1d678-997c-4368-8394-6e9ec7cb5369", "content": "", "title": "支付通知", "description": "payment-notification", "extend": "{}", "progress_status": "completed", "repo_id": "94f96537-e6cc-41d0-af2e-17841bee1253", "workspace_path": "", "id": "39de936f-0ef1-4837-a7a5-8d338d95755f", "gmt_create": "2025-09-22T22:16:58.5652525+08:00", "gmt_modified": "2025-09-22T22:16:58.589064+08:00"}, {"catalog_id": "45ac9675-3ee8-4b0f-9927-cc514e45b49d", "content": "", "title": "分账接收方管理", "description": "division-receiver-management", "extend": "{}", "progress_status": "completed", "repo_id": "94f96537-e6cc-41d0-af2e-17841bee1253", "workspace_path": "", "id": "96d72f62-077e-45e4-a33d-a0bb7b08c887", "gmt_create": "2025-09-22T22:17:48.5293464+08:00", "gmt_modified": "2025-09-22T22:17:48.5554699+08:00"}, {"catalog_id": "cdf28e0f-1f1d-4128-85d0-9f2b9cad2ebd", "content": "", "title": "转账失败重发", "description": "transfer-reissue", "extend": "{}", "progress_status": "completed", "repo_id": "94f96537-e6cc-41d0-af2e-17841bee1253", "workspace_path": "", "id": "30284771-dc6e-4651-a0e3-1b4525e170cf", "gmt_create": "2025-09-22T22:19:10.5057167+08:00", "gmt_modified": "2025-09-22T22:19:10.5331818+08:00"}], "wiki_overview": {"content": "<blog>\n# UniPay 统一支付平台项目分析\n\n## 1. 项目介绍\n\n### 1.1 项目目的\nUniPay 是一个统一支付平台，旨在为商户、代理商和运营方提供一套完整的支付解决方案。系统通过微服务架构，实现了支付、分账、通知、配置管理等核心功能的解耦与高可用性。\n\n### 1.2 核心目标\n- 提供统一的支付接口，支持多种支付渠道（微信、支付宝等）\n- 实现商户、代理商、运营平台的多角色管理\n- 通过消息队列实现异步解耦，提升系统稳定性\n- 提供灵活的配置管理和动态更新机制\n\n### 1.3 目标用户\n- **商户**：使用支付、分账、订单管理等功能\n- **代理商**：管理下级商户，获取分润\n- **运营人员**：管理系统配置、用户权限、监控数据\n- **开发者**：集成支付SDK，进行二次开发\n\n## 2. 技术架构\n\n### 2.1 组件分解\n系统采用微服务架构，主要由以下核心模块构成：\n\n```mermaid\ngraph TD\n    A[支付网关] --> B[商户系统]\n    A --> C[运营平台]\n    A --> D[代理商系统]\n    E[消息中间件] --> A\n    E --> B\n    E --> C\n    E --> D\n    F[数据库] --> A\n    F --> B\n    F --> C\n    F --> D\n```\n\n### 2.2 设计模式\n- **策略模式**：支付服务通过 `IPaymentService` 接口实现不同支付方式的动态路由\n- **抽象工厂模式**：消息中间件通过 `IMQSender` 和 `IMQReceiver` 抽象不同厂商的实现\n- **单例模式**：配置服务 `SysConfigService` 使用静态变量缓存应用配置\n\n### 2.3 系统关系\n各子系统通过 REST API 和消息队列进行通信，形成松耦合的分布式架构。\n\n```mermaid\nflowchart TD\n    A[前端UI] --> B[API网关]\n    B --> C[支付网关]\n    B --> D[商户系统]\n    B --> E[运营平台]\n    B --> F[代理商系统]\n    C --> G[(数据库)]\n    D --> G\n    E --> G\n    F --> G\n    C --> H[消息中间件]\n    D --> H\n    E --> H\n    F --> H\n```\n\n### 2.4 数据流\n支付核心流程的数据流如下：\n\n```mermaid\nflowchart TD\n    A[商户发起支付] --> B[支付网关]\n    B --> C{选择支付渠道}\n    C -->|微信| D[微信支付服务]\n    C -->|支付宝| E[支付宝支付服务]\n    D --> F[调用微信API]\n    E --> G[调用支付宝API]\n    F --> H[微信返回结果]\n    G --> H\n    H --> I[更新订单状态]\n    I --> J[发送MQ通知]\n    J --> K[商户系统]\n    J --> L[代理商系统]\n    J --> M[运营平台]\n```\n\n## 3. 关键实现\n\n### 3.1 主要入口点\n- **支付网关**: `sys-payment/src/main/java/com/unipay/pay/bootstrap/PaymentApplication.java`\n- **商户系统**: `sys-merchant/src/main/java/com/unipay/mch/bootstrap/MerchantApplication.java`\n- **运营平台**: `sys-manager/src/main/java/com/unipay/mgr/bootstrap/ManagerApplication.java`\n- **代理商系统**: `sys-agent/src/main/java/com/unipay/agent/bootstrap/AgentApplication.java`\n\n### 3.2 核心模块\n- **支付服务**: `sys-payment/src/main/java/com/unipay/pay/channel/`\n- **消息中间件**: `components/components-mq/src/main/java/com/unipay/components/mq/`\n- **配置管理**: `core/src/main/java/com/unipay/core/model/DBApplicationConfig.java`\n- **实体定义**: `core/src/main/java/com/unipay/core/entity/`\n\n### 3.3 配置方法\n系统通过 `application.yml` 文件进行配置，关键配置项包括：\n\n- **数据库**: `spring.datasource.url`\n- **Redis**: `spring.data.redis.host`\n- **消息队列**: `isys.mq.vender`\n- **OSS存储**: `isys.oss.service-type`\n\nSources:\n- [conf/devCommons/config/application.yml](c:\\Users\\<USER>\\Desktop\\uni-pay\\conf\\devCommons\\config\\application.yml)\n- [sys-payment/src/main/resources/application.yml](c:\\Users\\<USER>\\Desktop\\uni-pay\\sys-payment\\src\\main\\resources\\application.yml)\n\n### 3.4 外部依赖\n- **数据库**: MySQL\n- **缓存**: Redis\n- **消息队列**: ActiveMQ/RabbitMQ/RocketMQ\n- **文件存储**: 本地/阿里云OSS\n- **支付渠道**: 微信支付、支付宝\n\n### 3.5 集成点\n- **支付回调**: `/api/pay/notify/{ifCode}`\n- **商户通知**: `/api/anon/paytestNotify`\n- **转账通知**: `/api/anon/transferNotify`\n- **配置更新**: 通过MQ广播 `BROADCAST_RESET_APP_CONFIG`\n\n### 3.6 组件关系\n```mermaid\ngraph LR\n    A[支付网关] --> B[支付服务]\n    A --> C[消息发送器]\n    B --> D[微信支付]\n    B --> E[支付宝]\n    C --> F[ActiveMQ]\n    C --> G[RabbitMQ]\n    C --> H[RocketMQ]\n    I[商户系统] --> J[消息接收器]\n    J --> K[订单处理]\n    L[运营平台] --> M[配置管理]\n    M --> C\n```\n\n## 4. 关键特性\n\n### 4.1 功能概述\n- **统一支付**: 支持多种支付方式的统一下单\n- **分账功能**: 支持自动分账和手动分账\n- **多级代理**: 支持代理商层级管理\n- **动态配置**: 支持运行时配置更新\n- **异步通知**: 通过MQ实现跨系统通知\n\n### 4.2 实现亮点\n- **支付路由**: 通过 `PaywayUtil.getRealPaywayService()` 实现支付方式的动态选择\n- **消息广播**: 使用 `BROADCAST_RESET_APP_CONFIG` 实现配置的全局更新\n- **缓存管理**: `SysConfigService` 提供配置缓存，可通过 `cache-config` 开关控制\n\n### 4.3 特性架构\n```mermaid\nstateDiagram-v2\n    [*] --> 初始化\n    初始化 --> 配置加载: 读取application.yml\n    配置加载 --> 服务启动: 启动Spring Boot应用\n    服务启动 --> 等待请求: 监听HTTP端口\n    等待请求 --> 支付处理: 接收支付请求\n    支付处理 --> 渠道选择: 根据wayCode选择服务\n    渠道选择 --> 调用API: 调用第三方支付接口\n    调用API --> 结果处理: 处理支付结果\n    结果处理 --> 发送通知: 通过MQ发送通知\n    发送通知 --> 等待请求\n```\n</blog>", "gmt_create": "2025-09-22T20:33:45.3262065+08:00", "gmt_modified": "2025-09-22T20:33:45.3262065+08:00", "id": "154403ed-a283-493f-aad9-b3767716290e", "repo_id": "94f96537-e6cc-41d0-af2e-17841bee1253", "workspace_path": ""}, "wiki_readme": {"content": "No readme file", "gmt_create": "2025-09-22T20:31:09.7700059+08:00", "gmt_modified": "2025-09-22T20:31:09.7700059+08:00", "id": "94a3a01a-98c7-4394-b93f-7de335f575a5", "repo_id": "94f96537-e6cc-41d0-af2e-17841bee1253", "workspace_path": ""}, "wiki_repo": {"id": "94f96537-e6cc-41d0-af2e-17841bee1253", "workspace_path": "", "name": "uni-pay", "progress_status": "completed", "wiki_present_status": "COMPLETED", "optimized_catalog": "\".\\n├── code-gen\\n│   ├── src\\\\main\\\\java\\\\com\\\\gen\\n│   │   └── MainGen.java\\n│   └── pom.xml\\n├── components\\n│   ├── components-mq\\n│   │   ├── src\\\\main\\\\java\\\\com\\\\unipay\\\\components\\\\mq\\n│   │   │   ├── constant\\n│   │   │   │   ├── MQSendTypeEnum.java\\n│   │   │   │   └── MQVenderCS.java\\n│   │   │   ├── executor\\n│   │   │   │   └── MqThreadExecutor.java\\n│   │   │   ├── model\\n│   │   │   │   ├── AbstractMQ.java\\n│   │   │   │   ├── CleanMchLoginAuthCacheMQ.java\\n│   │   │   │   ├── PayOrderDivisionMQ.java\\n│   │   │   │   ├── PayOrderMchNotifyMQ.java\\n│   │   │   │   ├── PayOrderReissueMQ.java\\n│   │   │   │   ├── ResetAppConfigMQ.java\\n│   │   │   │   └── ResetIsvMchAppInfoConfigMQ.java\\n│   │   │   └── vender\\n│   │   │       ├── activemq\\n│   │   │       ├── aliyunrocketmq\\n│   │   │       ├── rabbitmq\\n│   │   │       ├── rocketmq\\n│   │   │       ├── IMQMsgReceiver.java\\n│   │   │       └── IMQSender.java\\n│   │   └── pom.xml\\n│   ├── components-oss\\n│   │   ├── src\\\\main\\\\java\\\\com\\\\unipay\\\\components\\\\oss\\n│   │   │   ├── config\\n│   │   │   │   ├── AliyunOssYmlConfig.java\\n│   │   │   │   └── OssYmlConfig.java\\n│   │   │   ├── constant\\n│   │   │   │   ├── OssSavePlaceEnum.java\\n│   │   │   │   └── OssServiceTypeEnum.java\\n│   │   │   ├── ctrl\\n│   │   │   │   └── OssFileController.java\\n│   │   │   ├── model\\n│   │   │   │   └── OssFileConfig.java\\n│   │   │   └── service\\n│   │   │       ├── AliyunOssService.java\\n│   │   │       ├── IOssService.java\\n│   │   │       └── LocalFileService.java\\n│   │   └── pom.xml\\n│   └── pom.xml\\n├── conf\\n│   ├── devCommons\\\\config\\n│   │   └── application.yml\\n│   └── readme.txt\\n├── core\\n│   ├── src\\\\main\\\\java\\\\com\\\\unipay\\\\core\\n│   │   ├── aop\\n│   │   │   └── MethodLog.java\\n│   │   ├── beans\\n│   │   │   └── RequestKitBean.java\\n│   │   ├── cache\\n│   │   │   ├── ITokenService.java\\n│   │   │   └── RedisUtil.java\\n│   │   ├── constants\\n│   │   │   ├── ApiCodeEnum.java\\n│   │   │   └── CS.java\\n│   │   ├── ctrls\\n│   │   │   └── AbstractCtrl.java\\n│   │   ├── entity\\n│   │   │   ├── AgentInfo.java\\n│   │   │   ├── AgentMchRelation.java\\n│   │   │   ├── AgentProfitRecord.java\\n│   │   │   ├── IsvInfo.java\\n│   │   │   ├── MchApp.java\\n│   │   │   ├── MchDivisionReceiver.java\\n│   │   │   ├── MchDivisionReceiverGroup.java\\n│   │   │   ├── MchInfo.java\\n│   │   │   ├── MchNotifyRecord.java\\n│   │   │   ├── MchPayPassage.java\\n│   │   │   ├── PayInterfaceConfig.java\\n│   │   │   ├── PayInterfaceDefine.java\\n│   │   │   ├── PayOrder.java\\n│   │   │   ├── PayOrderDivisionRecord.java\\n│   │   │   ├── PayWay.java\\n│   │   │   ├── RefundOrder.java\\n│   │   │   ├── SysConfig.java\\n│   │   │   ├── SysEntitlement.java\\n│   │   │   ├── SysLog.java\\n│   │   │   ├── SysRole.java\\n│   │   │   ├── SysRoleEntRela.java\\n│   │   │   ├── SysUser.java\\n│   │   │   ├── SysUserAuth.java\\n│   │   │   ├── SysUserRoleRela.java\\n│   │   │   └── TransferOrder.java\\n│   │   ├── exception\\n│   │   │   ├── BizException.java\\n│   │   │   ├── BizExceptionResolver.java\\n│   │   │   ├── JeepayAuthenticationException.java\\n│   │   │   └── ResponseException.java\\n│   │   ├── jwt\\n│   │   │   ├── JWTPayload.java\\n│   │   │   └── JWTUtils.java\\n│   │   ├── model\\n│   │   │   ├── params\\n│   │   │   │   ├── alipay\\n│   │   │   │   ├── plspay\\n│   │   │   │   ├── pppay\\n│   │   │   │   ├── wxpay\\n│   │   │   │   ├── xxpay\\n│   │   │   │   ├── ysf\\n│   │   │   │   ├── IsvParams.java\\n│   │   │   │   ├── IsvsubMchParams.java\\n│   │   │   │   └── NormalMchParams.java\\n│   │   │   ├── security\\n│   │   │   │   └── JeeUserDetails.java\\n│   │   │   ├── ApiPageRes.java\\n│   │   │   ├── ApiRes.java\\n│   │   │   ├── BaseModel.java\\n│   │   │   ├── DBApplicationConfig.java\\n│   │   │   ├── OriginalRes.java\\n│   │   │   └── QRCodeParams.java\\n│   │   ├── service\\n│   │   │   ├── ICodeSysTypeManager.java\\n│   │   │   ├── IMchQrcodeManager.java\\n│   │   │   └── ISysConfigService.java\\n│   │   └── utils\\n│   │       ├── AmountUtil.java\\n│   │       ├── ApiResBodyAdviceKit.java\\n│   │       ├── DateKit.java\\n│   │       ├── FileKit.java\\n│   │       ├── JeepayKit.java\\n│   │       ├── JsonKit.java\\n│   │       ├── RegKit.java\\n│   │       ├── SeqKit.java\\n│   │       ├── SpringBeansUtil.java\\n│   │       ├── StringKit.java\\n│   │       └── TreeDataBuilder.java\\n│   └── pom.xml\\n├── game-server\\n│   ├── src\\\\main\\n│   │   ├── java\\\\com\\\\game\\n│   │   │   ├── config\\n│   │   │   │   ├── GameConfig.java\\n│   │   │   │   └── PaymentConfig.java\\n│   │   │   ├── controller\\n│   │   │   │   ├── PaymentCallbackController.java\\n│   │   │   │   └── RechargeController.java\\n│   │   │   ├── entity\\n│   │   │   │   ├── GameUser.java\\n│   │   │   │   └── RechargeOrder.java\\n│   │   │   ├── repository\\n│   │   │   │   ├── GameUserRepository.java\\n│   │   │   │   └── RechargeOrderRepository.java\\n│   │   │   ├── service\\n│   │   │   │   ├── GameUserService.java\\n│   │   │   │   ├── PaymentService.java\\n│   │   │   │   └── RechargeService.java\\n│   │   │   ├── util\\n│   │   │   │   └── PaymentUtil.java\\n│   │   │   └── GameServerApplication.java\\n│   │   └── resources\\n│   │       ├── templates\\n│   │       │   ├── recharge.html\\n│   │       │   └── success.html\\n│   │       └── application.yml\\n│   ├── README.md\\n│   └── pom.xml\\n├── service\\n│   ├── src\\\\main\\n│   │   ├── java\\\\com\\\\unipay\\\\service\\n│   │   │   ├── impl\\n│   │   │   │   ├── AgentInfoService.java\\n│   │   │   │   ├── AgentMchRelationService.java\\n│   │   │   │   ├── AgentProfitRecordService.java\\n│   │   │   │   ├── IsvInfoService.java\\n│   │   │   │   ├── MchAppService.java\\n│   │   │   │   ├── MchDivisionReceiverGroupService.java\\n│   │   │   │   ├── MchDivisionReceiverService.java\\n│   │   │   │   ├── MchInfoService.java\\n│   │   │   │   ├── MchNotifyRecordService.java\\n│   │   │   │   ├── MchPayPassageService.java\\n│   │   │   │   ├── PayInterfaceConfigService.java\\n│   │   │   │   ├── PayInterfaceDefineService.java\\n│   │   │   │   ├── PayOrderDivisionRecordService.java\\n│   │   │   │   ├── PayOrderService.java\\n│   │   │   │   ├── PayWayService.java\\n│   │   │   │   ├── RefundOrderService.java\\n│   │   │   │   ├── SysConfigService.java\\n│   │   │   │   ├── SysEntitlementService.java\\n│   │   │   │   ├── SysLogService.java\\n│   │   │   │   ├── SysRoleEntRelaService.java\\n│   │   │   │   ├── SysRoleService.java\\n│   │   │   │   ├── SysUserAuthService.java\\n│   │   │   │   ├── SysUserRoleRelaService.java\\n│   │   │   │   ├── SysUserService.java\\n│   │   │   │   └── TransferOrderService.java\\n│   │   │   └── mapper\\n│   │   │       ├── AgentInfoMapper.java\\n│   │   │       ├── AgentMchRelationMapper.java\\n│   │   │       ├── AgentProfitRecordMapper.java\\n│   │   │       ├── IsvInfoMapper.java\\n│   │   │       ├── IsvInfoMapper.xml\\n│   │   │       ├── MchAppMapper.java\\n│   │   │       ├── MchAppMapper.xml\\n│   │   │       ├── MchDivisionReceiverGroupMapper.java\\n│   │   │       ├── MchDivisionReceiverGroupMapper.xml\\n│   │   │       ├── MchDivisionReceiverMapper.java\\n│   │   │       ├── MchDivisionReceiverMapper.xml\\n│   │   │       ├── MchInfoMapper.java\\n│   │   │       ├── MchInfoMapper.xml\\n│   │   │       ├── MchNotifyRecordMapper.java\\n│   │   │       ├── MchNotifyRecordMapper.xml\\n│   │   │       ├── MchPayPassageMapper.java\\n│   │   │       ├── MchPayPassageMapper.xml\\n│   │   │       ├── PayInterfaceConfigMapper.java\\n│   │   │       ├── PayInterfaceConfigMapper.xml\\n│   │   │       ├── PayInterfaceDefineMapper.java\\n│   │   │       ├── PayInterfaceDefineMapper.xml\\n│   │   │       ├── PayOrderDivisionRecordMapper.java\\n│   │   │       ├── PayOrderDivisionRecordMapper.xml\\n│   │   │       ├── PayOrderMapper.java\\n│   │   │       ├── PayOrderMapper.xml\\n│   │   │       ├── PayWayMapper.java\\n│   │   │       ├── PayWayMapper.xml\\n│   │   │       ├── RefundOrderMapper.java\\n│   │   │       ├── RefundOrderMapper.xml\\n│   │   │       ├── SysConfigMapper.java\\n│   │   │       ├── SysConfigMapper.xml\\n│   │   │       ├── SysEntitlementMapper.java\\n│   │   │       ├── SysEntitlementMapper.xml\\n│   │   │       ├── SysLogMapper.java\\n│   │   │       ├── SysLogMapper.xml\\n│   │   │       ├── SysRoleEntRelaMapper.java\\n│   │   │       ├── SysRoleEntRelaMapper.xml\\n│   │   │       ├── SysRoleMapper.java\\n│   │   │       ├── SysRoleMapper.xml\\n│   │   │       ├── SysUserAuthMapper.java\\n│   │   │       ├── SysUserAuthMapper.xml\\n│   │   │       ├── SysUserMapper.java\\n│   │   │       ├── SysUserMapper.xml\\n│   │   │       ├── SysUserRoleRelaMapper.java\\n│   │   │       ├── SysUserRoleRelaMapper.xml\\n│   │   │       ├── TransferOrderMapper.java\\n│   │   │       └── TransferOrderMapper.xml\\n│   │   └── resources\\\\mapper\\n│   │       ├── AgentInfoMapper.xml\\n│   │       ├── AgentMchRelationMapper.xml\\n│   │       └── AgentProfitRecordMapper.xml\\n│   └── pom.xml\\n├── sys-agent\\n│   ├── src\\\\main\\n│   │   ├── java\\\\com\\\\unipay\\\\agent\\n│   │   │   ├── aop\\n│   │   │   │   └── MethodLogAop.java\\n│   │   │   ├── bootstrap\\n│   │   │   │   ├── AgentApplication.java\\n│   │   │   │   ├── FastJsonHttpMessageConverterEx.java\\n│   │   │   │   ├── InitRunner.java\\n│   │   │   │   └── SwaggerJsonSerializer.java\\n│   │   │   ├── config\\n│   │   │   │   ├── RedisConfig.java\\n│   │   │   │   ├── SwaggerConfig.java\\n│   │   │   │   └── SystemYmlConfig.java\\n│   │   │   ├── ctrl\\n│   │   │   │   ├── agent\\n│   │   │   │   ├── anon\\n│   │   │   │   ├── common\\n│   │   │   │   ├── division\\n│   │   │   │   ├── merchant\\n│   │   │   │   ├── order\\n│   │   │   │   ├── payconfig\\n│   │   │   │   ├── paytest\\n│   │   │   │   ├── profit\\n│   │   │   │   ├── sysuser\\n│   │   │   │   ├── transfer\\n│   │   │   │   ├── CommonCtrl.java\\n│   │   │   │   ├── CurrentUserController.java\\n│   │   │   │   └── MainController.java\\n│   │   │   ├── mq\\n│   │   │   │   ├── CleanMchLoginAuthCacheMQReceiver.java\\n│   │   │   │   └── ResetAppConfigMQReceiver.java\\n│   │   │   ├── secruity\\n│   │   │   │   ├── JeeAuthenticationEntryPoint.java\\n│   │   │   │   ├── JeeAuthenticationTokenFilter.java\\n│   │   │   │   ├── JeeUserDetailsServiceImpl.java\\n│   │   │   │   └── WebSecurityConfig.java\\n│   │   │   ├── service\\n│   │   │   │   ├── AuthService.java\\n│   │   │   │   └── CodeSysTypeManager.java\\n│   │   │   ├── web\\n│   │   │   │   ├── ApiResBodyAdvice.java\\n│   │   │   │   ├── ApiResInterceptor.java\\n│   │   │   │   ├── ApplicationContextKit.java\\n│   │   │   │   ├── SpaController.java\\n│   │   │   │   └── WebmvcConfig.java\\n│   │   │   └── websocket\\n│   │   │       ├── config\\n│   │   │       └── server\\n│   │   └── resources\\n│   │       ├── templates\\\\channelUser\\n│   │       │   └── getChannelUserIdPage.ftl\\n│   │       ├── application.yml\\n│   │       └── logback-spring.xml\\n│   ├── package-lock.json\\n│   └── pom.xml\\n├── sys-manager\\n│   ├── src\\\\main\\n│   │   ├── java\\\\com\\\\unipay\\\\mgr\\n│   │   │   ├── aop\\n│   │   │   │   └── MethodLogAop.java\\n│   │   │   ├── bootstrap\\n│   │   │   │   ├── FastJsonHttpMessageConverterEx.java\\n│   │   │   │   ├── InitRunner.java\\n│   │   │   │   ├── ManagerApplication.java\\n│   │   │   │   └── SwaggerJsonSerializer.java\\n│   │   │   ├── config\\n│   │   │   │   ├── RedisConfig.java\\n│   │   │   │   ├── SwaggerConfig.java\\n│   │   │   │   └── SystemYmlConfig.java\\n│   │   │   ├── ctrl\\n│   │   │   │   ├── agent\\n│   │   │   │   ├── anon\\n│   │   │   │   ├── common\\n│   │   │   │   ├── config\\n│   │   │   │   ├── isv\\n│   │   │   │   ├── merchant\\n│   │   │   │   ├── order\\n│   │   │   │   ├── payconfig\\n│   │   │   │   ├── sysuser\\n│   │   │   │   ├── CommonCtrl.java\\n│   │   │   │   └── CurrentUserController.java\\n│   │   │   ├── mq\\n│   │   │   │   └── ResetAppConfigMQReceiver.java\\n│   │   │   ├── secruity\\n│   │   │   │   ├── JeeAuthenticationEntryPoint.java\\n│   │   │   │   ├── JeeAuthenticationTokenFilter.java\\n│   │   │   │   ├── JeeUserDetailsServiceImpl.java\\n│   │   │   │   ├── WebSecurityConfig.java\\n│   │   │   │   └── WebSecurityConfigSecure.java\\n│   │   │   ├── service\\n│   │   │   │   ├── AuthService.java\\n│   │   │   │   └── CodeSysTypeManager.java\\n│   │   │   └── web\\n│   │   │       ├── ApiResBodyAdvice.java\\n│   │   │       ├── ApiResInterceptor.java\\n│   │   │       ├── ApplicationContextKit.java\\n│   │   │       ├── SpaController.java\\n│   │   │       └── WebmvcConfig.java\\n│   │   └── resources\\n│   │       ├── application.yml\\n│   │       └── logback-spring.xml\\n│   └── pom.xml\\n├── sys-merchant\\n│   ├── src\\\\main\\n│   │   ├── java\\\\com\\\\unipay\\\\mch\\n│   │   │   ├── aop\\n│   │   │   │   └── MethodLogAop.java\\n│   │   │   ├── bootstrap\\n│   │   │   │   ├── FastJsonHttpMessageConverterEx.java\\n│   │   │   │   ├── InitRunner.java\\n│   │   │   │   ├── MerchantApplication.java\\n│   │   │   │   └── SwaggerJsonSerializer.java\\n│   │   │   ├── config\\n│   │   │   │   ├── RedisConfig.java\\n│   │   │   │   ├── SwaggerConfig.java\\n│   │   │   │   └── SystemYmlConfig.java\\n│   │   │   ├── ctrl\\n│   │   │   │   ├── anon\\n│   │   │   │   ├── common\\n│   │   │   │   ├── division\\n│   │   │   │   ├── merchant\\n│   │   │   │   ├── order\\n│   │   │   │   ├── payconfig\\n│   │   │   │   ├── paytest\\n│   │   │   │   ├── sysuser\\n│   │   │   │   ├── transfer\\n│   │   │   │   ├── CommonCtrl.java\\n│   │   │   │   └── CurrentUserController.java\\n│   │   │   ├── mq\\n│   │   │   │   ├── CleanMchLoginAuthCacheMQReceiver.java\\n│   │   │   │   └── ResetAppConfigMQReceiver.java\\n│   │   │   ├── secruity\\n│   │   │   │   ├── JeeAuthenticationEntryPoint.java\\n│   │   │   │   ├── JeeAuthenticationTokenFilter.java\\n│   │   │   │   ├── JeeUserDetailsServiceImpl.java\\n│   │   │   │   └── WebSecurityConfig.java\\n│   │   │   ├── service\\n│   │   │   │   ├── AuthService.java\\n│   │   │   │   └── CodeSysTypeManager.java\\n│   │   │   ├── web\\n│   │   │   │   ├── ApiResBodyAdvice.java\\n│   │   │   │   ├── ApiResInterceptor.java\\n│   │   │   │   ├── ApplicationContextKit.java\\n│   │   │   │   ├── SpaController.java\\n│   │   │   │   └── WebmvcConfig.java\\n│   │   │   └── websocket\\n│   │   │       ├── config\\n│   │   │       └── server\\n│   │   └── resources\\n│   │       ├── templates\\\\channelUser\\n│   │       │   └── getChannelUserIdPage.ftl\\n│   │       ├── application.yml\\n│   │       └── logback-spring.xml\\n│   └── pom.xml\\n├── sys-payment\\n│   ├── src\\\\main\\n│   │   ├── java\\\\com\\\\unipay\\\\pay\\n│   │   │   ├── bootstrap\\n│   │   │   │   ├── FastJsonHttpMessageConverterEx.java\\n│   │   │   │   ├── InitRunner.java\\n│   │   │   │   ├── PaymentApplication.java\\n│   │   │   │   └── SwaggerJsonSerializer.java\\n│   │   │   ├── channel\\n│   │   │   │   ├── alipay\\n│   │   │   │   ├── plspay\\n│   │   │   │   ├── pppay\\n│   │   │   │   ├── wxpay\\n│   │   │   │   ├── xxpay\\n│   │   │   │   ├── ysfpay\\n│   │   │   │   ├── AbstractChannelNoticeService.java\\n│   │   │   │   ├── AbstractChannelRefundNoticeService.java\\n│   │   │   │   ├── AbstractDivisionRecordChannelNotifyService.java\\n│   │   │   │   ├── AbstractPaymentService.java\\n│   │   │   │   ├── AbstractRefundService.java\\n│   │   │   │   ├── AbstractTransferNoticeService.java\\n│   │   │   │   ├── IChannelNoticeService.java\\n│   │   │   │   ├── IChannelRefundNoticeService.java\\n│   │   │   │   ├── IChannelUserService.java\\n│   │   │   │   ├── IDivisionService.java\\n│   │   │   │   ├── IPayOrderCloseService.java\\n│   │   │   │   ├── IPayOrderQueryService.java\\n│   │   │   │   ├── IPaymentService.java\\n│   │   │   │   ├── IRefundService.java\\n│   │   │   │   ├── ITransferNoticeService.java\\n│   │   │   │   └── ITransferService.java\\n│   │   │   ├── config\\n│   │   │   │   ├── RedisConfig.java\\n│   │   │   │   ├── SwaggerConfig.java\\n│   │   │   │   └── SystemYmlConfig.java\\n│   │   │   ├── ctrl\\n│   │   │   │   ├── division\\n│   │   │   │   ├── payorder\\n│   │   │   │   ├── qr\\n│   │   │   │   ├── refund\\n│   │   │   │   ├── scanimg\\n│   │   │   │   ├── transfer\\n│   │   │   │   ├── ApiController.java\\n│   │   │   │   └── CommonController.java\\n│   │   │   ├── exception\\n│   │   │   │   └── ChannelException.java\\n│   │   │   ├── model\\n│   │   │   │   ├── AlipayClientWrapper.java\\n│   │   │   │   ├── IsvConfigContext.java\\n│   │   │   │   ├── MchAppConfigContext.java\\n│   │   │   │   ├── MchInfoConfigContext.java\\n│   │   │   │   ├── PaypalWrapper.java\\n│   │   │   │   └── WxServiceWrapper.java\\n│   │   │   ├── mq\\n│   │   │   │   ├── PayOrderDivisionMQReceiver.java\\n│   │   │   │   ├── PayOrderMchNotifyMQReceiver.java\\n│   │   │   │   ├── PayOrderReissueMQReceiver.java\\n│   │   │   │   ├── ResetAppConfigMQReceiver.java\\n│   │   │   │   └── ResetIsvMchAppInfoMQReceiver.java\\n│   │   │   ├── rqrs\\n│   │   │   │   ├── division\\n│   │   │   │   ├── msg\\n│   │   │   │   ├── payorder\\n│   │   │   │   ├── refund\\n│   │   │   │   ├── transfer\\n│   │   │   │   ├── AbstractMchAppRQ.java\\n│   │   │   │   ├── AbstractRQ.java\\n│   │   │   │   ├── AbstractRS.java\\n│   │   │   │   └── ChannelUserIdRQ.java\\n│   │   │   ├── service\\n│   │   │   │   ├── ChannelOrderReissueService.java\\n│   │   │   │   ├── CodeSysTypeManager.java\\n│   │   │   │   ├── ConfigContextQueryService.java\\n│   │   │   │   ├── ConfigContextService.java\\n│   │   │   │   ├── PayMchNotifyService.java\\n│   │   │   │   ├── PayOrderDivisionProcessService.java\\n│   │   │   │   ├── PayOrderProcessService.java\\n│   │   │   │   ├── RefundOrderProcessService.java\\n│   │   │   │   ├── TransferOrderReissueService.java\\n│   │   │   │   └── ValidateService.java\\n│   │   │   ├── task\\n│   │   │   │   ├── PayOrderDivisionRecordReissueTask.java\\n│   │   │   │   ├── PayOrderExpiredTask.java\\n│   │   │   │   ├── PayOrderReissueTask.java\\n│   │   │   │   ├── RefundOrderExpiredTask.java\\n│   │   │   │   ├── RefundOrderReissueTask.java\\n│   │   │   │   └── TransferOrderReissueTask.java\\n│   │   │   └── util\\n│   │   │       ├── ApiResBuilder.java\\n│   │   │       ├── ChannelCertConfigKitBean.java\\n│   │   │       ├── CodeImgUtil.java\\n│   │   │       └── PaywayUtil.java\\n│   │   └── resources\\n│   │       ├── markdown\\\\doc\\n│   │       │   ├── api1.md\\n│   │       │   ├── api2.md\\n│   │       │   ├── api3.md\\n│   │       │   ├── api4.md\\n│   │       │   └── api5.md\\n│   │       ├── templates\\n│   │       │   ├── cashier\\n│   │       │   ├── channel\\n│   │       │   └── common\\n│   │       ├── application.yml\\n│   │       └── logback-spring.xml\\n│   └── pom.xml\\n├── unipay-portal\\n│   ├── 404.html\\n│   ├── 50x.html\\n│   ├── README.md\\n│   ├── index.html\\n│   ├── monitor.sh\\n│   ├── script.js\\n│   ├── styles.css\\n│   └── 项目总览.md\\n├── unipay-web-ui\\n│   ├── unipay-ui-agent\\n│   │   ├── public\\n│   │   │   └── index.html\\n│   │   ├── src\\n│   │   │   ├── api\\n│   │   │   │   ├── login.js\\n│   │   │   │   └── manage.js\\n│   │   │   ├── components\\n│   │   │   │   ├── ChannelUser\\n│   │   │   │   ├── GlobalFooter\\n│   │   │   │   ├── GlobalHeader\\n│   │   │   │   ├── GlobalLoad\\n│   │   │   │   ├── JeepayCard\\n│   │   │   │   ├── JeepayLayout\\n│   │   │   │   ├── JeepayTable\\n│   │   │   │   ├── JeepayTextUp\\n│   │   │   │   └── JeepayUpload\\n│   │   │   ├── config\\n│   │   │   │   └── appConfig.js\\n│   │   │   ├── core\\n│   │   │   │   ├── bootstrap.js\\n│   │   │   │   ├── lazy_use.js\\n│   │   │   │   └── use.js\\n│   │   │   ├── http\\n│   │   │   │   ├── HttpRequest.js\\n│   │   │   │   └── request.js\\n│   │   │   ├── layouts\\n│   │   │   │   ├── BasicLayout.vue\\n│   │   │   │   ├── BlankLayout.vue\\n│   │   │   │   ├── PageView.vue\\n│   │   │   │   ├── RouteView.vue\\n│   │   │   │   ├── UserLayout.vue\\n│   │   │   │   └── index.js\\n│   │   │   ├── router\\n│   │   │   │   ├── generator-routers.js\\n│   │   │   │   └── index.js\\n│   │   │   ├── store\\\\modules\\n│   │   │   │   └── user.ts\\n│   │   │   ├── utils\\n│   │   │   │   ├── domUtil.js\\n│   │   │   │   ├── filter.js\\n│   │   │   │   ├── infoBox.js\\n│   │   │   │   ├── jeepayStorageWrapper.js\\n│   │   │   │   ├── ruleGenerator.js\\n│   │   │   │   ├── screenLog.js\\n│   │   │   │   ├── throttle.js\\n│   │   │   │   └── util.js\\n│   │   │   ├── views\\n│   │   │   │   ├── agent\\n│   │   │   │   ├── current\\n│   │   │   │   ├── dashboard\\n│   │   │   │   ├── exception\\n│   │   │   │   ├── mch\\n│   │   │   │   ├── mchApp\\n│   │   │   │   ├── order\\n│   │   │   │   ├── payconfig\\\\payWay\\n│   │   │   │   ├── profit\\n│   │   │   │   ├── role\\n│   │   │   │   ├── sysuser\\n│   │   │   │   ├── user\\n│   │   │   │   └── ... 0 files, 1 dirs not shown\\n│   │   │   ├── App.vue\\n│   │   │   ├── icons.ts\\n│   │   │   ├── main.ts\\n│   │   │   └── router.ts\\n│   │   ├── README.md\\n│   │   ├── components.d.ts\\n│   │   ├── fix-permissions.sh\\n│   │   ├── index.html\\n│   │   ├── package-lock.json\\n│   │   ├── package.json\\n│   │   ├── tsconfig.json\\n│   │   └── vite.config.ts\\n│   ├── unipay-ui-cashier\\n│   │   ├── public\\n│   │   │   └── index.html\\n│   │   ├── src\\n│   │   │   ├── api\\n│   │   │   │   └── api.js\\n│   │   │   ├── config\\n│   │   │   │   ├── index.js\\n│   │   │   │   └── rem.js\\n│   │   │   ├── http\\n│   │   │   │   ├── HttpRequest.js\\n│   │   │   │   └── request.js\\n│   │   │   ├── router\\n│   │   │   │   └── index.js\\n│   │   │   ├── utils\\n│   │   │   │   ├── channelUserId.js\\n│   │   │   │   └── wayCode.js\\n│   │   │   ├── views\\n│   │   │   │   ├── dialog\\n│   │   │   │   ├── keyboard\\n│   │   │   │   ├── payway\\n│   │   │   │   ├── Cashier.vue\\n│   │   │   │   ├── Error.vue\\n│   │   │   │   ├── Hub.vue\\n│   │   │   │   └── Oauth2Callback.vue\\n│   │   │   ├── App.vue\\n│   │   │   └── main.js\\n│   │   ├── README.md\\n│   │   ├── babel.config.js\\n│   │   ├── package.json\\n│   │   └── vue.config.js\\n│   ├── unipay-ui-manager\\n│   │   ├── src\\n│   │   │   ├── api\\n│   │   │   │   ├── login.js\\n│   │   │   │   └── manage.js\\n│   │   │   ├── components\\n│   │   │   │   ├── GlobalFooter\\n│   │   │   │   ├── GlobalHeader\\n│   │   │   │   ├── GlobalLoad\\n│   │   │   │   ├── JeepayCard\\n│   │   │   │   ├── JeepayLayout\\n│   │   │   │   ├── JeepayTable\\n│   │   │   │   ├── JeepayTextUp\\n│   │   │   │   └── JeepayUpload\\n│   │   │   ├── config\\n│   │   │   │   └── appConfig.js\\n│   │   │   ├── core\\n│   │   │   │   ├── bootstrap.js\\n│   │   │   │   ├── lazy_use.js\\n│   │   │   │   └── use.js\\n│   │   │   ├── http\\n│   │   │   │   ├── HttpRequest.js\\n│   │   │   │   └── request.js\\n│   │   │   ├── layouts\\n│   │   │   │   └── ... 1 files, 0 dirs not shown\\n│   │   │   ├── router\\n│   │   │   │   └── ... 2 files, 0 dirs not shown\\n│   │   │   ├── store\\\\modules\\n│   │   │   │   └── ... 1 files, 0 dirs not shown\\n│   │   │   ├── utils\\n│   │   │   │   └── ... 7 files, 0 dirs not shown\\n│   │   │   ├── views\\n│   │   │   │   └── ... 0 files, 14 dirs not shown\\n│   │   │   ├── App.vue\\n│   │   │   ├── global.css\\n│   │   │   ├── icons.ts\\n│   │   │   ├── main.ts\\n│   │   │   └── router.ts\\n│   │   ├── components.d.ts\\n│   │   ├── index.html\\n│   │   ├── package-lock.json\\n│   │   ├── package.json\\n│   │   ├── tsconfig.json\\n│   │   └── vite.config.ts\\n│   ├── unipay-ui-merchant\\n│   │   ├── public\\n│   │   │   └── index.html\\n│   │   ├── src\\n│   │   │   ├── api\\n│   │   │   │   └── ... 2 files, 0 dirs not shown\\n│   │   │   ├── components\\n│   │   │   │   └── ... 0 files, 9 dirs not shown\\n│   │   │   ├── config\\n│   │   │   │   └── ... 1 files, 0 dirs not shown\\n│   │   │   ├── core\\n│   │   │   │   └── ... 3 files, 0 dirs not shown\\n│   │   │   ├── http\\n│   │   │   │   └── ... 2 files, 0 dirs not shown\\n│   │   │   ├── layouts\\n│   │   │   │   └── ... 6 files, 0 dirs not shown\\n│   │   │   ├── router\\n│   │   │   │   └── ... 2 files, 0 dirs not shown\\n│   │   │   ├── store\\\\modules\\n│   │   │   │   └── ... 1 files, 0 dirs not shown\\n│   │   │   ├── utils\\n│   │   │   │   └── ... 8 files, 0 dirs not shown\\n│   │   │   ├── views\\n│   │   │   │   └── ... 0 files, 12 dirs not shown\\n│   │   │   ├── App.vue\\n│   │   │   ├── icons.ts\\n│   │   │   ├── main.ts\\n│   │   │   └── router.ts\\n│   │   ├── components.d.ts\\n│   │   ├── index.html\\n│   │   ├── package-lock.json\\n│   │   ├── package.json\\n│   │   ├── tsconfig.json\\n│   │   └── vite.config.ts\\n│   ├── Dockerfile\\n│   ├── README.md\\n│   └── package-lock.json\\n├── z-docs\\n│   ├── deploy\\n│   │   ├── DEPLOYMENT.md\\n│   │   ├── README-nginx.md\\n│   │   ├── bt-deploy.sh\\n│   │   ├── bt-nginx.conf\\n│   │   ├── deploy-nginx.sh\\n│   │   ├── deploy.sh\\n│   │   ├── nginx-unipay-single.conf\\n│   │   ├── nginx-unipay.conf\\n│   │   ├── nginx.conf\\n│   │   ├── ssl-setup.sh\\n│   │   ├── 宝塔快速配置.txt\\n│   │   └── 宝塔面板部署指南.md\\n│   ├── md_records\\n│   │   ├── 代理商配置添加说明.md\\n│   │   └── 系统配置说明.md\\n│   ├── script\\n│   │   ├── app.sh\\n│   │   └── init_database.sh\\n│   └── sql\\n│       ├── tools\\n│       │   ├── analyze_duplicate_data.sql\\n│       │   ├── clean_duplicate_data.sql\\n│       │   ├── clean_exact_duplicates.sql\\n│       │   └── debug_agent_permissions.sql\\n│       ├── agent_permissions.sql\\n│       ├── init.sql\\n│       └── patch.sql\\n├── 代码示例\\n│   ├── 1-支付请求实现.java\\n│   ├── 2-订单创建实现.java\\n│   ├── 3-支付回调处理.java\\n│   ├── 4-签名验证工具类.java\\n│   ├── 5-配置文件和实体类.java\\n│   ├── 快速开始指南.md\\n│   └── 支付流程实现教程.md\\n├── README.md\\n├── build_ui.sh\\n├── connection-fix-guide.md\\n├── pom.xml\\n├── push_jar_to_cloud.sh\\n└── start_tar.sh\\n\"", "current_document_structure": "WikiEncrypted: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", "catalogue_think_content": "WikiEncrypted:sk7rVrOm0cPExtlhTWA9t2PvqyKOiJpq4Llj5BeizdIMQN4TAH1WByLCNiUmxpA4GD3bQIuRckPCVj0L9VnYEtMcaYk9ib/V1bfEAiCerY2SEU/legxxH5XZLq7F2V70/fDC6teP3f5vL1riS52wSfhLgJ2d+t9qFWqAx/MVRkwKWs+EENgj2rEElLX3SUolVq27in1H5yu+SgTB0HcK3v6LrmLdAqyaHAjNrmYDpB60G8boe9PpXH7r26mt7jxUii061EnT6NWGMvJ54NGYY/axpwGOYlOI6rqwQQhVRgPjFiIAlR541hYmsyPtdvbjjzH48vSVOFF2/h4xe7mCmrUgAlKpMEdGi3IGogJral5kwRBh1UdVnzYk8Ie3c0dIsHOxC+IXx1oV9cuAt+BmJvgl1mfZQtuf27kPlE1qWi8FT1Zw34ZjSTjrZ2ddSBz/JOOxsbS9oVbnmZnHI1okx6Z+dKlXiOoeNVZq9yyJSYEEWbJBwbFkOs+OiWiT+O+aoLdNGy8hC2VRiJ3mtW99WvIYNh/bgcQavz7AH3s0vRz2S4k6DwBd8TnnXhA7V2WOOaR4zVJcEQc+EtBKwH8z9a/NjJtbiZZyiMMVw90eLC/W0t/yxNdr2iHmZGOiYwUWG0HB8vqw84jKM009JhCWElSiGBcBlznIkEaQ/6702ILb81mlsm+tbbjy5fd2mv43TOR+eEiGNlQ0imbxVvKCi7lTlqEBXlhJ2N2wrpovzIyMGzJWIRN1SI5XYzz6v8+H9CzGEYhjJxp2yPzdQyofIwqyyUrwzPwKo/ZanuAAShrtJiUj9/S0RYSYZg7/c3auyfQGiKikkki11klGpCC60aGbPDyDkGlqheyu7gXqXZaFMiZ9F70z34AxuYAwYGIBaTcGmqS0PHqQR3ngSlGTkwZOa2InRI7CSnMgsisucodFgYjnF3ll0IgbI5SYFpO0SUAl4UbrtBxTrOT20NtDSaA1STKFrSjCJsJ4sTDt01YJLDCHlJqQ00W9moMFTWn1VsN+u6aKBmmksSphxkcTikLo2Qgy1Cqg3/4BrmAbai9lJkankueg2Rvgm2CD9PQdURrxEYIZug/+qI3XIVt0tsVkhT+yBTxj250TqefuD7n9j1akvEgMYbQw1sEFZEuan6ApBfUBp7PCPqjURSaknOXjwzNUe3JW3R4qCKR6Cz0ov0blpdsHt0DkazwqPfCenr8qJh8Gy0gVCjkCpLrP0AfZqELyHd8Uro7jq2VEnzqa518cmgo+UrYz1leCc1rW6F33NxaBAVOUR1Fsmjp9TfDmlm6vCHQi1hqv1c3neuezrDR6lQpaLGqVXc56HpBl8QymQfOIRVXdlIwycyYSBOJa7UtpYccTbs9viJzPZmZ3v97LlNRY9OkM0qZjkisfBwQtLG+Yb1qxjDuxA8xZZ3P/C19we+K/o278T9YlwqSs4yQASW4LxuU86Xy4s+/D+cHLw28ocYuU1L7ldwbzYzcK4BdHu/mUxGFiL2Vtrn0e1AFhntevFKO8vGvHcF/CQvWV+qqbdh4qUZXSrJ5hofCnvRtzGIU2N0+MjI+cUeq8ooW/KLCUMinVmFfvIqFRVOfO7wu7AA3EEi7YDWv3VevJfBsGREdOZNb2gXGh8UQqU4er5oXfgeTl8zIJLGGOIJmSUCus33ZdeRgog16J9CqnHY5oLyqpZ8bEHcLpvlW7iQF+ZkafyWHx4+xzc8sHV7aaUsmwuQYV+wL2H4JRU0zRsXk2YEAF1PtL+lCeZVhJsr8WgYGnSdb8nzlXsOcmyb7dDErrX0raNMUDZ2E4TKFo7he69NK/gBy0ZGAL5So8SqYOJZ2ImW00zyO0lhxRy3WSlCNSk3lsB1BJmZv/RS9Pf2CSko+yZo3OqrBxPSyvQVJsdix4tV4HQFIsRRggUOfey42vPivFDcdO+iO6MgOKyuAJemSy7jbvschzwg6aP1DrBGuITGPaBKHRdQ2Hs4tF5iy/PCIxeD8g0/g3clSK+xwziG14DsRamWAvz8KoxqiaicCh44zuQa6tPOkp7yzxTlIOyYlzeCVZ6lHDzZaWFcoEdmqVYxZWNRlXTtq7HaL2LS9RXE0uzOWevpHOKiJmHVGbIQpa/SJiFpxdHYyAgA/J9AIiVhKYWyRnH07IURPoxDNbBCOgHYLZUqgBZJF6xA0cvk24pGKtvMWAFoNy97cT2jJrXG/BG/YyHqqPbOHndw9grDE5W7i23dMH9oAsDVc0npA+/vqwhul9ovwhYwAYhG+OURIq8JsPlTFbi4KjoLsmseL3Cc+nkYOXqKOXp8T9F2osE+kMWMzgPrhrR2BCVvlDd7ioUP+uN77tmHMr1gXlHL7/At/z7su+P7viBwaNXV7qETrNCP5bI0Ea9MC2HPltQY0wWmCAkO2jYaghla4r1oNWxCtKL+0/6vqs2UOWwgp8/jEo9NjLr1on2PHh9z7m6bDl4T1YzXydnJR1YA5FmhWlvR6+HID/G3Rg+vaHnce6UBOyfiPldnQ4tCJSeisAq8g5BGckE1/z6wSy1lvAuLDXBN6ArFF8rk5GyaaDSzFYoAVR7oJwJxWJXzTf2JpqdoGic/Xv2l53ckPTgAsV+WnVxGVrlB0gKKRfQVN4f4Z+00IG8+bxBH7Kykr7mK4c83f2MA4vbYSV0+0Th9Yvcb58R0I9mykKBvvJ5Rrgwxno/bz7VyQQbz3KO0NYTYtTp/CcOBrJeySe89PugWRbvT2eAqBYWbK5f/h5yiJvgMgIufdn7Bam7l3R+A2wbyOlv2HPp4hA8UHTp6gByJx5JOStMAoWMlPVdy5IFGU//EBZNPKmYdY1cXqM0UOmAtEBB5vdn2oZpNuy6EDVZLvQhtxYyZIZFTw4kmG1D82/Pjw5h/fWa97bVJQraf+yTNhlrPl+hefZIfTzVO8GTN+0HVPTarc7tgmVm7kHBNu0nvHiNnteAAV3jaZe4DLWp/HV1isI9qUkjOe3458ieVUqkqeWOpLsirGpRX8hWbtF0wg4InRyw5Ihb6NN8lQmdg1kx0GJ+iNUfk0fg308J+fGV/XULhACZ61L8YFKdwNhdLez7t5X1F/mcwn9aWz+epfCXKYT+ZiugEVHgr5YVMnXYO+VmFXBpxErod9S0ygqbq7zQVUvdqOAGzOxsE4HlLxqxiPDY5ZQ3BvO4pqr3cGwrGziFZe/4aoZfAJRFk8Gb5FybIWBjpI7I5GE3iRBykdI3VOL+olt4464Xh9XGuAwUFhP4fDW+8vvo44wHwPIOev91XkOc96jfeWVMBHFjYnez2sciXKoBjgNaJokv08GhcGIoMjhaKcLkfRGCLcKJExi5nKUD1ojZOIk+isEGrLDNpQw2iD+bPnSrjHyDntNr/tIba6VuxW3S1prOjuSmkemazB6xwcTLKXnWm9+fbSk2FgD3D4vQi/68fLAC+GckqBIh6Z1pJ33/3cqmREkkVCKobi6itl/ewsw/pBY1SrvX4GTS2Y77T4wLxicykEAHatAEhoxGOffxDT8hYEKBMRFTQCkcmHpFpWEDZLBMOrt+bGOZCO80ipOxXXcjMeBzXFKTjglbUtYIKlf9rMoZCIizhBgtVxPP3stKIdABPQ3NJ/AvafcvqZkUvikpl1BlSnOejL6GPNpS3I/RJ7BMqqyZqCUxyYlsvIP8icRU9IauggbPepR44Fbv7IIYG9X2rBa8qYxsUn7QJWv3u3UWtcsi+bSUE3F/4OW6iVKfdP/hBZTYNnPhS1CiTlQi4f7d5dkyD8EZF7JbilDqvTUzOwlx1qOtTMGIXVo7lpxFqtzFixhtFPqnrGeBQl8lKk9trsXPmfFzPE4lj6md+iwg2B7uURT9T8jUa+qzPZkVy/u3ssb5fF6z9YVTKpHp1GDVMjn5cQL9jQ1f4jL6V20ReAMqoy8YKch9+BO8Q3MOd1r46t2+X+sJ3vEZ8gCV/FcWt+bIFnDY76fwpiruqBqnRyJLcqpJoDtjVid/SnLPzV1+LNacX4PoWQ9uFq0N6BY5m5HJyZ03LhvQtygDcnuLU/lR78Jj6fwAVcAQ9gRb7nWBhA7Lj6cv+3JzM9YETauCRx1qB1fShFXLu6J20YevthYE9NUL+lz+6k7UjA53Rj6hqsdx8nRLlGzm8B4jV4MynUjwampbsbdLbfzt6A5+5mjteHmlg26m75F6r7tbXI6xPNc2gfztvBoZjVfRkpaq3VTygzKxy4RhOe5L4g4M6pAEptNuvmjqi7n+bVrDQtbVsTuiXPNmGmKoVjnfkX93JHfGtHGYYxe1iZ4C2sNnoCnBmTMLWS/G+87Xpk9kVjpYaXOwCZymYW8/0pSJ/p+o0TxjcHI2TbtBmxiX3Ax4lipzaYn3mIG2FMFOThKPBzOZXxDeMeb3yqUUvl/Jtm1Of+mZMw/d65QHgDWPU2yDb4Dm1OPFeJURN21nJ2djHR0Tcee6eGCg9U3NswDayZT6SacbYQZSSjyFndNb3sW1f1VL5LiyKPgy+bFj+1ktegNSln+g8vgPj4Zdaru0YrBrUw2BC6ASWT0I0DsyjLELax7pu0aGE775jqqhHjBUpgR8g/K9i8kTCNYNqQOAj5P4Nd6Ft9E88gKvsVJSlQ+pqPGdE4DhYtzyVtkQtkneJ2Ci1ntiWdy8dYfzDLkeTJYg+ZHMpIMMALFnKk8sy71/znMfgSgbBxHi2kFZeWLMEgdtx/JI/OqaZlVs0elA+MYrcpC+Ervdv3eU+EdVk+xDxulvsV4LnUg7Cfq0x/95r1diQXmYzuwlzWC4tuuGqMdjpLv17V1Ek6/16ViP1y1N3UY98QRX4qnvdIaO/YaiExBNN1ZI90T3rTF8CU9oexCfnwfZcmkieZwrY+tpr4OhZUGJfJ4GlYGchR3U4dQ1QZWE/UO8Th07KJsRPyNXwV78A00crMqSxcFyCzmwC0JM8Nf4S6ILQRt9gZnGThYBJPsKMNHcU11fk23il0OqSHAWfh0wH20S04iXNpHA3ge9D5Gubs3sn+g6W8Ra1jn+qYChDtD7mnoopfZahkQ9EinnzAR9FfxtZWRedWVC8DKMQql/udhR0QonqbfwkDAealr1kMY8bR84tbxrjbb/kfZVbP1jBaJth+zu5oLoZydQSCqXZu2Be281EytqUAJOj7L6QmvzQdFfyyG3NneKK0kg7kQzo6Q5fi9jKty5wSkzO70Ed6JhI8I71Os6i4S46wkIVi/opnhmdxxBXMvkk3s1A1X6MVEqkJq9v2hoOtd3kLOT52SeL4MVEPxo/VuHJvHVV0R1lQ5kMbcraqCPxGwwN+5Mt+Ew1rq799eKod//QjvS/ajdxpFZcE2+1MLjtjjd/a+4oOAykRMRvzR8aKaqpJNlUHrP34F8JlY25VgGtloqzHB5VhlNkqTQ6B6fihhxSrAn1Rer8QW1gjmh7UMyh/gi1RxZ0A9nbaeC12q/bMVlEF1smKe40jM0vu3fXzI4PeIQp+bM+Pir10cLr1WaKQS7Vu4fna8l/FdaC33Tib/LjmAk1emGIyQMMbFYbxzyNU/2xaA69QtKAd35mlutXwxaH4hkOSj6gPxELy1vRl4fmqa4j5qjX55D8oGq1t23hXNj2uLCGm6vVd6r++JYk/6dbAdbofAa7Si5FLv3Rafovgaryr5AJN5rdGY+cqgE77KX3OHRaHcY4wOJZva95y8HODfO36lDxKEUpjmr/8/9hz37ZcS4eJop5j8jYQBC73VMUR9YeOEzX9w8cWkBcjV35/4cdhHnmZf9k26kF6MGcsL2foVz2SM4eRkwaBd61hrwihjv2RtnIeDhRqDpdGuoWKu1JO+PYlZLuuyPqygwLrlDGUedl4S1hsOKAOaOudWlUJ+yj8yiY4EGX4eklQO3iox2GL2MwQfmzbgF2wA+ay5BpA0FyHgiUFNOG4ORDiRNC4bKPn+5yQUTlRLZmTN8f8fT+aCaePsrCa1o3vwqF0uyuzgCZ/UO+VLXv9iluetRoBRyuI+vpxSNQvRZO9/C28aMbJ1LaPxSATylTfB3gNTbsdoWalGakuSZDPwAnbrvFkm4F2KQ9l3T6vS6/fRA3vR/8RMRP+h6t8U8Cng709/+6UtCa/cgOuaRHJEqL75SN7a6tC4nGJ7iUPBA8WoMvlX6zWNnle7O4yX85nqaDCn1/ca7ax0qLGPUVMFSkzRfugItWuiU0Y6FZVY/AqGqKuD6Q+BOH+Fyokyljmeo/s0tTP5W1r6uf7R0gJ42WYPwuBK4zSvzOZ99Vczs1XMY+AvyyZjq4wkZjuN1qD6tDeKKu2YaWP5KEcsjjCR/412WrYJDzG817BwjeC2vMuvnGuc9HfdBLC/PcpEBWFopVwi2kda+iP/KvD237+zLRBmS3jjNGRrHG7L0z2wo9xXpwHDjAKBJlM3pyVDgYjQZQRhy2Ouf2VvZsE9jTDdW6Xi/Cri2n1hM0pI9eYsAoEOU1iep8F1b6ueLkNVRhrQEibOTOdVw4OVtQAPxFzfKdCNwo8qEiFEajPM0TD3TOTPC0kLeWHPjST/c6PAsMGYpKsECmZmh/R9ueiZnNFdSXMYQy5oengJsrV6nDG1nhzgbSPr8555JUteJAjgqrsn6bp4qZluTbxecsGCMeCrWtKDqdBjSeSlAZdvtxkw8oVdsTppJuUX5L5LlKoYvaEZw/J2tcbzTTnAmqdKFkujzZe84pav3GIDMArNRTaKU4PiDJOxXS7tRLdMKXTj3CeR6OeODzAnEBS9U0vNd8dfrgrD8OzN0POf+1dUt/fcSKtz9ixQdJHss7g8g66nT1cTWEtUsErw3oA0rOT67FrzY/e1bVxbCBsw4IrcGJ/FoHVuIZt4mBGC2NzC3uMRJpFkZsV3rKr0puvSidbI5V51H9Nm9vtoOBUDUI69q85aaOoPUshy+3KEsV8XlWhZ/IA03wq77XvSjkvqw3M5EUdo9qDvccwiS/C8scgbU8QX/J4yt4kKhG3/ZwdLcLIzMBaP+Hu1nUQzsKQdzARWB9YZiBjocBWaGHfreimQ30nQvVgzGUzRp4d9Nsrh7dMq0e9nL5knzrgRd73Pi7SkBh/SFyYzeUZ5ar6kmyhikuvBnq4Z7B+t2wl7rWAJqTcpv8TA/X+kKt8tU653b0ScrrKd2+Ytps0Th3Wix7RqgHpmBrcLiFvUoyO7xt4JXf014qnAbH4/Wg1t1LigfOGFK/0Qj8y/0ypM+3doaWKlD/mXmLCZns24oVbT0ldx3g2cYSuMrTilbDI4OD7CbdDJOfAQ5kRUjY6MtcbddnHthf7XuAdpCN2t836ift4Qhovk7g/0v9VluPFZX+sNxRT80xKyMR56BgoRh6lQNhr+CuySXYy2dBiKPXzoHdj47cl5tL1f43t4IwHvwVwifPrTAPKX+fnOzyVQdzapTxa8nSnTHMtIJpfOS9ewUifeQjpWDfnQuwI60Fx7UXkfQyxdvnsB4KtI+k6sg6PFJmFqKflhlrgKpdMUpEluUKI9hROLlcSo/dlhv//hzqynGVMEQOrTxhtmtzldf99jAJ54gIhHM/7VLaeE3MIxmmf4bZ3FmN06REM1i+i/IobvDjEVapyB2D5cvb8aBWSpsjN00FDS/WCNTdRtrqNTJvnD8dstQKl1JmnqfmW6sY31pf83ulpSmy/VzT9gmZFAqOKtMfgLrL6i3J+EtDGlQuNmrRjstG4tW2cEDgaM5PuxJyGOEEuvNqaH3tCdh3RT9s2T8cmTBuSZD+JTPuGrIihn03+j71qE2BR1eQ7aA1DFJlxVjd+O+JqyeNajxbup+j/k+Eij97SSUxddYn7v/X01L+NcDF/PBfzryJzyu0WKH0tiiiVJW8I0tVq6fRnw9jJXySYq4KgQ6FRG/vmutQtPXw6UvKtglf5kal63foB5NF7UKFBu5CDMs3YJ/MiVTYF8Aiz2oG1aZUq08HJJAfkvk31xXHTKxYX/Jh1U6y/+TXhR3r/oWd9aTtB1h8lgI33aaODewC5psU3tfhkwctsyN/A5NBl9tLU5gKNqZ8IeNjYtakD51P/p6cW4a0xZdeyQQd7BZ7/eNnXFkhyEzn62P1ELXYs3uyMUSXDzMBc5/Z/dDw1fnuvkABPzCIK+6WkCDu5mnlodgise4hub+AVlTjGIHA3IxOtyDbldiu42irRqpQWVt4HuDc4BsokB7lDwqE/OKdiTGplPUhQb8kb1DlvoNYIETlJ9FO6iqftj2JiW7CtvtOThdy3QmD0FsTeyJh5Tk8JFrz6uvpwSK1SeIyX4GcS1YiG7OmAZL+/5KAc4svoWPFleAZ1M4eeWls0UNxXb7py7cMm9R6SwV+6WCPYC7Eqh6Ocol2QrifIsVP6chOcMyEuZz8v6igdqNnw3IfHiRzekTQ6Us+kiZ1weJh/jGKrBETdhnbezaD/RlRFtWGp8ln3JZmg9MfaF2J7mFGAo39aXevU6UaMeEO/HNSFxJEXkda2pWJcQJXxmy419tOEjs7aydY+VNAjZ3t+Xq4dsTZuvripUS93nIresVOTYWH1QR+eolTpLFx2/6vldh0bAXdoCgDlEUNVHPOVyg4xp8Aer/wUDtIbjuxc+hyPtgoeEP1CNXIZYdxC7fXP3P3mCq2rVRBBenX4FlabVdSF+AJHbDW/2I0LYA1+nWzkEydc9L/w2mQUPj3tw9zzxz0uBcV6/BemGSoAMYTh45ChTce9oVgRAxfLYwuvn/gSXKYhKcI4l2fNz8qyg+0VKjZDFSPBTW2uHsXRMfJXNLsTRvr9nHik6RAllT8vOOw72meuQ9T/QQhVPCrrunVK9F+asd5HEj1yPjg7MSbsetctueO50Qd7SozpxfLnKsQobFa+xPt//WjHwY2u8RaZ3fBfGlcIFx24gHQL4/z6/kVbSobw/p+is593m09tFp9w8+s65wPsDFk1QL3tnCqEFkrfgRLF6uOnOydbxrb6HMjaBYxX5iA0mRomro3k2oDzg4WgjqOMO5e1zAyA/QvoRFmcBHkdPvszle8xq9X4NDAEgznrpYVbsFTesmAW4DA2ncybkKwhzqZ7N8AQq4tstYwg/aVdNC6/mandiEen4LU0jBg4OR3UcRjw0DkfxqMnT+oDpR8FbxqfCePKG0hLeqBMmTCzG/8zaKV04lrGV3vhvvADkZnm+y9u5Rj46fno8fa/r6oXhXoaWY5+sewBaKeo/45pCK9p0k0hJWtrjUp/TWwaHq0qQZ2WuEVvmbAKrSptD0Ok9Sz6g9bgI6nImrhTMwYpvzmBlvkyDcGJdYOvuOv+PYoNfUTnt5g4daiVSDvuSBSjf/VrWfNj7wi6VrJSY43dBpR2Z9bqWcgJVJKaHqF7YP/C4fRa1IDgUsOpnVfHI3s69CmB6A2qrN4FzSBM7OZwFb1Q/Ic4uLY8sUGcENWoNvrLQgF6M8wXZb7xtq5CyGzjsrEsTANZIhLslIaXD0Dc/dt0EcrlQ7E7o2NN3C4n/VlKearWoZliA7JZk9iaF0hK6n675qdEOt5NCoyD40ATNtRWetVVv8Kwrm24iubQTkeLNJ/RSCiIEpHqo+/D9OVKQwQqbsjOWwX499uF8JvOv+ATyV6pinMLYHCoM3fRkmRfnYc8IwVU6jjqJECu0QFHyvTpOzNlg+18iquRpdiBk4XH35ctySuqRlumAMMA+h5jVZMNzGnUu2ovGOfLnLCz/vPkHzki+4ffR3LBGGLZJ62GekQMPX1W6spqMGM+fy6d0pzLzMTxw8/bC9c5bmElXNTe/EOGxhtjEjzN6W+xvtPrdLZ/iiRZpKI/X+W0vfD5z7jlfWXiJiebqvZfaSJtUFBG/6jtJHkwVW8jpaQA4MuuFKhCReRoB/D9Tm5DqRV+Q2EE5IR7JhbXPTSrkLcQ9NKqzeTlcdxzVzDYlgidNEwaU4TaxTJUmsAutoOQkPKWatg62dU9CFFcmdh/IGKI5xT2fz82BInBPZ+qEOlTZmar3wOWIMLWer9OzJSl7n8ebk1lm1wLngDbL462EayV4AXX+0XET/BYXPZvpRCgicttOBdu5y1OHyd1FBReiYCH4mfcLxHUAvFkeJzj2vVnl6uXZQ5FeBGPIjTYNsbvnjjG69jkrYIOMfj5fgta4ZUNWT3h89HtemeFUJmuyhEWnBDliqKSIxZVeTf04i/X6sp4JTxOiKnUvd7Fj74bTSm/z7lKgTVYzUgSqA2kTlPuYSJKjiRqfYdylunf6xJBQO+c38aGV+Zm4JIQ2mcjC7b6U5QqVBs1UmRn5Q3nyOYbpgjSSHXykvxX+ng0GHaPt1jy8kBzct27uEoeuBeZUNjq//ZUKutJV4Ze8FFtR5F85d+JMzHbLtej7LqVSXi5LcWRnC7AdFjieW6esUtQGlxw+tZtpvcpjHPJkEjZrc86FIF0ic56lrsGVz+Ub0fTGMYsIvJJDp+c1+ums6E2r/1X7K1wTrJSCu8dspVy9POnQd0YmXy3mjzQW1HDtkXFgKk0BNO+ovcjnviSO8fxF0/pTdC36KCk4++iSN0I+Xu0G1BEzJAm4BdEA8ImrcHM0iEEx8qLuY8/DSvQNmd8/RZ7nTJIRId8YxUN7XTm+LmeexwZ6T6WioK1RhOf2eUwysZIvMgrg+k57VzJcBO+Ly6Cg/Ymzs/JD5WuDGGkMK+rYEYdrJw7UmBudT1JLJk6oruU1xISOkaU17IZOHJyMYqBNYvsUvYH23JyqXVc448B4A4Nh2ENBZHuCBMGPN5KPa0gOE0K9rXnXGOxyYfsR0NredNBFyTDlz7EeoSUFoCnihdgFPagZJ4EwXx+OcPBPC8sCsQcgh7PWUIOhab0vm1onRhFOW+Gsha6rm+QDV74ccKWAG6n5CKaf62uQt9Vwi0WCd+di7ZIdeEPkWwCLlhtbdmp7ycRbuzCJeN/aHsOUu6/GHSalIqF7vxHK2oSwA8M6qXB3UZ1Wtm9o6LcNId9S0a3pxZJApQNg/oABtVL8Kl9UNZkH/1V0P+1LjpcTYutI5Y3fVIQAKMm7zRnR1NI1H+BwLVi4eLgT+lSZiv6tmvkPbKwAlulfq2J0mNfYsO/NlqbNx8Tsye+/CG9DYsDxCrFwHSBJpux8DRwpoJgaVVGbdjt5+3vAfdoReR1JFLKtwb2UeozeTVXg6lER0IHMjIHQiOZizVRQBbTMpAitrmWN8Co9SVm5QHYxCa1lH8bM/Pu534ZxgXpW73bsnwx5+H9jzvqfio9umWFNpD3dtG1VxhFgbL+FCsigWLO8+uqDsmO65mHsczAjElmuLdoxTdsh0J+b4rhKYIUKAFHHvsK1poJdkBMDT+2idaA/nHiypNUpdWkuz08FRNSvgacOZDw6drFvuuf2CP5EOmG9qR2b1TihvwBSckAlS9JpMgx0sju2jKA6xk6TMIw5PCljOKypI8/KV2DnVn3D9BvTTGzABw77llw7WjdK7RJqv+nJeK/HZFenHPDFldPJR2SNY+xcCobSEs/gSbMd+uJ3r1sik1Sgpk0hNupTDORfALUIBCW8ZrGT3kXv/Kb7XaBCwI0mAO5KEnxiPwPwtpK/WnQgQ4z4QunJ/xCGTjJ9+y8UXcftpfXIDs4qmbi8vI5byZ2pTt1c/DPp+/z2oObzNjYtSf5XqVcbYNaFxxF4jBCjIIAe0KYQCraznq7MfFfasfYER97Yx6LpTB2qWdUq6yG7OcAKUF5H4JqEobpMoaDcAX3PJj01ISgXlC4fykWzlaolnC64invOC/3Y53wOf05oyyVJ5+ABZsgJHV47RZsMbLKrxEZ771WIE4PN8qKU5RuXqLpNHHu/ZuoS54aOl5HZt4HVQ+1y+e1i9P4OAW82e2C31wE0pR2iY1XoE87ftQplBlf6cAMtqT9BKLjD2h+/EfkNXzbYSiTi2eAB6BJsfqDzPT3hH79OyI5jrTQBwSrMLeqTldXMnRzw/ZDdHUtdxBVEhEFylQv2EKZBFdduCV7Jw+2ItgtRRWDpWiy8G428a9shV6opG8+MWx4Kdk/ds4/X0YGA24pzBErKHK7FpQMgixlauuRe2JAxCBWcZcwqghL1jB7sCEMdwaI0itOyYnr35uPBdl2NfClLsK5HbGBHzu7bXX2BFmDDw5uwsN4ogT52yxw6oGzi7+7ygPMyuoNz5ny87AnXdK3uJri+Gka5Jw6+k/BSuZnJn5GlbWzrt6dnF+oJrpkMtQwhzmspHhWMT2bneGHhMb0+Bdk7Z40jp4iiTz6JbdzsuxFAyeI0Wu3FgzlxjSdekYkdCTmpaYraO0X6K/0QHHbb4T2yeM0=", "recovery_checkpoint": "wiki_generation_completed", "last_commit_id": "496450cea5d19b165936cdba3307a7da48d1e21a", "last_commit_update": "2025-09-21T21:19:44+08:00", "gmt_create": "2025-09-22T20:31:00.897108+08:00", "gmt_modified": "2025-09-22T22:19:10.9915418+08:00", "extend_info": "{\"language\":\"zh\",\"active\":true,\"branch\":\"main\",\"shareStatus\":\"\",\"server_error_code\":\"\",\"cosy_version\":\"\"}"}}