# 查询API

<cite>
**本文档引用文件**   
- [QueryPayOrderRQ.java](file://sys-payment/src/main/java/com/unipay/pay/rqrs/payorder/QueryPayOrderRQ.java)
- [QueryPayOrderRS.java](file://sys-payment/src/main/java/com/unipay/pay/rqrs/payorder/QueryPayOrderRS.java)
- [QueryOrderController.java](file://sys-payment/src/main/java/com/unipay/pay/ctrl/payorder/QueryOrderController.java)
- [PayOrder.java](file://core/src/main/java/com/unipay/core/entity/PayOrder.java)
- [ApiController.java](file://sys-payment/src/main/java/com/unipay/pay/ctrl/ApiController.java)
- [PayOrderService.java](file://service/src/main/java/com/unipay/service/impl/PayOrderService.java)
- [ApiCodeEnum.java](file://core/src/main/java/com/unipay/core/constants/ApiCodeEnum.java)
- [CS.java](file://core/src/main/java/com/unipay/core/constants/CS.java)
</cite>

## 目录
1. [接口概述](#接口概述)
2. [请求参数](#请求参数)
3. [响应数据结构](#响应数据结构)
4. [认证要求](#认证要求)
5. [订单状态说明](#订单状态说明)
6. [JSON响应示例](#json响应示例)
7. [错误情况](#错误情况)
8. [轮询频率与性能优化](#轮询频率与性能优化)

## 接口概述

`GET /api/pay/queryOrder` 接口用于通过商户订单号或平台订单号查询订单状态。该接口是支付系统中用于订单状态查询的核心接口，支持商户通过商户订单号（mchOrderNo）或支付系统订单号（payOrderId）来获取订单的详细信息。

该接口位于 `QueryOrderController` 类中，通过 `queryOrder` 方法实现。接口要求进行商户身份验证和签名验证，确保请求的安全性。查询结果基于 `PayOrder` 实体类，并通过 `QueryPayOrderRS` 类构建响应数据。

**Section sources**
- [QueryOrderController.java](file://sys-payment/src/main/java/com/unipay/pay/ctrl/payorder/QueryOrderController.java#L21-L50)
- [PayOrder.java](file://core/src/main/java/com/unipay/core/entity/PayOrder.java#L24-L278)

## 请求参数

查询订单状态的请求参数定义在 `QueryPayOrderRQ` 类中，继承自 `AbstractMchAppRQ`，包含以下两个核心字段：

| 参数名 | 类型 | 必填 | 描述 |
| :--- | :--- | :--- | :--- |
| mchOrderNo | String | 是（二选一） | 商户系统内的订单号，用于标识商户的交易订单。 |
| payOrderId | String | 是（二选一） | 支付系统的唯一订单号，由系统生成。 |

请求必须提供 `mchOrderNo` 或 `payOrderId` 中的至少一个。如果两者都为空，接口将返回错误。

**Section sources**
- [QueryPayOrderRQ.java](file://sys-payment/src/main/java/com/unipay/pay/rqrs/payorder/QueryPayOrderRQ.java#L11-L20)

## 响应数据结构

查询订单的响应数据结构由 `QueryPayOrderRS` 类定义，继承自 `AbstractRS`。该类将 `PayOrder` 实体的字段映射为对外暴露的响应字段，所有时间字段（如 `successTime` 和 `createdAt`）均转换为毫秒级时间戳。

| 字段名 | 类型 | 描述 |
| :--- | :--- | :--- |
| payOrderId | String | 支付系统生成的唯一订单号。 |
| mchNo | String | 发起交易的商户号。 |
| appId | String | 商户应用ID，用于标识具体的商户应用。 |
| mchOrderNo | String | 商户系统内的订单号。 |
| ifCode | String | 支付接口代码，如 "alipay"、"wxpay" 等。 |
| wayCode | String | 支付方式代码，如 "ALI_QR"（支付宝二维码）。 |
| amount | Long | 支付金额，单位为分。 |
| currency | String | 三位货币代码，例如 "cny" 表示人民币。 |
| state | Byte | 支付状态码，具体含义见下文。 |
| clientIp | String | 发起支付请求的客户端IP地址。 |
| subject | String | 商品标题。 |
| body | String | 商品描述信息。 |
| channelOrderNo | String | 渠道（如支付宝、微信）生成的订单号。 |
| errCode | String | 渠道返回的支付错误码（如果支付失败）。 |
| errMsg | String | 渠道返回的支付错误描述（如果支付失败）。 |
| extParam | String | 商户在创建订单时传入的扩展参数。 |
| successTime | Long | 订单支付成功的时间戳（毫秒）。 |
| createdAt | Long | 订单创建的时间戳（毫秒）。 |

**Section sources**
- [QueryPayOrderRS.java](file://sys-payment/src/main/java/com/unipay/pay/rqrs/payorder/QueryPayOrderRS.java#L12-L121)
- [PayOrder.java](file://core/src/main/java/com/unipay/core/entity/PayOrder.java#L24-L278)

## 认证要求

该接口要求进行严格的商户身份验证和签名验证，流程如下：

1.  **参数获取与通用验证**：通过 `getRQ` 方法获取请求参数并进行基础格式验证。
2.  **商户信息校验**：
    *   验证 `mchNo`（商户号）和 `appId`（应用ID）是否存在且不为空。
    *   通过 `configContextQueryService.queryMchInfoAndAppInfo(mchNo, appId)` 查询商户和应用的配置上下文。
    *   检查商户信息（`MchInfo`）是否存在且状态为启用（`CS.YES`）。
    *   检查商户应用（`MchApp`）是否存在且状态为启用（`CS.YES`）。
    *   验证 `appId` 与 `mchNo` 的匹配关系。
3.  **签名验证**：
    *   获取商户应用的密钥（`appSecret`）。
    *   将请求参数（除 `sign` 外）转换为JSON对象。
    *   使用 `JeepayKit.getSign()` 方法，基于JSON对象和 `appSecret` 重新计算签名。
    *   将计算出的签名与请求中的 `sign` 参数进行比对，不一致则抛出“验签失败”异常。

只有通过以上所有校验，请求才会被处理。

**Section sources**
- [ApiController.java](file://sys-payment/src/main/java/com/unipay/pay/ctrl/ApiController.java#L40-L86)
- [CS.java](file://core/src/main/java/com/unipay/core/constants/CS.java#L13-L203)

## 订单状态说明

订单状态由 `PayOrder` 类中的 `state` 字段表示，是一个字节型（Byte）枚举值。其流转条件如下：

| 状态码 | 状态名 | 含义 | 流转条件 |
| :--- | :--- | :--- | :--- |
| 0 | 订单生成 | 订单已创建，等待支付。 | 订单创建时的初始状态。 |
| 1 | 支付中 | 订单已提交至支付渠道，正在处理中。 | 用户扫码或确认支付后，系统向渠道发起支付请求。 |
| 2 | 支付成功 | 订单已成功支付，资金已结算。 | 收到支付渠道的“支付成功”回调通知。 |
| 3 | 支付失败 | 支付尝试失败。 | 收到支付渠道的“支付失败”通知，或支付超时。 |
| 4 | 已撤销 | 订单已被撤销。 | 用户在支付前主动取消，或系统在特定条件下（如超时）自动撤销。 |
| 5 | 已退款 | 订单已全额或部分退款。 | 成功发起退款请求并完成退款流程。 |
| 6 | 订单关闭 | 订单已关闭，无法再进行支付。 | 订单超时未支付，或由商户/系统主动关闭。 |

**Section sources**
- [PayOrder.java](file://core/src/main/java/com/unipay/core/entity/PayOrder.java#L38-L44)
- [CS.java](file://core/src/main/java/com/unipay/core/constants/CS.java#L13-L203)

## JSON响应示例

**成功响应示例：**

```json
{
  "code": 0,
  "msg": "SUCCESS",
  "data": {
    "payOrderId": "PO20231010123456789",
    "mchNo": "MCH123456789",
    "appId": "APP987654321",
    "mchOrderNo": "ORDER20231010001",
    "ifCode": "alipay",
    "wayCode": "ALI_QR",
    "amount": 10000,
    "currency": "cny",
    "state": 2,
    "clientIp": "***********",
    "subject": "商品名称",
    "body": "商品描述",
    "channelOrderNo": "2023101021001004410212345678",
    "errCode": null,
    "errMsg": null,
    "extParam": "customParam",
    "successTime": 1696915200000,
    "createdAt": 1696915000000
  },
  "sign": "calculated_signature_value"
}
```

**失败响应示例（订单不存在）：**

```json
{
  "code": 9999,
  "msg": "订单不存在",
  "data": null,
  "sign": null
}
```

**Section sources**
- [QueryOrderController.java](file://sys-payment/src/main/java/com/unipay/pay/ctrl/payorder/QueryOrderController.java#L31-L48)
- [ApiCodeEnum.java](file://core/src/main/java/com/unipay/core/constants/ApiCodeEnum.java#L7-L40)

## 错误情况

调用该接口时可能遇到以下错误情况：

| 错误码 | 错误信息 | 原因 |
| :--- | :--- | :--- |
| 9999 | 订单不存在 | 提供的 `mchOrderNo` 或 `payOrderId` 在系统中找不到对应的订单记录。 |
| 9999 | mchOrderNo 和 payOrderId不能同时为空 | 请求参数中 `mchOrderNo` 和 `payOrderId` 均未提供。 |
| 9999 | 参数有误！ | `mchNo`、`appId` 或 `sign` 参数为空或格式错误。 |
| 9999 | 商户或商户应用不存在 | 提供的 `mchNo` 或 `appId` 无效。 |
| 9999 | 商户信息不存在或商户状态不可用 | 商户存在但状态为禁用。 |
| 9999 | 商户应用不存在或应用状态不可用 | 商户应用存在但状态为禁用。 |
| 9999 | 参数appId与商户号不匹配 | `appId` 不属于 `mchNo` 对应的商户。 |
| 9999 | 验签失败 | 请求的签名与使用商户密钥计算出的签名不匹配。 |

**Section sources**
- [QueryOrderController.java](file://sys-payment/src/main/java/com/unipay/pay/ctrl/payorder/QueryOrderController.java#L31-L48)
- [ApiController.java](file://sys-payment/src/main/java/com/unipay/pay/ctrl/ApiController.java#L40-L86)

## 轮询频率与性能优化

为了减少对服务端的压力，建议遵循以下轮询策略和性能优化措施：

1.  **智能轮询策略**：
    *   **初始阶段**：用户发起支付后，客户端可以以 **3-5秒** 的间隔进行轮询。
    *   **状态变更后**：一旦查询到订单状态变为“支付成功”、“支付失败”或“订单关闭”，应立即停止轮询。
    *   **超时处理**：设置合理的轮询总时长（例如10分钟），超过该时长仍未得到最终状态，应提示用户“支付超时”，并停止轮询。

2.  **性能优化建议**：
    *   **缓存查询结果**：服务端可以对查询结果进行短时间的缓存（例如1-2秒），避免对数据库的频繁查询。
    *   **使用索引**：确保数据库表 `t_pay_order` 在 `mchNo`、`mchOrderNo` 和 `payOrderId` 字段上建立了高效索引，以保证查询性能。
    *   **异步处理**：对于非关键的查询操作，可以考虑使用消息队列进行异步处理，减轻主服务的压力。
    *   **客户端优化**：客户端应避免在用户未关注支付结果时进行后台轮询，以节省用户设备的电量和网络流量。

**Section sources**
- [QueryOrderController.java](file://sys-payment/src/main/java/com/unipay/pay/ctrl/payorder/QueryOrderController.java#L31-L48)
- [PayOrderService.java](file://service/src/main/java/com/unipay/service/impl/PayOrderService.java#L127-L136)