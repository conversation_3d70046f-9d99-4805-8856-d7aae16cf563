
# 转账API

<cite>
**本文档引用文件**  
- [TransferOrderRQ.java](file://sys-payment/src/main/java/com/unipay/pay/rqrs/transfer/TransferOrderRQ.java)
- [TransferOrderRS.java](file://sys-payment/src/main/java/com/unipay/pay/rqrs/transfer/TransferOrderRS.java)
- [TransferOrderController.java](file://sys-payment/src/main/java/com/unipay/pay/ctrl/transfer/TransferOrderController.java)
- [TransferOrder.java](file://core/src/main/java/com/unipay/core/entity/TransferOrder.java)
- [TransferOrderService.java](file://service/src/main/java/com/unipay/service/impl/TransferOrderService.java)
- [ITransferService.java](file://sys-payment/src/main/java/com/unipay/pay/channel/ITransferService.java)
- [AbstractTransferNoticeService.java](file://sys-payment/src/main/java/com/unipay/pay/channel/AbstractTransferNoticeService.java)
- [TransferNotifyController.java](file://sys-merchant/src/main/java/com/unipay/mch/ctrl/transfer/TransferNotifyController.java)
- [ApiCodeEnum.java](file://core/src/main/java/com/unipay/core/constants/ApiCodeEnum.java)
- [ValidateService.java](file://sys-payment/src/main/java/com/unipay/pay/service/ValidateService.java)
</cite>

## 目录
1. [接口概述](#接口概述)
2. [请求体结构（TransferOrderRQ）](#请求体结构transferorderrq)
3. [响应格式（TransferOrderRS）](#响应格式transferorderrs)
4. [安全认证与签名验证](#安全认证与签名验证)
5. [转账类型与渠道限制](#转账类型与渠道限制)
6. [手续费计算规则](#手续费计算规则)
7. [请求示例](#请求示例)
8. [错误码说明](#错误码说明)
9. [转账状态查询机制](#转账状态查询机制)
10. [异步通知机制](#异步通知机制)
11. [资金安全与防重放攻击最佳实践](#资金安全与防重放攻击最佳实践)

## 接口概述

`POST /api/pay/transferOrder` 接口用于发起转账请求，支持多种入账方式和支付渠道。该接口由 `TransferOrderController` 类实现，位于 `sys-payment` 模块中，是系统核心支付功能的一部分。

该接口遵循统一的请求-响应模式，所有请求必须携带商户签名以确保安全性。系统在接收到请求后会进行参数校验、商户配置检查、渠道可用性验证，并最终调用对应的支付通道服务完成转账操作。

```mermaid
sequenceDiagram
participant 商户 as 商户系统
participant 支付网关 as 支付网关(sys-payment)
participant 支付通道 as 第三方支付通道(微信/支付宝等)
商户->>支付网关 : POST /api/pay/transferOrder (含签名)
支付网关->>支付网关 : 验证签名与参数
支付网关->>支付网关 : 检查商户配置与渠道可用性
支付网关->>支付网关 : 生成转账订单并入库
支付网关->>支付通道 : 调用具体支付通道转账接口
支付通道-->>支付网关 : 返回处理结果
支付网关->>支付网关 : 更新订单状态
支付网关->>商户 : 返回签名后的响应结果
支付网关->>商户 : 异步通知转账结果
```

**Diagram sources**
- [TransferOrderController.java](file://sys-payment/src/main/java/com/unipay/pay/ctrl/transfer/TransferOrderController.java#L46-L138)
- [TransferOrder.java](file://core/src/main/java/com/unipay/core/entity/TransferOrder.java#L22-L211)

**Section sources**
- [TransferOrderController.java](file://sys-payment/src/main/java/com/unipay/pay/ctrl/transfer/TransferOrderController.java#L34-L236)

## 请求体结构（TransferOrderRQ）

`TransferOrderRQ` 类定义了转账请求的参数结构，继承自 `AbstractMchAppRQ`，包含商户身份信息和具体的转账业务参数。

### 核心字段说明

| 字段名 | 类型 | 是否必填 | 描述 |
|--------|------|----------|------|
| mchNo | String | 是 | 商户号 |
| appId | String | 是 | 商户应用ID |
| mchOrderNo | String | 是 | 商户订单号，需保证在商户侧唯一 |
| ifCode | String | 是 | 支付接口代码，如 `WX_CASH`、`ALIPAY_CASH` |
| entryType | String | 是 | 入账方式，决定资金如何到账 |
| amount | Long | 是 | 转账金额，单位为分 |
| currency | String | 是 | 货币代码，如 `CNY` |
| accountNo | String | 是 | 收款账号 |
| accountName | String | 否 | 收款人姓名 |
| bankName | String | 否 | 收款人开户行名称 |
| clientIp | String | 否 | 客户端IP地址 |
| transferDesc | String | 是 | 转账备注信息 |
| notifyUrl | String | 否 | 异步通知地址，用于接收转账结果 |
| channelExtra | String | 否 | 特定渠道发起额外参数，JSON格式 |
| extParam | String | 否 | 商户扩展参数，可用于业务关联 |

### 参数校验规则

系统通过 `ValidateService` 对请求参数进行校验，具体规则如下：

- `mchNo`、`appId`、`mchOrderNo`、`ifCode`、`entryType`、`amount`、`currency`、`accountNo`、`transferDesc` 不能为空。
- `amount` 必须大于等于1（即至少1分钱）。
- `notifyUrl` 如果提供，必须是有效的HTTP或HTTPS URL。

```mermaid
classDiagram
class TransferOrderRQ {
+String mchNo
+String appId
+String mchOrderNo
+String ifCode
+String entryType
+Long amount
+String currency
+String accountNo
+String accountName
+String bankName
+String clientIp
+String transferDesc
+String notifyUrl
+String channelExtra
+String extParam
}
class AbstractMchAppRQ {
+String mchNo
+String appId
}
TransferOrderRQ --|> AbstractMchAppRQ : 继承
```

**Diagram sources**
- [TransferOrderRQ.java](file://sys-payment/src/main/java/com/unipay/pay/rqrs/transfer/TransferOrderRQ.java#L14-L64)
- [AbstractMchAppRQ.java](file://sys-payment/src/main/java/com/unipay/pay/rqrs/AbstractMchAppRQ.java#L12-L24)

**Section sources**
- [TransferOrderRQ.java](file://sys-payment/src/main/java/com/unipay/pay/rqrs/transfer/TransferOrderRQ.java#L14-L64)
- [ValidateService.java](file://sys-payment/src/main/java/com/unipay/pay/service/ValidateService.java#L20-L30)

## 响应格式（TransferOrderRS）

`TransferOrderRS` 类定义了转账接口的响应数据结构，包含转账订单的详细信息和处理结果。

### 响应字段说明

| 字段名 | 类型 | 描述 |
|--------|------|------|
| transferId | String | 系统生成的转账单号，全局唯一 |
| mchOrderNo | String | 商户订单号 |
| amount | Long | 转账金额（单位：分） |
| accountNo | String | 收款账号 |
| accountName | String | 收款人姓名 |
| bankName | String | 收款人开户行名称 |
| state | Byte | 转账状态：0-订单生成, 1-转账中, 2-转账成功, 3-转账失败, 4-订单关闭 |
| channelOrderNo | String | 渠道返回的订单号 |
| channelResData | String | 渠道响应的原始数据（如微信确认数据包） |
| errCode | String | 渠道返回的错误代码 |
| errMsg | String | 渠道返回的错误信息 |

### 响应示例

```json
{
  "code": 0,
  "msg": "SUCCESS",
  "data": {
    "transferId": "T20231015123456789",
    "mchOrderNo": "MCH20231015001",
    "amount": 10000,
    "accountNo": "**********",
    "accountName": "张三",
    "bankName": "中国工商银行",
    "state": 1,
    "channelOrderNo": "****************",
    "channelResData": "{\"prepay_id\":\"wx20231015123456\"}",
    "errCode": "",
    "errMsg": ""
  },
  "sign": "abc123def456..."
}
```

**Diagram sources**
- [TransferOrderRS.java](file://sys-payment/src/main/java/com/unipay/pay/rqrs/transfer/TransferOrderRS.java#L12-L69)
- [TransferOrder.java](file://core/src/main/java/com/unipay/core/entity/TransferOrder.java#L22-L211)

**Section sources**
- [TransferOrderRS.java](file://sys-payment/src/main/java/com/unipay/pay/rqrs/transfer/TransferOrderRS.java#L12-L69)

## 安全认证与签名验证

系统采用基于商户密钥的签名机制来保证接口调用的安全性。

### 认证流程

1. 商户在发起 `POST /api/pay/transferOrder` 请求时，需在请求体中包含所有业务参数。
2. 系统接收到请求后，首先从请求体中提取 `mchNo` 和 `appId`。
3. 根据 `mchNo` 和 `appId` 查询商户的应用信息，获取其 `appSecret`。
4. 使用 `appSecret` 对请求参数进行签名验证。
5. 只有签名验证通过，才会继续处理业务逻辑。

### 签名算法

签名算法通常采用 HMAC-SHA256 或 MD5，具体实现由 `JeepayKit` 工具类提供。签名过程会排除 `sign` 字段，并按照参数名的字典序进行排序后拼接。

```mermaid
flowchart TD
A[商户发起转账请求] --> B{包含mchNo和appId?}
B --> |否| C[返回参数错误]
B --> |是| D[查询商户应用信息]
D --> E{商户信息存在?}
E --> |否| F[返回商户信息不存在]
E --> |是| G[获取appSecret]
G --> H[验证请求签名]
H --> |验证失败| I[返回签名错误]
H --> |验证成功| J[处理业务逻辑]
```

**Diagram sources**
- [TransferOrderController.java](file://sys-payment/src/main/java/com/unipay/pay/ctrl/transfer/TransferOrderController.java#L46-L138)
- [ApiController.java](file://sys-payment/src/main/java/com/unipay/pay/ctrl/ApiController.java) (隐含引用，用于 `getRQByWithMchSign` 方法)

**Section sources**
- [TransferOrderController.java](file://sys-payment/src/main/java/com/unipay/pay/ctrl/transfer/TransferOrderController.java#L46-L138)

## 转账类型与渠道限制

系统支持多种转账类型和入账方式，具体取决于商户配置的支付渠道。

### 转账类型

| 类型 | 说明 |
|------|------|
| 平台到用户 | 平台向用户的个人账户转账，如退款、返现、奖励等 |
| 用户到用户 | 用户A向用户B转账，需平台作为中介 |
| 商户到用户 | 商户向其客户或合作伙伴转账 |

### 入账方式（entryType）

| 入账方式 | 说明 | 支持渠道 |
|----------|------|----------|
| WX_CASH | 微信零钱 | 微信支付 |
| ALIPAY_CASH | 支付宝转账 | 支付宝 |
| BANK_CARD | 银行卡 | 银联、网银等 |

### 渠道限制

- 商户必须在系统后台为其应用 (`appId`) 开通相应的支付接口 (`ifCode`) 才能使用。
- 每个支付通道 (`ITransferService`) 会通过 `isSupport(entryType)` 方法检查是否支持特定的入账方式。
- 例如，`WxTransferService` 只支持 `WX_CASH`，而 `AliPayTransferService` 只支持 `ALIPAY_CASH`。

```mermaid
classDiagram
class ITransferService {
+String getIfCode()
+boolean isSupport(String entryType)
+String preCheck(TransferOrderRQ bizRQ, TransferOrder transferOrder)
+ChannelRetMsg transfer(TransferOrderRQ bizRQ, TransferOrder refundOrder, MchAppConfigContext mchAppConfigContext)
+ChannelRetMsg query(TransferOrder transferOrder, MchAppConfigContext mchAppConfigContext)
}
class WxTransferService {
+String getIfCode() // 返回 "WX_CASH"
+boolean isSupport(String entryType) // 只支持 "WX_CASH"
}
class AliPayTransferService {
+String getIfCode() // 返回 "ALIPAY_CASH"
+boolean isSupport(String entryType) // 只支持 "ALIPAY_CASH"
}
ITransferService <|-- WxTransferService
ITransferService <|-- AliPayTransferService
```

**Diagram sources**
- [ITransferService.java](file://sys-payment/src/main/java/com/unipay/pay/channel/ITransferService.java#L12-L29)
- [TransferOrderController.java](file://sys-payment/src/main/java/com/unipay/pay/ctrl/transfer/TransferOrderController.java#L46-L138)

**Section sources**
- [ITransferService.java](file://sys-payment/src/main/java/com/unipay/pay/channel/ITransferService.java#L12-L29)
- [TransferOrderController.java](file://sys-payment/src/main/java/com/unipay/pay/ctrl/transfer/TransferOrderController.java#L46-L138)

## 手续费计算规则

本API文档不直接包含手续费计算的代码逻辑。手续费的计算通常在支付通道的实现类（如 `WxTransferService` 或 `AliPayTransferService`）中完成，属于支付通道的内部业务逻辑。

### 一般规则

1. **费率模式**：按转账金额的百分比收取，例如 0.1%。
2. **固定费用模式**：每笔转账收取固定费用，例如 1元。
3. **混合模式**：费率 + 固定费用。
4. **阶梯定价**：根据转账金额区间采用不同的费率。

### 注意事项

- 手续费通常由商户承担，从商户账户余额中扣除。
- 转账金额 (`amount`) 是用户实际收到的金额，不包含手续费。
- 具体的费率和计算方式需要参考与第三方支付渠道签订的合同。

**Section sources**
- [ITransferService.java](file://sys-payment/src/main/java/com/unipay/pay/channel/ITransferService.java#L12-L29)

## 请求示例

### 正常转账请求

```json
POST /api/pay/transferOrder HTTP/1.1
Content-Type: application/json

{
  "mchNo": "MCH123456789",
  "appId": "APP123456789",
  "mchOrderNo": "ORDER20231015001",
  "ifCode": "WX_CASH",
  "entryType": "WX_CASH",
  "amount": 10000,
  "currency": "CNY",
  "accountNo": "wx_openid_123456",
  "accountName": "张三",
  "clientIp": "***********",
  "transferDesc": "订单退款",
  "notifyUrl": "https://yourdomain.com/notify/transfer",
  "extParam": "{\"orderId\":\"12345\"}",
  "sign": "生成的签名"
}
```

### 异常情况请求

#### 情况1：缺少必填参数

```json
{
  "mchNo": "MCH123456789",
  "appId": "APP123456789"
  // 缺少 mchOrderNo, ifCode 等必填字段
}
```
**预期响应**：`{"code": 11, "msg": "参数有误[商户订单号不能为空]"}`

#### 情况2：重复商户订单号

```json
{
  "mchNo": "MCH123456789",
  "appId": "APP123456789",
  "mchOrderNo": "ORDER20231015001", // 该订单号已存在
  "ifCode": "WX_CASH",
  "entryType": "WX_CASH",
  "amount": 10000,
  "currency": "CNY",
  "accountNo": "wx_openid_123456",
  "transferDesc": "重复订单"
}
```
**预期响应**：`{"code": 9999, "msg": "商户订单[ORDER20231015001]已存在"}`

#### 情况3：应用未开通接口

```json
{
  "mchNo": "MCH123456789",
  "appId": "APP123456789",
  "mchOrderNo": "ORDER20231015002",
  "ifCode": "NON_EXISTENT_IF", // 该应用未配置此接口
  "entryType": "WX_CASH",
  "amount": 10000,
  "currency": "CNY",
  "accountNo": "wx_openid_123456",
  "transferDesc": "未开通接口"
}
```
**预期响应**：`{"code": 9999, "msg": "应用未开通此接口配置!"}`

**Section sources**
- [TransferOrderController.java](file://sys-payment/src/main/java/com/unipay/pay/ctrl/transfer/TransferOrderController.java#L46-L138)
- [ApiCodeEnum.java](file://core/src/main/java/com/unipay/core/constants/ApiCodeEnum.java#L1-L41)

## 错误码说明

系统定义了统一的错误码体系，位于 `ApiCodeEnum` 枚举类中。

| 错误码 | 错误信息 | 说明 |
|--------|----------|------|
| 0 | SUCCESS | 请求成功 |
| 9999 | 自定义业务异常 | 业务逻辑错误，如订单已存在、余额不足等 |
| 10 | 系统异常[%s] | 系统内部错误，如数据库连接失败 |
| 11 | 参数有误[%s] | 请求参数缺失或格式错误 |
| 12 | 数据库服务异常 | 数据库操作失败 |
| 5000 | 新增失败 | 创建记录失败 |
| 5001 | 删除失败 | 删除记录失败 |
| 5002 | 修改失败 | 更新记录失败 |
| 5003 | 记录不存在 | 查询的记录不存在 |
| 5004 | 权限错误，当前用户不支持此操作 | 用户权限不足 |

**Section sources**
- [ApiCodeEnum.java](file://core/src/main/java/com/unipay/core/constants/ApiCodeEnum.java#L1-L41)

## 转账状态查询机制

系统提供了多种方式查询转账订单状态：

1. **同步响应**：`POST /api/pay/transferOrder` 接口的响应中包含当前订单状态。
2. **异步通知**：系统会向商户提供的 `notifyUrl` 发送状态变更通知。
3. **主动查询**：
   - 商户可通过 `GET /api/transferOrders/{recordId}` 接口查询单个订单详情。
   - 管理员可通过 `GET /api/transferOrders` 接口查询订单列表。

状态流转如下：
`订单生成(0)` → `转账中(1)` → `转账成功(2)` 或 `转账失败(3)`
`订单生成(0)` → `订单关闭(4)`

```mermaid
stateDiagram-v2
[*] --> 订单生成
订单生成 --> 转账中 : 调用支付通道
转账中 --> 转账成功 : 通道确认成功
转账中 --> 转账失败 : 通道确认失败
转账中 --> 订单关闭 : 超时或取消
订单生成 --> 订单关闭 : 验证失败
```

**Diagram sources**
- [TransferOrder.java](file://core/src/main/java/com/unipay/core/entity/TransferOrder.java#L22-L211)
- [TransferOrderController.java](file://sys-payment/src/main/java/com/unipay/pay/ctrl/transfer/TransferOrderController.java#L173-L210)

**Section sources**
- [TransferOrder.java](file://core/src/main/java/com/unipay/core/entity/TransferOrder.java#L22-L211)
- [TransferOrderController.java](file://sys-payment/src/main/java/com/unipay/pay/ctrl/transfer/TransferOrderController.java#L173-L210)

## 异步通知机制

当转账状态发生变更时，系统会向商户在请求中提供的 `notifyUrl` 发送异步通知。

### 通知流程

1. 支付通道返回最终结果（成功或失败）。
2. `TransferOrderController` 调用 `payMchNotifyService.transferOrderNotify(transferOrder)`。
3. `PayMchNotifyService` 将通知任务放入消息队列或直接发送。
4. 系统向 `notifyUrl` 发送POST请求，包含通知数据和签名。
5. 商户系统接收通知，验证签名，并返回处理结果。

### 通知重试

为保证可靠性，系统会对失败的通知进行重试，通常采用指数退避策略。

```mermaid
sequenceDiagram
participant 支付网关 as 支付网关
participant 消息队列 as 消息队列(MQ)
participant 商户系统 as 商户系统
支付网关->>支付网关 : 转账状态变为成功/失败
支付网关->>消息队列 : 发送通知消息(PayOrderMchNotifyMQ)
消息队列-->>支付网关 : 确认接收
消息队列->>商户系统 : POST /notify/transfer (含签名)
alt 通知成功
商户系统-->>消息队列 : HTTP 200
消息队列->>消息队列 : 删除消息
else 通知失败
消息队列->>消息队列 : 记录失败，等待重试
消息队列->>商户系统 : 重试发送 (最多N次)
end
```

**Diagram sources**
- [TransferOrderController.java](file://sys-payment/src/main/java/com/unipay/pay/ctrl/transfer/TransferOrderController.java#L173-L210)
- [PayMchNotifyService.java](file://sys-payment/src/main/java/com/unipay/pay/service/PayMchNotifyService.java) (隐含引用)
- [PayOrderMchNotifyMQ.java](file://components/components-mq/src/main/java/com/unipay/components/mq/model/PayOrderMchNotifyMQ.java) (隐含引用)

**Section sources**
- [TransferOrderController.java](file://sys-payment/src/main/java/com/unipay/pay/ctrl/transfer/TransferOrderController.java#L173-L210)

## 资金安全与防重放攻击最佳实践

为确保资金安全，系统和商户需共同采取以下措施：

### 系统侧措施

1. **幂等性设计**：通过 `mchOrderNo` 保证同一商户订单号只能成功创建一次转账订单。
2. **严格参数校验**：使用 `@NotBlank`、`@NotNull` 等注解和