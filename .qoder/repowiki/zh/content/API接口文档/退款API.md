
# 退款API

<cite>
**本文档引用文件**  
- [RefundOrderRQ.java](file://sys-payment/src/main/java/com/unipay/pay/rqrs/refund/RefundOrderRQ.java)
- [RefundOrderRS.java](file://sys-payment/src/main/java/com/unipay/pay/rqrs/refund/RefundOrderRS.java)
- [RefundOrder.java](file://core/src/main/java/com/unipay/core/entity/RefundOrder.java)
- [RefundOrderController.java](file://sys-payment/src/main/java/com/unipay/pay/ctrl/refund/RefundOrderController.java)
- [RefundOrderService.java](file://service/src/main/java/com/unipay/service/impl/RefundOrderService.java)
- [ApiController.java](file://sys-payment/src/main/java/com/unipay/pay/ctrl/ApiController.java)
- [PayMchNotifyService.java](file://sys-payment/src/main/java/com/unipay/pay/service/PayMchNotifyService.java)
- [JeepayKit.java](file://core/src/main/java/com/unipay/core/utils/JeepayKit.java)
</cite>

## 目录
1. [接口规范](#接口规范)
2. [请求参数](#请求参数)
3. [响应结构](#响应结构)
4. [认证与签名](#认证与签名)
5. [退款规则](#退款规则)
6. [示例](#示例)
7. [错误码](#错误码)
8. [异步通知](#异步通知)
9. [幂等性处理](#幂等性处理)

## 接口规范

**POST /api/refund/refundOrder**

该接口用于商户发起退款请求。系统将验证原始订单状态、退款金额限制，并调用对应支付渠道完成退款操作。

**接口特点**：
- 支持通过商户订单号（mchOrderNo）或支付系统订单号（payOrderId）关联原始订单
- 采用标准RESTful设计，返回结构化JSON响应
- 支持异步结果通知，确保商户系统可靠接收退款结果
- 实现严格的幂等性控制，防止重复退款

**Section sources**
- [RefundOrderController.java](file://sys-payment/src/main/java/com/unipay/pay/ctrl/refund/RefundOrderController.java#L47-L157)

## 请求参数

请求体为JSON格式，基于`RefundOrderRQ`类定义，所有参数均为必填，除非特别说明。

| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| mchNo | String | 是 | 商户号 |
| appId | String | 是 | 应用ID |
| mchOrderNo | String | 否 | 商户系统原始订单号，与payOrderId至少填写一个 |
| payOrderId | String | 否 | 支付系统生成的订单号，与mchOrderNo至少填写一个 |
| mchRefundNo | String | 是 | 商户系统生成的退款单号，需保证在商户维度唯一 |
| refundAmount | Long | 是 | 退款金额，单位：分，必须大于1分 |
| currency | String | 是 | 货币代码，如cny |
| refundReason | String | 是 | 退款原因 |
| clientIp | String | 否 | 客户端IP地址 |
| notifyUrl | String | 否 | 异步通知地址，协议必须为http://或https:// |
| channelExtra | String | 否 | 特定渠道发起时的额外参数，JSON字符串格式 |
| extParam | String | 否 | 商户扩展参数 |
| sign | String | 是 | 签名值 |

**Section sources**
- [RefundOrderRQ.java](file://sys-payment/src/main/java/com/unipay/pay/rqrs/refund/RefundOrderRQ.java#L14-L52)

## 响应结构

响应体为JSON格式，基于`RefundOrderRS`类定义，并包含签名。

| 参数名 | 类型 | 描述 |
|--------|------|------|
| code | Integer | 响应码，0表示成功 |
| msg | String | 响应消息 |
| data | Object | 退款结果数据 |
| &nbsp;&nbsp;refundOrderId | String | 支付系统生成的退款订单号 |
| &nbsp;&nbsp;mchRefundNo | String | 商户发起的退款订单号 |
| &nbsp;&nbsp;payAmount | Long | 原始订单支付金额（单位：分） |
| &nbsp;&nbsp;refundAmount | Long | 申请退款金额（单位：分） |
| &nbsp;&nbsp;state | Byte | 退款状态：0-订单生成, 1-退款中, 2-退款成功, 3-退款失败, 4-退款任务关闭 |
| &nbsp;&nbsp;channelOrderNo | String | 渠道返回的退款单号 |
| &nbsp;&nbsp;errCode | String | 渠道返回错误代码（仅失败时存在） |
| &nbsp;&nbsp;errMsg | String | 渠道返回错误信息（仅失败时存在） |
| sign | String | 响应签名 |

**Section sources**
- [RefundOrderRS.java](file://sys-payment/src/main/java/com/unipay/pay/rqrs/refund/RefundOrderRS.java#L12-L53)

## 认证与签名

### 认证方式
采用基于应用密钥（AppSecret）的签名认证机制。商户在请求头中无需提供身份凭证，身份信息通过`mchNo`和`appId`从请求体获取。

### 签名要求
1. **签名生成**：使用商户对应`appId`的`appSecret`对请求参数进行签名。
2. **签名算法**：采用MD5算法，具体步骤如下：
   - 将所有非空请求参数按参数名ASCII码升序排序
   - 拼接为`key=value&`格式的字符串
   - 在末尾追加`key=appSecret`
   - 对最终字符串进行MD5加密，并转换为大写
3. **验签流程**：服务端收到请求后，使用相同的算法和商户的`appSecret`重新计算签名，并与请求中的`sign`参数比对。

```mermaid
sequenceDiagram
participant 商户系统
participant 支付系统
商户系统->>支付系统 : 发起退款请求 (含sign)
支付系统->>支付系统 : 1. 根据mchNo, appId查询appSecret
支付系统->>支付系统 : 2. 提取请求参数并移除sign
支付系统->>支付系统 : 3. 按规则排序并拼接参数
支付系统->>支付系统 : 4. 追加key=appSecret并MD5加密
支付系统->>支付系统 : 5. 比对计算出的签名与请求sign
alt 签名匹配
支付系统-->>商户系统 : 继续处理退款逻辑
else 签名不匹配
支付系统-->>商户系统 : 返回"验签失败"
end
```

**Diagram sources**
- [ApiController.java](file://sys-payment/src/main/java/com/unipay/pay/ctrl/ApiController.java#L40-L86)
- [JeepayKit.java](file://core/src/main/java/com/unipay/core/utils/JeepayKit.java#L48-L68)

## 退款规则

### 退款金额限制
- 单次退款金额必须大于1分。
- 退款总额不能超过原始订单的支付金额。
- 系统会检查原始订单的已退款金额，确保本次申请后总退款金额不超过订单金额。

### 多次退款规则
- 支持对同一订单进行多次退款，直至累计退款金额等于订单总金额。
- 每次退款申请都会创建独立的退款订单（refundOrderId）。
- 当累计退款金额等于订单金额时，原始订单的退款状态将更新为“全额退款”。

### 与原始订单的关联关系
- 退款订单通过`payOrderId`字段与原始支付订单建立强关联。
- 退款成功后，系统会自动更新原始订单的`refundAmount`（已退款金额）和`refundState`（退款状态）。
- 退款订单的状态机独立于原始订单，但受原始订单状态约束（例如，仅支付成功的订单可退款）。

**Section sources**
- [RefundOrderController.java](file://sys-payment/src/main/java/com/unipay/pay/ctrl/refund/RefundOrderController.java#L47-L157)
- [RefundOrder.java](file://core/src/main/java/com/unipay/core/entity/RefundOrder.java#L22-L204)
- [PayOrder.java](file://core/src/main/java/com/unipay/core/entity/PayOrder.java#L40-L48)

## 示例

### 成功场景
**请求**：
```json
{
  "mchNo": "MCH20230001",
  "appId": "APP20230001",
  "mchOrderNo": "MCHORDER20230001",
  "mchRefundNo": "REF20230001",
  "refundAmount": 100,
  "currency": "cny",
  "refundReason": "客户取消订单",
  "notifyUrl": "https://yourdomain.com/notify/refund",
  "sign": "A1B2C3D4E5F6..."
}
```

**响应**：
```json
{
  "code": 0,
  "msg": "success",
  "data": {
    "refundOrderId": "REFUND20230001",
    "mchRefundNo": "REF20230001",
    "payAmount": 1000,
    "refundAmount": 100,
    "state": 1,
    "channelOrderNo": "CHREF20230001"
  },
  "sign": "F6E5D4C3B2A1..."
}
```

### 失败场景
**请求**（金额超限）：
```json
{
  "mchNo": "MCH20230001",
  "appId": "APP20230001",
  "payOrderId": "PAY20230001",
  "mchRefundNo": "REF20230002",
  "refundAmount": 1500,
  "currency": "cny",
  "refundReason": "测试超限",
  "sign": "X1Y2Z3..."
}
```

**响应**：
```json
{
  "code": -1,
  "msg": "申请金额超出订单可退款余额，请检查退款金额"
}
```

**Section sources**
- [RefundOrderController.java](file://sys-payment/src/main/java/com/unipay/pay/ctrl/refund/RefundOrderController.java#L47-L157)

## 错误码

| 错误码 | 错误信息 | 原因 | 处理方法 |
|--------|--------|------|--------|
| -1 | mchOrderNo 和 payOrderId不能同时为空 | 未提供订单关联信息 | 至少填写mchOrderNo或payOrderId中的一个 |
| -1 | 异步通知地址协议仅支持http:// 或 https:// ! | notifyUrl协议不正确 | 修改为http://或https://开头的URL |
| -1 | 退款订单不存在 | 提供的订单号在系统中找不到 | 检查mchOrderNo或payOrderId是否正确 |
| -1 | 订单状态不正确， 无法完成退款 | 原始订单未支付成功 | 确认订单状态为支付成功后再发起退款 |
| -1 | 订单已全额退款，本次申请失败 | 订单已全部退完 | 无需再次退款 |
| -1 | 申请金额超出订单可退款余额，请检查退款金额 | 退款金额超过剩余可退金额 | 调整退款金额 |
| -1 | 支付订单具有在途退款申请，请稍后再试 | 存在未完成的退款 | 等待在途退款完成或失败后重试 |
| -1 | 商户退款订单号[xxx]已存在 | mchRefundNo重复 | 更换为新的、唯一的退款单号 |
| -1 | 获取商户应用信息失败 | mchNo或appId无效 | 检查商户号和应用ID是否正确 |
| -1 | 当前通道不支持退款！ | 支付方式不支持退款 | 选择支持退款的支付方式 |
| -1 | 验签失败 | 签名计算错误 | 检查签名算法和appSecret是否正确 |
| -1 | 系统异常 | 服务端内部错误 | 记录请求信息，联系技术支持 |

**Section sources**
- [RefundOrderController.java](file://sys-payment/src/main/java/com/unipay/pay/ctrl/refund/RefundOrderController.java#L47-L157)

## 异步通知

### 机制说明
当退款在支付渠道侧的状态发生变更（成功或失败）时，系统会向商户预先设置的`notifyUrl`发送异步HTTP POST通知。

### 通知内容
通知的请求体为标准的退款结果数据，与API响应的`data`部分相同，并包含`sign`签名。

### 通知保障
- **幂等性**：通过`MchNotifyRecord`表确保同一退款订单不会重复发送通知。
- **重试机制**：通知失败后，系统会通过MQ任务进行多次重试，直至成功或达到最大重试次数。
- **状态跟踪**：每次通知尝试都会记录在`mch_notify_record`表中，便于排查问题。

```mermaid
sequenceDiagram
    participant 支付渠道
    participant 支付系统
    participant