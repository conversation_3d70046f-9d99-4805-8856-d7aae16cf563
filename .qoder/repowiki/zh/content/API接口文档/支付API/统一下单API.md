# 统一下单API

<cite>
**本文档中引用的文件**  
- [UnifiedOrderController.java](file://sys-payment/src/main/java/com/unipay/pay/ctrl/payorder/UnifiedOrderController.java)
- [UnifiedOrderRQ.java](file://sys-payment/src/main/java/com/unipay/pay/rqrs/payorder/UnifiedOrderRQ.java)
- [UnifiedOrderRS.java](file://sys-payment/src/main/java/com/unipay/pay/rqrs/payorder/UnifiedOrderRS.java)
- [CS.java](file://core/src/main/java/com/unipay/core/constants/CS.java)
- [AliBarOrderRQ.java](file://sys-payment/src/main/java/com/unipay/pay/rqrs/payorder/payway/AliBarOrderRQ.java)
</cite>

## 目录
1. [简介](#简介)
2. [接口概览](#接口概览)
3. [请求参数](#请求参数)
4. [响应参数](#响应参数)
5. [请求/响应示例](#请求响应示例)
6. [签名生成算法](#签名生成算法)
7. [异步通知机制](#异步通知机制)
8. [订单状态流转与超时处理](#订单状态流转与超时处理)
9. [错误码列表](#错误码列表)
10. [客户端调用示例](#客户端调用示例)

## 简介
统一下单API是支付系统的核心接口，用于创建支付订单。该接口支持多种支付方式（如支付宝、微信、银联等），通过统一的请求结构和响应格式，简化商户接入流程。系统根据支付方式自动路由到对应的支付渠道，并返回相应的支付参数。

## 接口概览
统一下单接口通过HTTP POST方法调用，提供标准化的请求体和响应体结构，确保跨平台兼容性。

### 基本信息
- **HTTP方法**: POST
- **URL路径**: `/api/pay/unifiedOrder`
- **认证方式**: 商户签名（基于商户号和应用密钥）
- **内容类型**: `application/json`

### 认证头
请求需在参数中包含商户身份信息，而非HTTP头。关键身份字段如下：
- `mchNo`: 商户号
- `appId`: 商户应用ID
- 请求签名通过请求体参数生成，详见“签名生成算法”章节。

**Section sources**
- [UnifiedOrderController.java](file://sys-payment/src/main/java/com/unipay/pay/ctrl/payorder/UnifiedOrderController.java#L15-L25)

## 请求参数
请求体为JSON格式，基于`UnifiedOrderRQ`类定义，包含基础参数和特定渠道扩展参数。

### 基础参数
| 参数名 | 类型 | 必填 | 描述 |
| :--- | :--- | :--- | :--- |
| `mchNo` | String | 是 | 商户号，标识发起请求的商户 |
| `appId` | String | 是 | 商户应用ID，用于鉴权和配置查询 |
| `mchOrderNo` | String | 是 | 商户订单号，需保证在商户系统内唯一 |
| `wayCode` | String | 是 | 支付方式代码，决定支付流程和参数结构 |
| `amount` | Long | 是 | 支付金额，单位：分（例如100代表1元） |
| `currency` | String | 是 | 货币代码，如CNY |
| `clientIp` | String | 否 | 客户端IP地址，用于风控 |
| `subject` | String | 是 | 商品标题，显示在支付页面 |
| `body` | String | 是 | 商品描述信息 |
| `notifyUrl` | String | 否 | 异步通知地址，支付结果将通过POST回调此URL |
| `returnUrl` | String | 否 | 跳转通知地址，支付完成后浏览器重定向至此 |
| `expiredTime` | Integer | 否 | 订单失效时间，单位：秒。默认值由系统配置决定 |
| `channelExtra` | String | 否 | 特定渠道发起额外参数，JSON字符串格式 |
| `extParam` | String | 否 | 商户扩展参数，原样返回，可用于关联业务数据 |
| `divisionMode` | Byte | 否 | 分账模式：0-不分账，1-自动分账，2-手动分账 |

### 支付方式代码 (wayCode)
`wayCode` 参数决定了具体的支付流程和`channelExtra`中的预期结构。常见值包括：
- **支付宝**: `ALI_BAR` (条码), `ALI_JSAPI` (服务窗), `ALI_APP` (App), `ALI_WAP` (Wap), `ALI_PC` (电脑网站)
- **微信**: `WX_BAR` (条码), `WX_JSAPI` (JSAPI), `WX_LITE` (小程序), `WX_NATIVE` (扫码), `WX_H5` (H5)
- **银联**: `UP_BAR` (被扫), `UP_QR` (主扫), `UP_APP` (App), `UP_PC` (网关)
- **云闪付**: `YSF_BAR` (条码), `YSF_JSAPI` (服务窗)
- **特殊**: `QR_CASHIER` (二维码收银台聚合), `AUTO_BAR` (条码自动分类)

### 特定渠道参数 (channelExtra)
当`wayCode`指定具体支付方式时，`channelExtra`应包含该方式所需的额外参数，以JSON格式传入。例如：
- **支付宝条码支付 (ALI_BAR)**: 需包含 `authCode` (用户支付条码)。
- **微信JSAPI支付 (WX_JSAPI)**: 需包含 `subOpenid` (用户在商户公众号下的OpenID)。

**Section sources**
- [UnifiedOrderRQ.java](file://sys-payment/src/main/java/com/unipay/pay/rqrs/payorder/UnifiedOrderRQ.java#L15-L70)
- [CS.java](file://core/src/main/java/com/unipay/core/constants/CS.java#L150-L180)

## 响应参数
响应体为JSON格式，基于`UnifiedOrderRS`类定义，包含订单信息和支付引导数据。

### 基础响应结构
| 参数名 | 类型 | 描述 |
| :--- | :--- | :--- |
| `payOrderId` | String | 系统生成的支付订单号，全局唯一 |
| `mchOrderNo` | String | 对应的商户订单号 |
| `orderState` | Byte | 订单状态码：1-初始化，2-支付中，3-成功，4-失败，5-已关闭 |
| `payDataType` | String | 支付参数类型，指示前端如何处理 `payData` |
| `payData` | String | 支付参数，具体内容取决于 `payDataType` |
| `errCode` | String | 渠道返回错误代码（仅当发生错误时存在） |
| `errMsg` | String | 渠道返回错误信息（仅当发生错误时存在） |
| `code` | Integer | 响应状态码，0表示成功 |
| `msg` | String | 响应消息 |
| `sign` | String | 响应签名，用于验证数据完整性 |

### 支付参数类型 (payDataType)
`payDataType` 决定了 `payData` 字段的使用方式：
- `none`: 无支付参数，订单可能已直接成功或失败。
- `payurl`: `payData` 为一个URL，前端应重定向至此URL完成支付。
- `form`: `payData` 为一个HTML表单，前端需自动提交此表单。
- `wxapp`: `payData` 为微信App支付所需的参数包，需调用 `wx.requestPayment()`。
- `aliapp`: `payData` 为支付宝App支付所需的参数，需调用支付宝SDK。
- `codeUrl`: `payData` 为一个二维码内容，前端需生成二维码供用户扫描。
- `codeImgUrl`: `payData` 为一个二维码图片的URL，可直接在页面上显示。

**Section sources**
- [UnifiedOrderRS.java](file://sys-payment/src/main/java/com/unipay/pay/rqrs/payorder/UnifiedOrderRS.java#L15-L35)
- [CS.java](file://core/src/main/java/com/unipay/core/constants/CS.java#L182-L195)

## 请求/响应示例
以下示例展示了不同支付方式的典型请求和响应。

### 示例1：支付宝条码支付 (ALI_BAR)
**请求**
```json
{
  "mchNo": "MCH123456",
  "appId": "APP789012",
  "mchOrderNo": "ORD20231101001",
  "wayCode": "ALI_BAR",
  "amount": 100,
  "currency": "CNY",
  "subject": "测试商品",
  "body": "测试商品描述",
  "channelExtra": "{\"authCode\": \"287451236548795123\"}",
  "sign": "..."
}
```

**响应 (支付成功)**
```json
{
  "code": 0,
  "msg": "success",
  "data": {
    "payOrderId": "P20231101000001",
    "mchOrderNo": "ORD20231101001",
    "orderState": 3,
    "payDataType": "none",
    "payData": ""
  },
  "sign": "..."
}
```

### 示例2：微信扫码支付 (WX_NATIVE)
**请求**
```json
{
  "mchNo": "MCH123456",
  "appId": "APP789012",
  "mchOrderNo": "ORD20231101002",
  "wayCode": "WX_NATIVE",
  "amount": 200,
  "currency": "CNY",
  "subject": "扫码支付",
  "body": "扫码支付测试",
  "expiredTime": 300,
  "sign": "..."
}
```

**响应 (等待支付)**
```json
{
  "code": 0,
  "msg": "success",
  "data": {
    "payOrderId": "P20231101000002",
    "mchOrderNo": "ORD20231101002",
    "orderState": 2,
    "payDataType": "codeUrl",
    "payData": "weixin://wxpay/bizpayurl?pr=xxxxx"
  },
  "sign": "..."
}
```

### 示例3：银联App支付 (UP_APP)
**请求**
```json
{
  "mchNo": "MCH123456",
  "appId": "APP789012",
  "mchOrderNo": "ORD20231101003",
  "wayCode": "UP_APP",
  "amount": 300,
  "currency": "CNY",
  "subject": "App支付",
  "body": "App支付测试",
  "sign": "..."
}
```

**响应**
```json
{
  "code": 0,
  "msg": "success",
  "data": {
    "payOrderId": "P20231101000003",
    "mchOrderNo": "ORD20231101003",
    "orderState": 2,
    "payDataType": "form",
    "payData": "<form>...</form>"
  },
  "sign": "..."
}
```

**Section sources**
- [UnifiedOrderController.java](file://sys-payment/src/main/java/com/unipay/pay/ctrl/payorder/UnifiedOrderController.java#L27-L55)
- [UnifiedOrderRQ.java](file://sys-payment/src/main/java/com/unipay/pay/rqrs/payorder/UnifiedOrderRQ.java#L74-L151)
- [UnifiedOrderRS.java](file://sys-payment/src/main/java/com/unipay/pay/rqrs/payorder/UnifiedOrderRS.java#L42-L49)

## 签名生成算法
为确保请求的完整性和真实性，所有请求必须进行签名。签名算法基于商户的应用密钥（`appSecret`）。

### 签名步骤
1.  **参数排序**: 将请求体中的所有非空参数（不包括`sign`本身）按键名进行字典序升序排列。
2.  **拼接字符串**: 将排序后的参数按 `key=value` 的形式连接，中间用 `&` 符号分隔，形成待签名字符串。
3.  **添加密钥**: 在待签名字符串末尾追加 `&key=应用密钥`。
4.  **生成签名**: 对最终字符串使用MD5算法进行哈希计算，并将结果转换为大写十六进制字符串。

### 示例
假设请求参数为：
```json
{
  "mchNo": "MCH123456",
  "appId": "APP789012",
  "mchOrderNo": "ORD20231101001",
  "wayCode": "ALI_BAR",
  "amount": 100,
  "currency": "CNY",
  "subject": "测试商品",
  "body": "测试商品描述"
}
```
且应用密钥为 `myAppSecretKey`。

1.  排序后参数：`amount=100&appId=APP789012&body=测试商品描述&currency=CNY&mchNo=MCH123456&mchOrderNo=ORD20231101001&subject=测试商品&wayCode=ALI_BAR`
2.  拼接密钥：`amount=100&appId=APP789012&body=测试商品描述&currency=CNY&mchNo=MCH123456&mchOrderNo=ORD20231101001&subject=测试商品&wayCode=ALI_BAR&key=myAppSecretKey`
3.  MD5计算：`sign=5F4DCC3B5AA765D61D8327DEB882CF99` (示例值)

最终请求体包含 `sign` 字段。

**Section sources**
- [UnifiedOrderController.java](file://sys-payment/src/main/java/com/unipay/pay/ctrl/payorder/UnifiedOrderController.java#L27-L28)
- [JeepayKit.java](file://core/src/main/java/com/unipay/core/utils/JeepayKit.java) (推断存在，用于实际签名逻辑)

## 异步通知机制
支付结果通过异步HTTP POST请求通知商户系统，确保结果的最终一致性。

### 通知流程
1.  支付平台在收到渠道的支付结果后，会向商户在`notifyUrl`参数中指定的地址发送POST请求。
2.  请求体为支付结果的JSON数据，结构与统一下单的响应体类似。
3.  商户系统必须在接收到通知后，进行**签名验证**，以确认通知来源的合法性。
4.  验证通过后，商户系统应更新本地订单状态，并返回一个特定的响应字符串 `{"code": "SUCCESS", "msg": "成功"}`。
5.  如果商户返回非成功响应或超时，支付平台会按照一定策略（如指数退避）进行多次重试，直到收到成功确认。

### 重要提示
- **幂等性处理**: 由于网络原因，商户可能收到重复的通知。业务逻辑必须设计为幂等的，即多次处理同一笔订单的通知不会产生副作用。
- **安全验证**: 必须验证通知的签名，防止伪造。
- **快速响应**: 应尽快返回成功响应，避免触发不必要的重试。

**Section sources**
- [PayOrderMchNotifyMQ.java](file://components/components-mq/src/main/java/com/unipay/components/mq/model/PayOrderMchNotifyMQ.java) (推断存在，用于触发通知)
- [PayMchNotifyService.java](file://sys-payment/src/main/java/com/unipay/pay/service/PayMchNotifyService.java) (推断存在，用于执行通知)

## 订单状态流转与超时处理
订单状态反映了支付的生命周期，系统会自动处理超时订单。

### 状态流转图
```mermaid
stateDiagram-v2
[*] --> 初始化
初始化 --> 支付中 : 用户开始支付
支付中 --> 支付成功 : 支付渠道返回成功
支付中 --> 支付失败 : 支付渠道返回失败
支付中 --> 已关闭 : 订单超时或被用户取消
支付成功 --> [*]
支付失败 --> [*]
已关闭 --> [*]
```

**Diagram sources**
- [PayOrder.java](file://core/src/main/java/com/unipay/core/entity/PayOrder.java) (推断存在，定义状态常量)
- [PayOrderExpiredTask.java](file://sys-payment/src/main/java/com/unipay/pay/task/PayOrderExpiredTask.java) (推断存在，处理超时任务)

### 状态说明
- **初始化 (1)**: 订单创建成功，等待支付。
- **支付中 (2)**: 用户已发起支付，等待渠道结果。
- **支付成功 (3)**: 支付已成功，资金已结算。
- **支付失败 (4)**: 支付明确失败，如余额不足。
- **已关闭 (5)**: 订单因超时或主动取消而关闭。

### 超时处理
- 订单的超时时间由 `expiredTime` 参数或系统默认配置决定。
- 当订单处于“初始化”或“支付中”状态且超过设定时间后，系统后台任务会将其状态自动更新为“已关闭”。
- 超时后，该订单号不可再用于支付。

**Section sources**
- [PayOrder.java](file://core/src/main/java/com/unipay/core/entity/PayOrder.java#L10-L20)
- [PayOrderExpiredTask.java](file://sys-payment/src/main/java/com/unipay/pay/task/PayOrderExpiredTask.java#L1-L20)

## 错误码列表
| 错误码 | 描述 | 解决方案 |
| :--- | :--- | :--- |
| `API_00001` | 签名验证失败 | 检查 `sign` 参数生成逻辑，确认应用密钥正确 |
| `API_00002` | 商户号或应用ID无效 | 确认 `mchNo` 和 `appId` 是否正确且已激活 |
| `API_00003` | 不支持的支付方式 | 检查 `wayCode` 是否在支持列表中，或确认商户是否已开通该支付方式 |
| `API_00004` | 商户订单号重复 | 确保 `mchOrderNo` 在商户系统内全局唯一 |
| `API_00005` | 必填参数为空 | 检查请求体，确保所有标记为“必填”的参数都已提供 |
| `PAY_10001` | 支付渠道返回余额不足 | 提示用户更换支付方式或充值 |
| `PAY_10002` | 支付渠道返回交易超时 | 建议用户重新发起支付 |
| `SYS_99999` | 系统内部错误 | 记录请求信息，联系技术支持 |

**Section sources**
- [ApiCodeEnum.java](file://core/src/main/java/com/unipay/core/constants/ApiCodeEnum.java) (推断存在，定义错误码)

## 客户端调用示例
### Java示例
```java
// 1. 构建请求对象
UnifiedOrderRQ request = new UnifiedOrderRQ();
request.setMchNo("MCH123456");
request.setAppId("APP789012");
request.setMchOrderNo("ORD" + System.currentTimeMillis());
request.setWayCode("WX_NATIVE");
request.setAmount(100L);
request.setCurrency("CNY");
request.setSubject("Java测试商品");
request.setBody("Java测试描述");

// 2. 生成签名 (假设使用JeepayKit工具类)
String sign = JeepayKit.getSign(request, "myAppSecretKey");
request.setSign(sign);

// 3. 发送HTTP请求 (使用OkHttp示例)
OkHttpClient client = new OkHttpClient();
RequestBody body = RequestBody.create(MediaType.get("application/json; charset=utf-8"), 
                                     JSON.toJSONString(request));
Request httpRequest = new Request.Builder()
    .url("https://api.yourdomain.com/api/pay/unifiedOrder")
    .post(body)
    .build();

Response response = client.newCall(httpRequest).execute();
String responseBody = response.body().string();
// 4. 解析响应并处理
```

### Python示例
```python
import requests
import hashlib
import json

# 1. 构建请求数据
data = {
    "mchNo": "MCH123456",
    "appId": "APP789012",
    "mchOrderNo": f"ORD{int(time.time() * 1000)}",
    "wayCode": "ALI_BAR",
    "amount": 100,
    "currency": "CNY",
    "subject": "Python测试商品",
    "body": "Python测试描述",
    "channelExtra": json.dumps({"authCode": "287451236548795123"})
}

# 2. 生成签名
def generate_sign(params, app_secret):
    sorted_params = sorted(params.items(), key=lambda x: x[0])
    string_to_sign = '&'.join([f"{k}={v}" for k, v in sorted_params if v is not None])
    string_to_sign += f"&key={app_secret}"
    return hashlib.md5(string_to_sign.encode('utf-8')).hexdigest().upper()

data['sign'] = generate_sign(data, 'myAppSecretKey')

# 3. 发送请求
url = "https://api.yourdomain.com/api/pay/unifiedOrder"
headers = {'Content-Type': 'application/json'}
response = requests.post(url, json=data, headers=headers)

# 4. 处理响应
if response.status_code == 200:
    result = response.json()
    print(result)
else:
    print(f"Request failed with status {response.status_code}")
```

**Section sources**
- [1-支付请求实现.java](file://代码示例/1-支付请求实现.java)
- [4-签名验证工具类.java](file://代码示例/4-签名验证工具类.java)