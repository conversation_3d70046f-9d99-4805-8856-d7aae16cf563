
# 退款API

<cite>
**本文档引用的文件**
- [RefundOrderController.java](file://sys-payment/src/main/java/com/unipay/pay/ctrl/refund/RefundOrderController.java)
- [RefundOrderService.java](file://service/src/main/java/com/unipay/service/impl/RefundOrderService.java)
- [RefundOrder.java](file://core/src/main/java/com/unipay/core/entity/RefundOrder.java)
- [RefundOrderRQ.java](file://sys-payment/src/main/java/com/unipay/pay/rqrs/refund/RefundOrderRQ.java)
- [RefundOrderRS.java](file://sys-payment/src/main/java/com/unipay/pay/rqrs/refund/RefundOrderRS.java)
- [IRefundService.java](file://sys-payment/src/main/java/com/unipay/pay/channel/IRefundService.java)
- [PayMchNotifyService.java](file://sys-payment/src/main/java/com/unipay/pay/service/PayMchNotifyService.java)
- [PayOrder.java](file://core/src/main/java/com/unipay/core/entity/PayOrder.java)
- [RefundOrderMapper.java](file://service/src/main/java/com/unipay/service/mapper/RefundOrderMapper.java)
- [RefundOrderMapper.xml](file://service/src/main/java/com/unipay/service/mapper/RefundOrderMapper.xml)
- [PayOrderMapper.xml](file://service/src/main/java/com/unipay/service/mapper/PayOrderMapper.xml)
- [SeqKit.java](file://core/src/main/java/com/unipay/core/utils/SeqKit.java)
- [RefundOrderProcessService.java](file://sys-payment/src/main/java/com/unipay/pay/service/RefundOrderProcessService.java)
- [ChannelRetMsg.java](file://sys-payment/src/main/java/com/unipay/pay/rqrs/msg/ChannelRetMsg.java)
- [RefundOrderExpiredTask.java](file://sys-payment/src/main/java/com/unipay/pay/task/RefundOrderExpiredTask.java)
</cite>

## 目录
1. [接口概述](#接口概述)
2. [请求参数说明](#请求参数说明)
3. [响应参数说明](#响应参数说明)
4. [全额退款与部分退款实现差异](#全额退款与部分退款实现差异)
5. [资金退回路径](#资金退回路径)
6. [手续费处理规则](#手续费处理规则)
7. [退款单号生成策略](#退款单号生成策略)
8. [退款状态机](#退款状态机)
9. [银行处理延迟](#银行处理延迟)
10. [与支付渠道的对账机制](#与支付渠道的对账机制)
11. [退款结果异步通知配置](#退款结果异步通知配置)
12. [退款失败常见原因分析](#退款失败常见原因分析)
13. [补偿事务处理方案](#补偿事务处理方案)

## 接口概述

`POST /api/pay/refund` 接口用于处理商户发起的退款请求。该接口支持全额退款和部分退款，通过验证支付订单状态、可退款余额、商户权限等条件后，调用相应的支付渠道退款服务完成资金退回操作。接口采用签名验证机制确保请求安全性，并通过异步通知方式将退款结果推送给商户系统。

**Section sources**
- [RefundOrderController.java](file://sys-payment/src/main/java/com/unipay/pay/ctrl/refund/RefundOrderController.java#L36-L256)

## 请求参数说明

| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| mchNo | String | 是 | 商户号 |
| appId | String | 是 | 应用ID |
| mchOrderNo | String | 否 | 商户订单号（与payOrderId二选一） |
| payOrderId | String | 否 | 支付系统订单号（与mchOrderNo二选一） |
| mchRefundNo | String | 是 | 商户系统生成的退款单号 |
| refundAmount | Long | 是 | 退款金额，单位：分 |
| currency | String | 是 | 货币代码，如cny |
| refundReason | String | 是 | 退款原因 |
| clientIp | String | 否 | 客户端IP地址 |
| notifyUrl | String | 否 | 异步通知地址 |
| channelExtra | String | 否 | 特定渠道发起额外参数 |
| extParam | String | 否 | 商户扩展参数 |

**Section sources**
- [RefundOrderRQ.java](file://sys-payment/src/main/java/com/unipay/pay/rqrs/refund/RefundOrderRQ.java#L14-L52)

## 响应参数说明

| 参数名 | 类型 | 描述 |
|--------|------|------|
| refundOrderId | String | 支付系统退款订单号 |
| mchRefundNo | String | 商户发起的退款订单号 |
| payAmount | Long | 订单支付金额，单位：分 |
| refundAmount | Long | 申请退款金额，单位：分 |
| state | Byte | 退款状态：0-订单生成, 1-退款中, 2-退款成功, 3-退款失败 |
| channelOrderNo | String | 渠道退款单号 |
| errCode | String | 渠道返回错误代码 |
| errMsg | String | 渠道返回错误信息 |

**Section sources**
- [RefundOrderRS.java](file://sys-payment/src/main/java/com/unipay/pay/rqrs/refund/RefundOrderRS.java#L12-L53)

## 全额退款与部分退款实现差异

全额退款与部分退款在业务逻辑上存在显著差异：

1. **状态判断**：系统首先检查支付订单的退款状态（`refundState`），若已为全额退款（`REFUND_STATE_ALL`）则拒绝新的退款请求。

2. **金额校验**：
   - 部分退款：验证本次退款金额加上已退款金额不超过订单总金额。
   - 全额退款：当本次退款后累计退款金额等于订单金额时，更新支付订单状态为"已退款"。

3. **数据库更新**：
   - 部分退款：更新`refund_amount`和`refund_times`，`refund_state`设为`REFUND_STATE_SUB`。
   - 全额退款：除上述字段外，还将`state`设为`STATE_REFUND`。

4. **事务处理**：使用`@Transactional`注解确保退款订单和支付订单的更新操作原子性。

```mermaid
flowchart TD
Start([开始]) --> ValidateOrder["验证支付订单状态"]
ValidateOrder --> CheckRefundState{"是否已全额退款?"}
CheckRefundState --> |是| ReturnFail["返回失败"]
CheckRefundState --> |否| CheckAmount["检查可退款余额"]
CheckAmount --> CalculateTotal["计算累计退款金额"]
CalculateTotal --> IsFullRefund{"是否为全额退款?"}
IsFullRefund --> |是| UpdateFull["更新为全额退款状态"]
IsFullRefund --> |否| UpdatePartial["更新为部分退款状态"]
UpdateFull --> End([结束])
UpdatePartial --> End
```

**Diagram sources**
- [RefundOrderController.java](file://sys-payment/src/main/java/com/unipay/pay/ctrl/refund/RefundOrderController.java#L47-L157)
- [RefundOrderService.java](file://service/src/main/java/com/unipay/service/impl/RefundOrderService.java#L58-L81)
- [PayOrderMapper.xml](file://service/src/main/java/com/unipay/service/mapper/PayOrderMapper.xml#L100-L115)

**Section sources**
- [RefundOrderController.java](file://sys-payment/src/main/java/com/unipay/pay/ctrl/refund/RefundOrderController.java#L47-L157)
- [RefundOrderService.java](file://service/src/main/java/com/unipay/service/impl/RefundOrderService.java#L58-L81)
- [PayOrderMapper.xml](file://service/src/main/java/com/unipay/service/mapper/PayOrderMapper.xml#L100-L115)

## 资金退回路径

资金退回路径遵循以下流程：

1. **商户请求**：商户系统调用`POST /api/pay/refund`接口发起退款请求。
2. **系统验证**：统一支付平台验证请求参数、订单状态和可退款余额。
3. **渠道调用**：根据支付订单的`ifCode`获取对应的`IRefundService`实现，调用渠道退款接口。
4. **资金退回**：支付渠道处理退款请求，将资金从商户账户退回至用户原支付账户。
5. **状态更新**：渠道返回结果后，更新退款