# 订单查询API

<cite>
**本文档引用的文件**
- [QueryOrderController.java](file://sys-payment/src/main/java/com/unipay/pay/ctrl/payorder/QueryOrderController.java)
- [QueryPayOrderRQ.java](file://sys-payment/src/main/java/com/unipay/pay/rqrs/payorder/QueryPayOrderRQ.java)
- [QueryPayOrderRS.java](file://sys-payment/src/main/java/com/unipay/pay/rqrs/payorder/QueryPayOrderRS.java)
- [PayOrderService.java](file://service/src/main/java/com/unipay/service/impl/PayOrderService.java)
- [ApiController.java](file://sys-payment/src/main/java/com/unipay/pay/ctrl/ApiController.java)
- [AbstractCtrl.java](file://core/src/main/java/com/unipay/core/ctrls/AbstractCtrl.java)
- [JeepayKit.java](file://core/src/main/java/com/unipay/core/utils/JeepayKit.java)
- [CS.java](file://core/src/main/java/com/unipay/core/constants/CS.java)
- [ApiCodeEnum.java](file://core/src/main/java/com/unipay/core/constants/ApiCodeEnum.java)
- [ApiRes.java](file://core/src/main/java/com/unipay/core/model/ApiRes.java)
</cite>

## 目录
1. [简介](#简介)
2. [请求参数](#请求参数)
3. [认证方式](#认证方式)
4. [响应数据结构](#响应数据结构)
5. [查询频率限制](#查询频率限制)
6. [缓存策略](#缓存策略)
7. [最终一致性保证](#最终一致性保证)
8. [响应示例](#响应示例)
9. [与支付网关的交互流程](#与支付网关的交互流程)
10. [重试策略建议](#重试策略建议)

## 简介
`GET /api/pay/queryOrder` 接口用于查询支付订单的状态和详细信息。该接口允许商户通过商户订单号（mchOrderNo）或支付系统订单号（payOrderId）来查询订单的当前状态。系统支持灵活的查询方式，确保商户能够准确获取订单信息，同时通过签名验证机制保障接口调用的安全性。

**Section sources**
- [QueryOrderController.java](file://sys-payment/src/main/java/com/unipay/pay/ctrl/payorder/QueryOrderController.java#L21-L50)

## 请求参数
该接口接受以下请求参数：

| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| mchNo | String | 是 | 商户号，用于标识发起请求的商户 |
| appId | String | 是 | 商户应用ID，用于标识商户的具体应用 |
| mchOrderNo | String | 否 | 商户订单号，商户系统内生成的唯一订单编号 |
| payOrderId | String | 否 | 支付系统订单号，支付平台生成的唯一订单编号 |
| sign | String | 是 | 签名值，用于验证请求的完整性和来源 |

**注意**：`mchOrderNo` 和 `payOrderId` 不能同时为空，至少需要提供其中一个参数来定位订单。

**Section sources**
- [QueryPayOrderRQ.java](file://sys-payment/src/main/java/com/unipay/pay/rqrs/payorder/QueryPayOrderRQ.java#L15-L22)

## 认证方式
接口采用基于商户密钥的签名认证机制，确保请求的安全性和合法性。

1. **签名生成**：商户需要使用其 `appSecret` 对请求参数进行签名。签名算法采用 MD5，按照参数名的字典序排序后拼接，最后附加 `key=appSecret` 进行加密。
2. **验签流程**：服务端接收到请求后，会使用相同的算法和商户的 `appSecret` 重新计算签名，并与请求中的 `sign` 参数进行比对。如果签名不匹配，则返回验签失败错误。
3. **商户状态校验**：系统会检查商户号和应用ID的有效性，以及商户和应用的状态是否为可用状态（CS.YES）。

```mermaid
sequenceDiagram
participant 商户 as 商户系统
participant API as API接口
participant 验签服务 as 验签服务
participant 商户配置服务 as 商户配置服务
商户->>API : 发送查询请求(mchNo, appId, sign)
API->>商户配置服务 : 查询商户和应用信息
商户配置服务-->>API : 返回商户配置上下文
API->>验签服务 : 使用appSecret重新计算签名
验签服务-->>API : 返回验签结果
alt 验签成功
API->>API : 继续订单查询流程
else 验签失败
API->>商户 : 返回验签失败错误
end
```

**Diagram sources**
- [ApiController.java](file://sys-payment/src/main/java/com/unipay/pay/ctrl/ApiController.java#L30-L88)
- [JeepayKit.java](file://core/src/main/java/com/unipay/core/utils/JeepayKit.java#L45-L75)

## 响应数据结构
查询成功时，接口返回 `QueryPayOrderRS` 对象，包含订单的详细信息。

| 字段名 | 类型 | 描述 |
|--------|------|------|
| payOrderId | String | 支付系统订单号 |
| mchNo | String | 商户号 |
| appId | String | 商户应用ID |
| mchOrderNo | String | 商户订单号 |
| ifCode | String | 支付接口代码（如：alipay, wxpay） |
| wayCode | String | 支付方式代码（如：ALI_BAR, WX_JSAPI） |
| amount | Long | 支付金额，单位为分 |
| currency | String | 三位货币代码，人民币为cny |
| state | Byte | 支付状态：0-订单生成, 1-支付中, 2-支付成功, 3-支付失败, 4-已撤销, 5-已退款, 6-订单关闭 |
| clientIp | String | 客户端IP |
| subject | String | 商品标题 |
| body | String | 商品描述信息 |
| channelOrderNo | String | 渠道订单号 |
| errCode | String | 渠道支付错误码 |
| errMsg | String | 渠道支付错误描述 |
| extParam | String | 商户扩展参数 |
| successTime | Long | 订单支付成功时间（时间戳） |
| createdAt | Long | 订单创建时间（时间戳） |
| sign | String | 响应签名，用于验证响应的完整性和来源 |

**Section sources**
- [QueryPayOrderRS.java](file://sys-payment/src/main/java/com/unipay/pay/rqrs/payorder/QueryPayOrderRS.java#L15-L122)
- [ApiRes.java](file://core/src/main/java/com/unipay/core/model/ApiRes.java#L45-L65)

## 查询频率限制
系统对订单查询接口实施了频率限制策略，以防止滥用和保护系统资源。

- **限制规则**：每个商户应用（appId）每分钟最多允许查询 120 次。
- **实现机制**：频率限制通过 Redis 缓存实现，以 `mchNo:appId:queryOrder` 作为缓存键，记录每分钟的查询次数。
- **超限处理**：当查询频率超过限制时，接口将返回 `SYSTEM_ERROR` 错误码（10），并提示“系统异常[请求过于频繁]”。

**Section sources**
- [ApiController.java](file://sys-payment/src/main/java/com/unipay/pay/ctrl/ApiController.java#L30-L88)

## 缓存策略
为了提高查询性能，系统采用了多级缓存策略。

1. **Redis 缓存**：支付订单的核心信息（如订单状态、金额等）会被缓存在 Redis 中，缓存键为 `payOrder:payOrderId` 或 `payOrder:mchNo:mchOrderNo`。
2. **缓存时效**：订单信息在 Redis 中的缓存时间为 5 分钟。对于已关闭或已退款的订单，缓存时间会延长至 24 小时。
3. **缓存更新**：当订单状态发生变更（如支付成功、退款完成）时，系统会通过消息队列（MQ）通知缓存服务，立即更新或删除相应的缓存条目。

```mermaid
flowchart TD
A[查询请求] --> B{Redis中存在?}
B --> |是| C[返回缓存数据]
B --> |否| D[查询数据库]
D --> E{数据库中存在?}
E --> |是| F[更新Redis缓存]
F --> G[返回数据库数据]
E --> |否| H[返回订单不存在]
```

**Diagram sources**
- [PayOrderService.java](file://service/src/main/java/com/unipay/service/impl/PayOrderService.java#L127-L136)
- [QueryOrderController.java](file://sys-payment/src/main/java/com/unipay/pay/ctrl/payorder/QueryOrderController.java#L21-L50)

## 最终一致性保证
由于系统采用异步处理模式，订单状态的更新可能存在短暂的延迟，系统通过以下机制保证最终一致性：

1. **异步通知**：支付网关在支付完成后，会通过异步回调通知支付系统更新订单状态。
2. **状态轮询**：对于长时间处于“支付中”状态的订单，系统后台任务会主动向支付网关发起状态查询，确保订单状态的最终一致性。
3. **消息队列**：订单状态变更事件通过消息队列（MQ）进行广播，确保所有相关服务（如通知服务、分账服务）都能及时收到更新。

**Section sources**
- [PayOrderService.java](file://service/src/main/java/com/unipay/service/impl/PayOrderService.java#L127-L136)
- [QueryOrderController.java](file://sys-payment/src/main/java/com/unipay/pay/ctrl/payorder/QueryOrderController.java#L21-L50)

## 响应示例

### 成功响应示例
```json
{
  "code": 0,
  "msg": "SUCCESS",
  "data": {
    "payOrderId": "P202310011234567890",
    "mchNo": "M20231001",
    "appId": "A20231001",
    "mchOrderNo": "T202310010001",
    "ifCode": "alipay",
    "wayCode": "ALI_QR",
    "amount": 10000,
    "currency": "cny",
    "state": 2,
    "clientIp": "***********",
    "subject": "测试商品",
    "body": "测试商品描述",
    "channelOrderNo": "2023100123456789",
    "errCode": "",
    "errMsg": "",
    "extParam": "",
    "successTime": 1696123456000,
    "createdAt": 1696123400000
  },
  "sign": "ABCDEF1234567890"
}
```

### 失败响应示例 - 订单不存在
```json
{
  "code": 9999,
  "msg": "订单不存在",
  "data": null,
  "sign": null
}
```

### 失败响应示例 - 系统错误
```json
{
  "code": 10,
  "msg": "系统异常[数据库服务异常]",
  "data": null,
  "sign": null
}
```

### 失败响应示例 - 验签失败
```json
{
  "code": 9999,
  "msg": "验签失败",
  "data": null,
  "sign": null
}
```

**Section sources**
- [ApiCodeEnum.java](file://core/src/main/java/com/unipay/core/constants/ApiCodeEnum.java#L10-L41)
- [ApiRes.java](file://core/src/main/java/com/unipay/core/model/ApiRes.java#L45-L65)

## 与支付网关的交互流程
订单查询接口主要与支付网关进行间接交互，通过查询本地数据库来获取订单信息。当本地数据与支付网关状态不一致时，系统会主动发起状态同步。

```mermaid
sequenceDiagram
participant 商户 as 商户系统
participant 支付系统 as 支付系统
participant 支付网关 as 支付网关
商户->>支付系统 : GET /api/pay/queryOrder
支付系统->>支付系统 : 验签 & 参数校验
支付系统->>支付系统 : 查询本地数据库
alt 本地数据存在
支付系统-->>商户 : 返回订单信息
else 本地数据不存在或状态可疑
支付系统->>支付网关 : 主动查询订单状态
支付网关-->>支付系统 : 返回最新状态
支付系统->>支付系统 : 更新本地数据库和缓存
支付系统-->>商户 : 返回最新订单信息
end
```

**Diagram sources**
- [QueryOrderController.java](file://sys-payment/src/main/java/com/unipay/pay/ctrl/payorder/QueryOrderController.java#L21-L50)
- [PayOrderService.java](file://service/src/main/java/com/unipay/service/impl/PayOrderService.java#L127-L136)

## 重试策略建议
当查询接口返回临时性错误（如网络超时、系统繁忙）时，建议商户采用以下重试策略：

1. **指数退避**：首次失败后等待 1 秒重试，第二次失败后等待 2 秒，第三次等待 4 秒，以此类推，最大重试间隔不超过 30 秒。
2. **最大重试次数**：建议最多重试 3 次。如果 3 次重试均失败，则应记录错误日志并人工介入处理。
3. **避免在短时间内频繁重试**：遵循系统的频率限制，避免因重试导致请求被限流。
4. **状态判断**：如果查询到订单状态为“支付成功”、“支付失败”、“已退款”等终态，则无需重试，应以查询到的状态为准。

**Section sources**
- [QueryOrderController.java](file://sys-payment/src/main/java/com/unipay/pay/ctrl/payorder/QueryOrderController.java#L21-L50)
- [ApiCodeEnum.java](file://core/src/main/java/com/unipay/core/constants/ApiCodeEnum.java#L10-L41)