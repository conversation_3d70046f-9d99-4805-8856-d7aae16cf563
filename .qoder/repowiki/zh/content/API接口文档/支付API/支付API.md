# 支付API

<cite>
**本文档中引用的文件**  
- [UnifiedOrderRQ.java](file://sys-payment/src/main/java/com/unipay/pay/rqrs/payorder/UnifiedOrderRQ.java)
- [UnifiedOrderRS.java](file://sys-payment/src/main/java/com/unipay/pay/rqrs/payorder/UnifiedOrderRS.java)
- [UnifiedOrderController.java](file://sys-payment/src/main/java/com/unipay/pay/ctrl/payorder/UnifiedOrderController.java)
- [PayOrderProcessService.java](file://sys-payment/src/main/java/com/unipay/pay/service/PayOrderProcessService.java)
- [IPaymentService.java](file://sys-payment/src/main/java/com/unipay/pay/channel/IPaymentService.java)
- [AbstractPaymentService.java](file://sys-payment/src/main/java/com/unipay/pay/channel/AbstractPaymentService.java)
- [AlipayPaymentService.java](file://sys-payment/src/main/java/com/unipay/pay/channel/alipay/AlipayPaymentService.java)
- [WxpayPaymentService.java](file://sys-payment/src/main/java/com/unipay/pay/channel/wxpay/WxpayPaymentService.java)
- [ApiCodeEnum.java](file://core/src/main/java/com/unipay/core/constants/ApiCodeEnum.java)
</cite>

## 目录
1. [介绍](#介绍)
2. [统一下单接口](#统一下单接口)
3. [请求结构](#请求结构)
4. [响应结构](#响应结构)
5. [请求/响应示例](#请求响应示例)
6. [签名生成算法](#签名生成算法)
7. [异步通知机制](#异步通知机制)
8. [订单状态流转与超时处理](#订单状态流转与超时处理)
9. [错误码列表](#错误码列表)
10. [客户端调用示例](#客户端调用示例)

## 介绍
本支付API文档旨在为开发者提供完整的统一下单接口说明。系统支持多种支付方式，包括支付宝、微信支付等主流渠道，并提供灵活的分账、异步通知和订单管理功能。通过统一的接口设计，商户可以轻松集成多种支付方式，实现高效、安全的资金收付。

**Section sources**
- [UnifiedOrderController.java](file://sys-payment/src/main/java/com/unipay/pay/ctrl/payorder/UnifiedOrderController.java#L24-L91)

## 统一下单接口
统一下单接口是支付流程的核心入口，用于创建支付订单并获取支付参数。

### 接口信息
- **HTTP方法**: `POST`
- **URL路径**: `/api/pay/unifiedOrder`

### 认证方式
请求需在HTTP头中包含商户认证信息：
- `Authorization`: 商户签名令牌，用于身份验证和防篡改

该接口通过商户号（mchNo）和应用ID（appId）识别商户身份，并使用商户密钥进行签名验证，确保交易安全。

**Section sources**
- [UnifiedOrderController.java](file://sys-payment/src/main/java/com/unipay/pay/ctrl/payorder/UnifiedOrderController.java#L24-L91)

## 请求结构
请求体为JSON格式，基于`UnifiedOrderRQ`类定义。

### 请求头
| 参数名 | 类型 | 必填 | 说明 |
|-------|------|------|------|
| Authorization | String | 是 | 商户签名令牌 |

### 请求体（UnifiedOrderRQ）
| 参数名 | 类型 | 必填 | 说明 |
|-------|------|------|------|
| mchNo | String | 是 | 商户号 |
| appId | String | 是 | 应用ID |
| mchOrderNo | String | 是 | 商户订单号，需保证唯一性 |
| wayCode | String | 是 | 支付方式编码，如：`alipay_wap`, `wxpay_jsapi` |
| amount | Long | 是 | 支付金额，单位：分 |
| currency | String | 是 | 货币代码，如：CNY |
| clientIp | String | 否 | 客户端IP地址 |
| subject | String | 是 | 商品标题 |
| body | String | 是 | 商品描述信息 |
| notifyUrl | String | 否 | 异步通知地址，用于接收支付结果 |
| returnUrl | String | 否 | 跳转通知地址，支付完成后跳转 |
| expiredTime | Integer | 否 | 订单失效时间，单位：秒 |
| channelExtra | String | 否 | 特定渠道发起额外参数（JSON字符串） |
| extParam | String | 否 | 商户扩展参数 |
| divisionMode | Byte | 否 | 分账模式：0-不分账，1-自动分账，2-手动分账 |

**Section sources**
- [UnifiedOrderRQ.java](file://sys-payment/src/main/java/com/unipay/pay/rqrs/payorder/UnifiedOrderRQ.java#L23-L159)

## 响应结构
响应体为JSON格式，基于`UnifiedOrderRS`类定义。

### 响应体（UnifiedOrderRS）
| 参数名 | 类型 | 说明 |
|-------|------|------|
| code | Integer | 响应码，0表示成功 |
| msg | String | 响应消息 |
| data | Object | 响应数据对象 |
| &nbsp;&nbsp;payOrderId | String | 支付系统订单号 |
| &nbsp;&nbsp;mchOrderNo | String | 商户订单号 |
| &nbsp;&nbsp;orderState | Byte | 订单状态：0-订单生成，1-支付中，2-支付成功，3-支付失败 |
| &nbsp;&nbsp;payDataType | String | 支付参数类型：`NONE`, `JSAPI`, `REDIRECT`, `CODE_IMG_URL` |
| &nbsp;&nbsp;payData | String | 支付参数，根据payDataType类型不同而不同 |
| &nbsp;&nbsp;errCode | String | 渠道返回错误代码 |
| &nbsp;&nbsp;errMsg | String | 渠道返回错误信息 |

响应数据经过签名，确保传输过程中的完整性和安全性。

**Section sources**
- [UnifiedOrderRS.java](file://sys-payment/src/main/java/com/unipay/pay/rqrs/payorder/UnifiedOrderRS.java#L13-L52)

## 请求/响应示例

### 支付宝WAP支付示例
```json
// 请求
{
  "mchNo": "MCH001",
  "appId": "APP001",
  "mchOrderNo": "ORDER20231101001",
  "wayCode": "alipay_wap",
  "amount": 100,
  "currency": "CNY",
  "subject": "测试商品",
  "body": "测试商品描述",
  "notifyUrl": "https://yourdomain.com/notify",
  "returnUrl": "https://yourdomain.com/return"
}
```

```json
// 响应
{
  "code": 0,
  "msg": "SUCCESS",
  "data": {
    "payOrderId": "PAY20231101001",
    "mchOrderNo": "ORDER20231101001",
    "orderState": 1,
    "payDataType": "REDIRECT",
    "payData": "https://openapi.alipay.com/gateway.do?..."
  }
}
```

### 微信JSAPI支付示例
```json
// 请求
{
  "mchNo": "MCH001",
  "appId": "APP001",
  "mchOrderNo": "ORDER20231101002",
  "wayCode": "wxpay_jsapi",
  "amount": 200,
  "currency": "CNY",
  "subject": "微信商品",
  "body": "微信商品描述",
  "notifyUrl": "https://yourdomain.com/notify",
  "channelExtra": "{\"openId\": \"oTgZp1K-...\"}"
}
```

```json
// 响应
{
  "code": 0,
  "msg": "SUCCESS",
  "data": {
    "payOrderId": "PAY20231101002",
    "mchOrderNo": "ORDER20231101002",
    "orderState": 1,
    "payDataType": "JSAPI",
    "payData": "{\"appId\": \"wx123456\", \"timeStamp\": \"123456789\", \"nonceStr\": \"abc123\", \"package\": \"prepay_id=wx123456\", \"signType\": \"MD5\", \"paySign\": \"ABC123\"}"
  }
}
```

**Section sources**
- [UnifiedOrderRQ.java](file://sys-payment/src/main/java/com/unipay/pay/rqrs/payorder/UnifiedOrderRQ.java#L23-L159)
- [UnifiedOrderRS.java](file://sys-payment/src/main/java/com/unipay/pay/rqrs/payorder/UnifiedOrderRS.java#L13-L52)

## 签名生成算法
系统采用商户密钥进行请求签名验证，确保接口调用的安全性。

### 签名生成步骤
1. 将请求参数按参数名ASCII码从小到大排序（字典序）
2. 将排序后的参数键值对以`key=value`形式拼接成字符串
3. 在拼接后的字符串末尾加上商户密钥（appSecret）
4. 对最终字符串进行MD5哈希运算，得到32位小写签名值

### 签名验证流程
1. 服务端接收请求后，使用相同的算法重新计算签名
2. 将计算出的签名与请求头中的签名进行比对
3. 若签名一致，则验证通过；否则拒绝请求

签名机制有效防止了请求被篡改或重放攻击。

**Section sources**
- [UnifiedOrderController.java](file://sys-payment/src/main/java/com/unipay/pay/ctrl/payorder/UnifiedOrderController.java#L24-L91)
- [AbstractPaymentService.java](file://sys-payment/src/main/java/com/unipay/pay/channel/AbstractPaymentService.java#L17-L57)

## 异步通知机制
支付结果通过异步HTTP POST请求通知商户服务器。

### 通知流程
1. 支付平台接收到渠道支付结果后
2. 构建通知数据包并进行签名
3. 向商户配置的`notifyUrl`发起POST请求
4. 商户服务器接收并处理通知
5. 返回`SUCCESS`表示接收成功，否则会进行重试

### 通知重试策略
- 初始延迟：立即发送
- 重试间隔：1分钟、5分钟、15分钟、30分钟、1小时、3小时、6小时、12小时
- 最多重试8次，若仍失败则停止

### 通知数据结构
```json
{
  "payOrderId": "PAY20231101001",
  "mchOrderNo": "ORDER20231101001",
  "amount": 100,
  "currency": "CNY",
  "state": 2,
  "successTime": "2023-11-01 10:30:00",
  "digest": "签名值"
}
```

商户必须实现幂等处理，防止重复通知导致业务异常。

**Section sources**
- [PayOrderProcessService.java](file://sys-payment/src/main/java/com/unipay/pay/service/PayOrderProcessService.java#L20-L97)
- [AbstractPaymentService.java](file://sys-payment/src/main/java/com/unipay/pay/channel/AbstractPaymentService.java#L17-L57)

## 订单状态流转与超时处理
### 订单状态流转
```mermaid
stateDiagram-v2
[*] --> 订单生成
订单生成 --> 支付中 : 用户发起支付
支付中 --> 支付成功 : 支付平台收到成功通知
支付中 --> 支付失败 : 支付超时或渠道返回失败
支付成功 --> 完成 : 业务处理完成
支付失败 --> 完成 : 订单关闭
```

**Diagram sources**
- [PayOrderProcessService.java](file://sys-payment/src/main/java/com/unipay/pay/service/PayOrderProcessService.java#L20-L97)

### 状态说明
- **订单生成 (0)**: 订单已创建，等待支付
- **支付中 (1)**: 用户已发起支付，等待渠道结果
- **支付成功 (2)**: 支付成功，资金已到账
- **支付失败 (3)**: 支付失败，订单关闭

### 超时处理策略
1. 订单创建时设置`expiredTime`参数
2. 系统后台任务定期扫描过期订单
3. 对于状态为"支付中"的过期订单，更新为"支付失败"
4. 触发订单关闭逻辑，释放相关资源

默认订单有效期为30分钟，可通过`expiredTime`参数自定义。

**Section sources**
- [PayOrderProcessService.java](file://sys-payment/src/main/java/com/unipay/pay/service/PayOrderProcessService.java#L20-L97)

## 错误码列表
| 错误码 | 错误信息 | 解决方案 |
|-------|--------|---------|
| 0 | SUCCESS | 请求成功 |
| 9999 | 自定义业务异常 | 检查业务逻辑 |
| 10 | 系统异常[%s] | 联系技术支持 |
| 11 | 参数有误[%s] | 检查请求参数格式和必填项 |
| 12 | 数据库服务异常 | 系统维护中，请稍后重试 |
| 5000 | 新增失败 | 检查数据唯一性约束 |
| 5001 | 删除失败 | 检查数据是否存在及关联关系 |
| 5002 | 修改失败 | 检查数据状态及权限 |
| 5003 | 记录不存在 | 检查查询条件 |
| 5004 | 权限错误 | 检查用户权限配置 |

**Section sources**
- [ApiCodeEnum.java](file://core/src/main/java/com/unipay/core/constants/ApiCodeEnum.java#L7-L40)

## 客户端调用示例

### Java调用示例
```java
// 使用HttpClient或其他HTTP库
HttpClient client = HttpClient.newHttpClient();
HttpRequest request = HttpRequest.newBuilder()
    .uri(URI.create("https://api.yourpay.com/api/pay/unifiedOrder"))
    .header("Content-Type", "application/json")
    .header("Authorization", generateSignature(params, appSecret))
    .POST(HttpRequest.BodyPublishers.ofString(jsonParams))
    .build();

HttpResponse<String> response = client.send(request, HttpResponse.BodyHandlers.ofString());
System.out.println(response.body());
```

### Python调用示例
```python
import requests
import hashlib
import json

def generate_signature(params, app_secret):
    sorted_params = sorted(params.items())
    query_string = '&'.join([f"{k}={v}" for k, v in sorted_params])
    sign_string = query_string + app_secret
    return hashlib.md5(sign_string.encode()).hexdigest()

headers = {
    'Content-Type': 'application/json',
    'Authorization': generate_signature(params, 'your_app_secret')
}

response = requests.post(
    'https://api.yourpay.com/api/pay/unifiedOrder',
    json=params,
    headers=headers
)

print(response.json())
```

**Section sources**
- [UnifiedOrderRQ.java](file://sys-payment/src/main/java/com/unipay/pay/rqrs/payorder/UnifiedOrderRQ.java#L23-L159)
- [UnifiedOrderRS.java](file://sys-payment/src/main/java/com/unipay/pay/rqrs/payorder/UnifiedOrderRS.java#L13-L52)
- [UnifiedOrderController.java](file://sys-payment/src/main/java/com/unipay/pay/ctrl/payorder/UnifiedOrderController.java#L24-L91)