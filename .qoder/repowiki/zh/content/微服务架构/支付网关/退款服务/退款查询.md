# 退款查询

<cite>
**本文档引用的文件**   
- [QueryRefundOrderController.java](file://sys-payment/src/main/java/com/unipay/pay/ctrl/refund/QueryRefundOrderController.java)
- [RefundOrderProcessService.java](file://sys-payment/src/main/java/com/unipay/pay/service/RefundOrderProcessService.java)
- [RefundOrder.java](file://core/src/main/java/com/unipay/core/entity/RefundOrder.java)
- [RefundOrderService.java](file://service/src/main/java/com/unipay/service/impl/RefundOrderService.java)
- [QueryRefundOrderRQ.java](file://sys-payment/src/main/java/com/unipay/pay/rqrs/refund/QueryRefundOrderRQ.java)
- [QueryRefundOrderRS.java](file://sys-payment/src/main/java/com/unipay/pay/rqrs/refund/QueryRefundOrderRS.java)
- [ChannelRetMsg.java](file://sys-payment/src/main/java/com/unipay/pay/rqrs/msg/ChannelRetMsg.java)
- [ConfigContextQueryService.java](file://sys-payment/src/main/java/com/unipay/pay/service/ConfigContextQueryService.java)
- [ApiController.java](file://sys-payment/src/main/java/com/unipay/pay/ctrl/ApiController.java)
- [ApiRes.java](file://core/src/main/java/com/unipay/core/model/ApiRes.java)
- [BizException.java](file://core/src/main/java/com/unipay/core/exception/BizException.java)
- [ChannelOrderReissueService.java](file://sys-payment/src/main/java/com/unipay/pay/service/ChannelOrderReissueService.java)
- [ChannelRefundNoticeController.java](file://sys-payment/src/main/java/com/unipay/pay/ctrl/refund/ChannelRefundNoticeController.java)
- [PayMchNotifyService.java](file://sys-payment/src/main/java/com/unipay/pay/service/PayMchNotifyService.java)
</cite>

## 目录
1. [简介](#简介)
2. [项目结构](#项目结构)
3. [核心组件](#核心组件)
4. [架构概述](#架构概述)
5. [详细组件分析](#详细组件分析)
6. [依赖分析](#依赖分析)
7. [性能考虑](#性能考虑)
8. [故障排除指南](#故障排除指南)
9. [结论](#结论)

## 简介
本文档详细阐述了统一支付系统中退款查询功能的实现机制。重点分析了`QueryRefundOrderController`如何根据商户号、订单号等条件查询退款订单状态，以及`RefundOrderProcessService`如何从数据库获取退款订单信息，并在必要时调用支付渠道的查询接口同步最新状态。文档还描述了退款状态机（如处理中、成功、失败）及其转换规则，提供了API接口定义、请求示例和响应数据结构说明，以及处理查询超时和网络异常的策略。

## 项目结构
系统采用模块化设计，退款查询功能主要分布在`sys-payment`模块中。该模块负责处理所有与支付、退款相关的业务逻辑和API接口。`core`模块定义了核心实体和常量，`service`模块提供了数据访问服务。

```mermaid
graph TB
subgraph "sys-payment"
QueryRefundOrderController["QueryRefundOrderController<br>处理退款查询API"]
RefundOrderProcessService["RefundOrderProcessService<br>处理退款订单业务"]
ChannelOrderReissueService["ChannelOrderReissueService<br>处理补单查询"]
ChannelRefundNoticeController["ChannelRefundNoticeController<br>处理渠道回调"]
end
subgraph "service"
RefundOrderService["RefundOrderService<br>退款订单数据服务"]
end
subgraph "core"
RefundOrder["RefundOrder<br>退款订单实体"]
end
QueryRefundOrderController --> RefundOrderService
RefundOrderProcessService --> RefundOrderService
ChannelOrderReissueService --> RefundOrderProcessService
ChannelRefundNoticeController --> RefundOrderProcessService
RefundOrderService --> RefundOrder
```

**图表来源**
- [QueryRefundOrderController.java](file://sys-payment/src/main/java/com/unipay/pay/ctrl/refund/QueryRefundOrderController.java)
- [RefundOrderProcessService.java](file://sys-payment/src/main/java/com/unipay/pay/service/RefundOrderProcessService.java)
- [ChannelOrderReissueService.java](file://sys-payment/src/main/java/com/unipay/pay/service/ChannelOrderReissueService.java)
- [ChannelRefundNoticeController.java](file://sys-payment/src/main/java/com/unipay/pay/ctrl/refund/ChannelRefundNoticeController.java)
- [RefundOrderService.java](file://service/src/main/java/com/unipay/service/impl/RefundOrderService.java)
- [RefundOrder.java](file://core/src/main/java/com/unipay/core/entity/RefundOrder.java)

**章节来源**
- [QueryRefundOrderController.java](file://sys-payment/src/main/java/com/unipay/pay/ctrl/refund/QueryRefundOrderController.java)
- [RefundOrderProcessService.java](file://sys-payment/src/main/java/com/unipay/pay/service/RefundOrderProcessService.java)
- [RefundOrder.java](file://core/src/main/java/com/unipay/core/entity/RefundOrder.java)

## 核心组件
退款查询功能的核心组件包括`QueryRefundOrderController`、`RefundOrderService`和`RefundOrderProcessService`。`QueryRefundOrderController`作为API入口，负责接收和验证查询请求。`RefundOrderService`提供数据访问服务，从数据库中获取退款订单信息。`RefundOrderProcessService`负责处理退款订单的业务逻辑，包括状态更新和商户通知。

**章节来源**
- [QueryRefundOrderController.java](file://sys-payment/src/main/java/com/unipay/pay/ctrl/refund/QueryRefundOrderController.java)
- [RefundOrderService.java](file://service/src/main/java/com/unipay/service/impl/RefundOrderService.java)
- [RefundOrderProcessService.java](file://sys-payment/src/main/java/com/unipay/pay/service/RefundOrderProcessService.java)

## 架构概述
退款查询功能的架构遵循典型的分层模式，包括控制器层、服务层和数据访问层。控制器层处理HTTP请求和响应，服务层封装业务逻辑，数据访问层负责与数据库交互。此外，系统通过消息队列实现异步通知，确保高并发下的系统稳定性。

```mermaid
sequenceDiagram
participant 商户 as "商户系统"
participant 控制器 as "QueryRefundOrderController"
participant 服务 as "RefundOrderService"
participant 数据库 as "数据库"
participant 支付渠道 as "支付渠道"
participant 通知服务 as "PayMchNotifyService"
商户->>控制器 : 发送查询请求
控制器->>控制器 : 验证签名和参数
控制器->>服务 : 调用queryMchOrder()
服务->>数据库 : 查询退款订单
数据库-->>服务 : 返回订单信息
服务-->>控制器 : 返回RefundOrder对象
控制器->>控制器 : 构建响应数据
控制器-->>商户 : 返回查询结果
alt 需要同步状态
控制器->>支付渠道 : 调用渠道查询接口
支付渠道-->>控制器 : 返回渠道状态
控制器->>服务 : 更新订单状态
服务->>通知服务 : 触发商户通知
通知服务->>商户 : 发送异步通知
end
```

**图表来源**
- [QueryRefundOrderController.java](file://sys-payment/src/main/java/com/unipay/pay/ctrl/refund/QueryRefundOrderController.java)
- [RefundOrderService.java](file://service/src/main/java/com/unipay/service/impl/RefundOrderService.java)
- [PayMchNotifyService.java](file://sys-payment/src/main/java/com/unipay/pay/service/PayMchNotifyService.java)

## 详细组件分析

### QueryRefundOrderController分析
`QueryRefundOrderController`是退款查询功能的API入口，继承自`ApiController`，实现了统一的请求处理和签名验证逻辑。

```mermaid
classDiagram
class QueryRefundOrderController {
+RefundOrderService refundOrderService
+ConfigContextQueryService configContextQueryService
+ApiRes queryRefundOrder()
}
class ApiController {
+getRQByWithMchSign(Class<T> cls)
}
class RefundOrderService {
+RefundOrder queryMchOrder(String mchNo, String mchRefundNo, String refundOrderId)
}
class ConfigContextQueryService {
+MchApp queryMchApp(String mchNo, String mchAppId)
}
QueryRefundOrderController --> ApiController : "继承"
QueryRefundOrderController --> RefundOrderService : "依赖"
QueryRefundOrderController --> ConfigContextQueryService : "依赖"
RefundOrderService --> RefundOrder : "操作"
```

**图表来源**
- [QueryRefundOrderController.java](file://sys-payment/src/main/java/com/unipay/pay/ctrl/refund/QueryRefundOrderController.java)
- [ApiController.java](file://sys-payment/src/main/java/com/unipay/pay/ctrl/ApiController.java)
- [RefundOrderService.java](file://service/src/main/java/com/unipay/service/impl/RefundOrderService.java)
- [ConfigContextQueryService.java](file://sys-payment/src/main/java/com/unipay/pay/service/ConfigContextQueryService.java)

**章节来源**
- [QueryRefundOrderController.java](file://sys-payment/src/main/java/com/unipay/pay/ctrl/refund/QueryRefundOrderController.java)

### RefundOrderProcessService分析
`RefundOrderProcessService`负责处理退款订单的业务逻辑，根据支付渠道返回的状态更新订单状态并触发相应的后续操作。

```mermaid
classDiagram
class RefundOrderProcessService {
+RefundOrderService refundOrderService
+PayMchNotifyService payMchNotifyService
+boolean handleRefundOrder4Channel(ChannelRetMsg, RefundOrder)
}
class RefundOrderService {
+boolean updateIng2Success(String refundOrderId, String channelOrderNo)
+boolean updateIng2Fail(String refundOrderId, String channelOrderNo, String channelErrCode, String channelErrMsg)
}
class PayMchNotifyService {
+void refundOrderNotify(RefundOrder dbRefundOrder)
}
class ChannelRetMsg {
+ChannelState channelState
+String channelOrderId
+String channelErrCode
+String channelErrMsg
}
class RefundOrder {
+Byte state
+String notifyUrl
}
RefundOrderProcessService --> RefundOrderService : "依赖"
RefundOrderProcessService --> PayMchNotifyService : "依赖"
RefundOrderProcessService --> ChannelRetMsg : "输入"
RefundOrderProcessService --> RefundOrder : "操作"
```

**图表来源**
- [RefundOrderProcessService.java](file://sys-payment/src/main/java/com/unipay/pay/service/RefundOrderProcessService.java)
- [RefundOrderService.java](file://service/src/main/java/com/unipay/service/impl/RefundOrderService.java)
- [PayMchNotifyService.java](file://sys-payment/src/main/java/com/unipay/pay/service/PayMchNotifyService.java)
- [ChannelRetMsg.java](file://sys-payment/src/main/java/com/unipay/pay/rqrs/msg/ChannelRetMsg.java)
- [RefundOrder.java](file://core/src/main/java/com/unipay/core/entity/RefundOrder.java)

**章节来源**
- [RefundOrderProcessService.java](file://sys-payment/src/main/java/com/unipay/pay/service/RefundOrderProcessService.java)

### 退款状态机
系统定义了清晰的退款状态机，确保订单状态的准确性和一致性。

```mermaid
stateDiagram-v2
[*] --> STATE_INIT
STATE_INIT --> STATE_ING : "发起退款"
STATE_ING --> STATE_SUCCESS : "渠道确认成功"
STATE_ING --> STATE_FAIL : "渠道确认失败"
STATE_ING --> STATE_CLOSED : "超时关闭"
STATE_INIT --> STATE_CLOSED : "超时关闭"
note right of STATE_INIT
订单生成
end note
note right of STATE_ING
退款中
end note
note right of STATE_SUCCESS
退款成功
end note
note right of STATE_FAIL
退款失败
end note
note right of STATE_CLOSED
退款任务关闭
end note
```

**图表来源**
- [RefundOrder.java](file://core/src/main/java/com/unipay/core/entity/RefundOrder.java)

## 依赖分析
退款查询功能依赖于多个核心服务和组件，形成了一个完整的业务闭环。

```mermaid
graph TD
QueryRefundOrderController --> RefundOrderService
QueryRefundOrderController --> ConfigContextQueryService
RefundOrderProcessService --> RefundOrderService
RefundOrderProcessService --> PayMchNotifyService
ChannelOrderReissueService --> RefundOrderProcessService
ChannelRefundNoticeController --> RefundOrderProcessService
PayMchNotifyService --> MQSender
RefundOrderService --> PayOrderMapper
RefundOrder --> BaseModel
ApiRes --> ApiCodeEnum
style QueryRefundOrderController fill:#f9f,stroke:#333
style RefundOrderProcessService fill:#f9f,stroke:#333
style ChannelOrderReissueService fill:#f9f,stroke:#333
style ChannelRefundNoticeController fill:#f9f,stroke:#333
```

**图表来源**
- [QueryRefundOrderController.java](file://sys-payment/src/main/java/com/unipay/pay/ctrl/refund/QueryRefundOrderController.java)
- [RefundOrderProcessService.java](file://sys-payment/src/main/java/com/unipay/pay/service/RefundOrderProcessService.java)
- [ChannelOrderReissueService.java](file://sys-payment/src/main/java/com/unipay/pay/service/ChannelOrderReissueService.java)
- [ChannelRefundNoticeController.java](file://sys-payment/src/main/java/com/unipay/pay/ctrl/refund/ChannelRefundNoticeController.java)
- [RefundOrderService.java](file://service/src/main/java/com/unipay/service/impl/RefundOrderService.java)
- [PayMchNotifyService.java](file://sys-payment/src/main/java/com/unipay/pay/service/PayMchNotifyService.java)
- [RefundOrder.java](file://core/src/main/java/com/unipay/core/entity/RefundOrder.java)
- [ApiRes.java](file://core/src/main/java/com/unipay/core/model/ApiRes.java)

**章节来源**
- [QueryRefundOrderController.java](file://sys-payment/src/main/java/com/unipay/pay/ctrl/refund/QueryRefundOrderController.java)
- [RefundOrderProcessService.java](file://sys-payment/src/main/java/com/unipay/pay/service/RefundOrderProcessService.java)
- [RefundOrder.java](file://core/src/main/java/com/unipay/core/entity/RefundOrder.java)

## 性能考虑
退款查询功能在设计时充分考虑了性能和稳定性。系统通过缓存机制减少数据库查询压力，使用消息队列实现异步通知，避免阻塞主业务流程。对于高并发场景，系统采用数据库连接池和线程池优化资源利用。

## 故障排除指南
当退款查询出现问题时，可以按照以下步骤进行排查：
1. 检查请求参数是否正确，特别是商户号、应用ID和签名。
2. 查看日志文件，定位具体的错误信息。
3. 检查数据库连接是否正常，退款订单是否存在。
4. 验证支付渠道配置是否正确。
5. 检查消息队列服务是否正常运行。

**章节来源**
- [QueryRefundOrderController.java](file://sys-payment/src/main/java/com/unipay/pay/ctrl/refund/QueryRefundOrderController.java)
- [RefundOrderProcessService.java](file://sys-payment/src/main/java/com/unipay/pay/service/RefundOrderProcessService.java)
- [BizException.java](file://core/src/main/java/com/unipay/core/exception/BizException.java)

## 结论
本文档详细阐述了统一支付系统中退款查询功能的实现机制。通过分析核心组件、架构设计和业务流程，展示了系统如何高效、可靠地处理退款查询请求。系统采用分层架构和模块化设计，确保了代码的可维护性和扩展性。同时，通过状态机和异步通知机制，保证了业务逻辑的准确性和系统的稳定性。