# 支付服务

<cite>
**本文档引用文件**   
- [UnifiedOrderController.java](file://sys-payment/src/main/java/com/unipay/pay/ctrl/payorder/UnifiedOrderController.java)
- [PayOrderProcessService.java](file://sys-payment/src/main/java/com/unipay/pay/service/PayOrderProcessService.java)
- [IPaymentService.java](file://sys-payment/src/main/java/com/unipay/pay/channel/IPaymentService.java)
- [AlipayPaymentService.java](file://sys-payment/src/main/java/com/unipay/pay/channel/alipay/AlipayPaymentService.java)
- [WxpayPaymentService.java](file://sys-payment/src/main/java/com/unipay/pay/channel/wxpay/WxpayPaymentService.java)
- [PayOrderMchNotifyMQ.java](file://components/components-mq/src/main/java/com/unipay/components/mq/model/PayOrderMchNotifyMQ.java)
- [PayOrder.java](file://core/src/main/java/com/unipay/core/entity/PayOrder.java)
- [PayOrderExpiredTask.java](file://sys-payment/src/main/java/com/unipay/pay/task/PayOrderExpiredTask.java)
- [PayMchNotifyService.java](file://sys-payment/src/main/java/com/unipay/pay/service/PayMchNotifyService.java)
- [AbstractPayOrderController.java](file://sys-payment/src/main/java/com/unipay/pay/ctrl/payorder/AbstractPayOrderController.java)
</cite>

## 目录
1. [统一下单接口实现](#统一下单接口实现)
2. [支付订单处理流程](#支付订单处理流程)
3. [支付方式路由与实现](#支付方式路由与实现)
4. [支付结果通知机制](#支付结果通知机制)
5. [支付订单状态机](#支付订单状态机)
6. [超时订单处理](#超时订单处理)
7. [支付结果缓存策略](#支付结果缓存策略)
8. [扩展新的支付方式](#扩展新的支付方式)

## 统一下单接口实现

`UnifiedOrderController` 是支付服务的核心入口，负责处理统一下单请求。该控制器通过 `/api/pay/unifiedOrder` 接口接收客户端请求，执行完整的支付订单创建流程。

接口实现逻辑始于参数获取与签名验证，通过 `getRQByWithMchSign()` 方法获取并验证请求参数。随后调用 `buildBizRQ()` 方法构建业务请求对象，该方法会根据支付方式代码（wayCode）进行特殊处理：

- 当支付方式为 `QR_CASHIER`（收银台聚合支付）时，直接返回业务请求对象
- 当支付方式为 `AUTO_BAR`（自动分类条码）时，通过条码内容识别实际支付方式
- 对于其他支付方式，验证其是否在系统支持的支付方式列表中

构建完成后，调用父类 `AbstractPayOrderController` 中的 `unifiedOrder()` 方法执行核心业务逻辑。最终响应数据会根据订单状态决定是否包含支付数据（如二维码或跳转链接），并通过商户应用密钥进行签名后返回。

**本节内容来源**
- [UnifiedOrderController.java](file://sys-payment/src/main/java/com/unipay/pay/ctrl/payorder/UnifiedOrderController.java#L24-L91)
- [AbstractPayOrderController.java](file://sys-payment/src/main/java/com/unipay/pay/ctrl/payorder/AbstractPayOrderController.java#L62-L213)

## 支付订单处理流程

`PayOrderProcessService` 服务类负责协调支付订单的整个处理流程，包括订单状态更新、分账处理和商户通知等核心业务逻辑。

该服务通过 `updateIngAndSuccessOrFailByCreatebyOrder()` 方法实现从"支付中"到"支付成功"或"支付失败"的状态转换。此方法使用 `@Transactional` 注解确保数据库操作的原子性，首先调用 `payOrderService.updateInit2Ing()` 将订单状态从"订单生成"更新为"支付中"，然后根据支付结果调用 `updateIng2SuccessOrFail()` 完成最终状态更新。

当支付明确成功时，`confirmSuccess()` 方法会被调用，执行一系列后续业务操作：
1. 查询最新订单详情
2. 设置订单状态为"支付成功"
3. 处理自动分账逻辑
4. 发送商户通知

自动分账处理通过 `updatePayOrderAutoDivision()` 方法实现，该方法会检查订单的分账模式，若为自动分账模式，则更新订单分账状态并推送消息到分账MQ，由定时任务在80秒后处理分账任务。

**本节内容来源**
- [PayOrderProcessService.java](file://sys-payment/src/main/java/com/unipay/pay/service/PayOrderProcessService.java#L20-L97)
- [PayOrderService.java](file://service/src/main/java/com/unipay/service/impl/PayOrderService.java#L47-L62)
- [PayOrderService.java](file://service/src/main/java/com/unipay/service/impl/PayOrderService.java#L114-L124)

## 支付方式路由与实现

支付服务采用接口抽象与实现分离的设计模式，通过 `IPaymentService` 接口定义支付服务的标准行为，各具体支付方式通过实现该接口完成特定逻辑。

`IPaymentService` 接口定义了五个核心方法：
- `getIfCode()`：获取支付接口代码（如支付宝为"ALIPAY"）
- `isSupport()`：判断是否支持特定支付方式
- `preCheck()`：前置参数校验
- `customPayOrderId()`：自定义支付订单号
- `pay()`：调起支付接口并返回响应数据

具体实现类如 `AlipayPaymentService` 和 `WxpayPaymentService` 均继承自 `AbstractPaymentService` 抽象类。这些实现类通过 `PaywayUtil.getRealPaywayService()` 工具类动态获取实际的支付方式服务实例，实现了灵活的支付方式路由。

以微信支付为例，`WxpayPaymentService` 的 `pay()` 方法会根据配置的API版本（V2或V3）选择不同的实现路径。对于V3版本，系统会调用 `PaywayUtil.getRealPaywayV3Service()` 获取V3专用服务实例，确保兼容不同版本的微信支付API。

**本节内容来源**
- [IPaymentService.java](file://sys-payment/src/main/java/com/unipay/pay/channel/IPaymentService.java#L12-L30)
- [AlipayPaymentService.java](file://sys-payment/src/main/java/com/unipay/pay/channel/alipay/AlipayPaymentService.java#L17-L40)
- [WxpayPaymentService.java](file://sys-payment/src/main/java/com/unipay/pay/channel/wxpay/WxpayPaymentService.java#L26-L155)

## 支付结果通知机制

支付结果通知机制采用消息队列（MQ）异步处理模式，确保通知的可靠性和系统性能。核心组件为 `PayOrderMchNotifyMQ` 消息模型和 `PayMchNotifyService` 服务类。

当支付订单状态明确为成功或失败时，`PayMchNotifyService.payOrderNotify()` 方法会被调用。该方法首先检查通知地址是否为空，然后查询是否存在已有的通知记录以避免重复发送。创建 `MchNotifyRecord` 记录后，将其保存到数据库，并通过 `mqSender.send()` 方法将通知消息推送到 `QUEUE_PAY_ORDER_MCH_NOTIFY` 队列。

`PayOrderMchNotifyMQ` 类定义了消息的结构和行为：
- `MQ_NAME`：队列名称为"QUEUE_PAY_ORDER_MCH_NOTIFY"
- `MsgPayload`：消息载体，包含通知单号（notifyId）
- `getMQType()`：消息类型为点对点（QUEUE）模式
- `build()`：静态工厂方法，用于创建消息实例

消息消费者通过实现 `IMQReceiver` 接口接收并处理通知消息，从消息中解析出通知单号，然后查询通知记录并执行HTTP回调，将支付结果通知到商户指定的异步通知地址。

```mermaid
sequenceDiagram
participant UnifiedOrderController as 统一下单控制器
participant PayOrderProcessService as 订单处理服务
participant PayMchNotifyService as 商户通知服务
participant MQ as 消息队列
participant MQReceiver as 消息接收器
participant Merchant as 商户服务器
UnifiedOrderController->>PayOrderProcessService : confirmSuccess()
PayOrderProcessService->>PayMchNotifyService : payOrderNotify()
PayMchNotifyService->>PayMchNotifyService : 创建通知记录
PayMchNotifyService->>MQ : send(PayOrderMchNotifyMQ.build())
MQ->>MQReceiver : 接收消息
MQReceiver->>MQReceiver : 解析notifyId
MQReceiver->>MQReceiver : 查询通知记录
MQReceiver->>Merchant : HTTP POST通知
Merchant-->>MQReceiver : 返回响应
```

**本节内容来源**
- [PayOrderMchNotifyMQ.java](file://components/components-mq/src/main/java/com/unipay/components/mq/model/PayOrderMchNotifyMQ.java#L16-L68)
- [PayMchNotifyService.java](file://sys-payment/src/main/java/com/unipay/pay/service/PayMchNotifyService.java#L35-L83)

## 支付订单状态机

支付订单状态机定义了订单在其生命周期内的各种状态及其转换规则。`PayOrder` 实体类中通过静态常量定义了所有可能的状态值：

- `STATE_INIT = 0`：订单生成
- `STATE_ING = 1`：支付中
- `STATE_SUCCESS = 2`：支付成功
- `STATE_FAIL = 3`：支付失败
- `STATE_CANCEL = 4`：已撤销
- `STATE_REFUND = 5`：已退款
- `STATE_CLOSED = 6`：订单关闭

状态转换遵循严格的业务规则：
1. 订单创建时状态为`STATE_INIT`
2. 调用支付接口后状态变为`STATE_ING`
3. 支付成功后状态变为`STATE_SUCCESS`
4. 支付失败后状态变为`STATE_FAIL`
5. 订单超时或被关闭后状态变为`STATE_CLOSED`

状态转换通过 `PayOrderService` 中的多个更新方法实现，如 `updateInit2Ing()`、`updateIng2Success()`、`updateIng2Fail()` 等。每个更新方法都包含状态检查，确保只有在正确状态下才能进行转换，防止非法状态变更。

```mermaid
stateDiagram-v2
[*] --> STATE_INIT
STATE_INIT --> STATE_ING : 调用支付接口
STATE_INIT --> STATE_CLOSED : 订单超时
STATE_ING --> STATE_SUCCESS : 支付成功
STATE_ING --> STATE_FAIL : 支付失败
STATE_ING --> STATE_CLOSED : 订单关闭
STATE_SUCCESS --> STATE_REFUND : 发起退款
STATE_REFUND --> [*]
STATE_CLOSED --> [*]
STATE_FAIL --> [*]
```

**本节内容来源**
- [PayOrder.java](file://core/src/main/java/com/unipay/core/entity/PayOrder.java#L24-L278)
- [PayOrderService.java](file://service/src/main/java/com/unipay/service/impl/PayOrderService.java)

## 超时订单处理

超时订单处理由 `PayOrderExpiredTask` 定时任务类负责，该任务每分钟执行一次，自动关闭已过期的支付订单。

`PayOrderExpiredTask` 使用 `@Scheduled(cron="0 0/1 * * * ?")` 注解配置执行计划，每分钟触发一次 `start()` 方法。该方法调用 `payOrderService.updateOrderExpired()` 服务方法，批量更新所有满足条件的订单状态。

`updateOrderExpired()` 方法通过MyBatis-Plus的Lambda查询构建器，查找所有状态为"订单生成"或"支付中"且过期时间小于当前时间的订单，并将其状态更新为"订单关闭"。该操作使用单条SQL语句完成，确保了高效率和数据一致性。

这种定时任务处理方式避免了对每个订单设置独立超时机制的复杂性，通过集中式批量处理提高了系统性能和可维护性。

```mermaid
flowchart TD
Start([定时任务启动]) --> Query["查询过期订单<br/>状态: INIT 或 ING<br/>过期时间 < 当前时间"]
Query --> HasExpired{"存在过期订单?"}
HasExpired --> |是| Update["批量更新状态为 CLOSED"]
Update --> Log["记录处理数量"]
HasExpired --> |否| Log
Log --> End([任务结束])
```

**本节内容来源**
- [PayOrderExpiredTask.java](file://sys-payment/src/main/java/com/unipay/pay/task/PayOrderExpiredTask.java#L13-L27)
- [PayOrderService.java](file://service/src/main/java/com/unipay/service/impl/PayOrderService.java#L36-L640)

## 支付结果缓存策略

系统通过多层次的缓存策略优化支付结果的查询性能和响应速度。核心缓存机制包括：

1. **应用级缓存**：通过 `ConfigContextQueryService` 查询服务缓存商户配置信息，避免频繁的数据库访问。商户应用信息、服务商参数等配置数据在首次查询后会被缓存，后续请求直接从缓存获取。

2. **分布式缓存**：系统集成Redis作为分布式缓存，通过 `RedisUtil` 工具类提供统一的缓存操作接口。支付订单的关键信息如订单状态、支付结果等可缓存到Redis中，支持高并发场景下的快速读取。

3. **本地缓存**：对于频繁访问且不经常变更的数据（如支付方式定义、系统配置等），系统采用本地缓存机制，进一步减少网络开销。

4. **缓存更新策略**：当商户配置发生变化时，系统通过消息队列（如`ResetAppConfigMQ`）通知相关节点刷新缓存，确保缓存数据的一致性。

缓存策略显著提升了系统的响应性能，特别是在高并发支付场景下，减少了数据库的压力，保证了服务的稳定性和可用性。

## 扩展新的支付方式

扩展新的支付方式需要遵循标准的实现模式，以确保与现有系统的兼容性和可维护性。以下是扩展新支付方式的步骤：

1. **创建实现类**：创建新的支付服务类，实现 `IPaymentService` 接口或继承 `AbstractPaymentService` 抽象类。

2. **实现核心方法**：重写 `getIfCode()` 方法返回唯一的接口代码，如 `CS.IF_CODE.NEW_PAY`。

3. **实现支付逻辑**：在 `pay()` 方法中实现调用新支付渠道API的逻辑，处理请求参数构建、API调用和响应解析。

4. **添加Spring Bean注解**：使用 `@Service` 注解将新支付服务注册为Spring Bean，确保可以通过 `SpringBeansUtil.getBean()` 获取实例。

5. **实现前置校验**：在 `preCheck()` 方法中实现参数校验逻辑，确保请求参数符合新支付方式的要求。

6. **配置依赖**：在项目依赖中添加新支付渠道的SDK或API客户端库。

7. **测试验证**：编写单元测试和集成测试，验证新支付方式的功能正确性。

通过这种模块化的设计，系统可以轻松支持新的支付渠道，而无需修改核心支付流程，体现了良好的开闭原则和扩展性。

```mermaid
classDiagram
class IPaymentService {
<<interface>>
+getIfCode() String
+isSupport(wayCode) boolean
+preCheck(bizRQ, payOrder) String
+customPayOrderId(bizRQ, payOrder, context) String
+pay(bizRQ, payOrder, context) AbstractRS
}
class AbstractPaymentService {
<<abstract>>
+configContextQueryService ConfigContextQueryService
}
class NewPaymentService {
+getIfCode()
+isSupport()
+preCheck()
+pay()
}
IPaymentService <|-- AbstractPaymentService
AbstractPaymentService <|-- NewPaymentService
NewPaymentService ..> ConfigContextQueryService : 使用
```

**本节内容来源**
- [IPaymentService.java](file://sys-payment/src/main/java/com/unipay/pay/channel/IPaymentService.java#L12-L30)
- [AbstractPaymentService.java](file://sys-payment/src/main/java/com/unipay/pay/channel/AbstractPaymentService.java)
- [AlipayPaymentService.java](file://sys-payment/src/main/java/com/unipay/pay/channel/alipay/AlipayPaymentService.java)