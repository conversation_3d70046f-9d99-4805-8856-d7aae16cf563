# 统一下单

<cite>
**本文档引用文件**  
- [UnifiedOrderController.java](file://sys-payment/src/main/java/com/unipay/pay/ctrl/payorder/UnifiedOrderController.java)
- [PayOrderProcessService.java](file://sys-payment/src/main/java/com/unipay/pay/service/PayOrderProcessService.java)
- [IPaymentService.java](file://sys-payment/src/main/java/com/unipay/pay/channel/IPaymentService.java)
- [UnifiedOrderRQ.java](file://sys-payment/src/main/java/com/unipay/pay/rqrs/payorder/UnifiedOrderRQ.java)
- [UnifiedOrderRS.java](file://sys-payment/src/main/java/com/unipay/pay/rqrs/payorder/UnifiedOrderRS.java)
- [AlipayPaymentService.java](file://sys-payment/src/main/java/com/unipay/pay/channel/alipay/AlipayPaymentService.java)
- [WxpayPaymentService.java](file://sys-payment/src/main/java/com/unipay/pay/channel/wxpay/WxpayPaymentService.java)
- [PaywayUtil.java](file://sys-payment/src/main/java/com/unipay/pay/util/PaywayUtil.java)
- [ApiController.java](file://sys-payment/src/main/java/com/unipay/pay/ctrl/ApiController.java)
- [PayOrderService.java](file://service/src/main/java/com/unipay/service/impl/PayOrderService.java)
- [PayMchNotifyService.java](file://sys-payment/src/main/java/com/unipay/pay/service/PayMchNotifyService.java)
- [ConfigContextQueryService.java](file://sys-payment/src/main/java/com/unipay/pay/service/ConfigContextQueryService.java)
</cite>

## 目录
1. [统一下单功能概述](#统一下单功能概述)
2. [请求处理流程](#请求处理流程)
3. [统一订单请求对象UnifiedOrderRQ](#统一订单请求对象unifiedorderrq)
4. [统一订单响应对象UnifiedOrderRS](#统一订单响应对象unifiedorderrs)
5. [支付渠道选择与执行](#支付渠道选择与执行)
6. [不同支付方式的下单流程差异](#不同支付方式的下单流程差异)
7. [订单状态管理与超时机制](#订单状态管理与超时机制)
8. [异常处理策略](#异常处理策略)

## 统一下单功能概述

统一下单功能是支付系统的核心入口，提供标准化的API接口供商户发起支付请求。该功能通过`UnifiedOrderController`暴露RESTful接口，接收商户的支付请求，经过一系列前置校验和业务处理后，调用相应的支付渠道完成支付流程。系统支持多种支付方式（如支付宝、微信等），并具备自动分账、商户通知、订单状态管理等高级功能。

**本节来源**  
- [UnifiedOrderController.java](file://sys-payment/src/main/java/com/unipay/pay/ctrl/payorder/UnifiedOrderController.java#L24-L91)

## 请求处理流程

统一下单的请求处理流程始于`UnifiedOrderController`的`unifiedOrder()`方法，该方法处理`/api/pay/unifiedOrder`接口的POST请求。整个流程包括参数获取与验签、商户身份认证、应用配置加载、支付方式校验、支付逻辑执行和响应生成等关键步骤。

```mermaid
sequenceDiagram
participant 商户 as 商户系统
participant 控制器 as UnifiedOrderController
participant 服务层 as PayOrderProcessService
participant 支付服务 as IPaymentService
participant 渠道 as 支付渠道
商户->>控制器 : 发起统一下单请求
控制器->>控制器 : getRQByWithMchSign() 获取参数并验签
控制器->>控制器 : buildBizRQ() 构建业务请求对象
控制器->>控制器 : unifiedOrder() 调用具体支付方式
控制器->>支付服务 : 调用pay()方法
支付服务->>渠道 : 调用支付渠道API
渠道-->>支付服务 : 返回支付结果
支付服务-->>控制器 : 返回UnifiedOrderRS
控制器->>控制器 : 构建最终响应
控制器-->>商户 : 返回签名后的响应
```

**流程图来源**  
- [UnifiedOrderController.java](file://sys-payment/src/main/java/com/unipay/pay/ctrl/payorder/UnifiedOrderController.java#L34-L61)
- [ApiController.java](file://sys-payment/src/main/java/com/unipay/pay/ctrl/ApiController.java#L40-L86)

### 参数校验与验签

在接收到请求后，系统首先通过`getRQByWithMchSign()`方法获取请求参数并进行验签。该方法从请求体中解析出`UnifiedOrderRQ`对象，并验证商户号（mchNo）、应用ID（appId）和签名（sign）等关键参数是否为空。随后，系统查询商户和应用的配置信息，并验证商户状态是否可用。最后，使用商户的应用密钥（appSecret）对请求参数进行签名验证，确保请求的完整性和来源的合法性。

**本节来源**  
- [ApiController.java](file://sys-payment/src/main/java/com/unipay/pay/ctrl/ApiController.java#L40-L86)

### 商户身份认证与应用配置加载

商户身份认证是确保交易安全的重要环节。系统通过`configContextQueryService.queryMchInfoAndAppInfo()`方法查询商户和应用的完整配置上下文（MchAppConfigContext）。该上下文包含了商户信息、应用信息、服务商信息（如适用）以及相关的支付参数配置。只有当商户存在且状态为启用（CS.YES），以及应用存在且状态为启用时，系统才会继续处理支付请求。此过程确保了只有合法且激活的商户才能发起支付。

**本节来源**  
- [ApiController.java](file://sys-payment/src/main/java/com/unipay/pay/ctrl/ApiController.java#L40-L86)

### 支付方式校验

在参数校验和身份认证通过后，系统会校验请求的支付方式是否支持。`buildBizRQ()`方法首先检查支付方式（wayCode）是否为特殊的聚合支付场景（QR_CASHIER），如果是则直接返回。对于自动分类条码支付（AUTO_BAR），系统会根据授权码（authCode）动态确定具体的支付方式。对于其他支付方式，系统会查询`PayWay`表，确认该支付方式是否存在且已配置。如果支付方式不支持，系统将抛出“不支持的支付方式”异常。

**本节来源**  
- [UnifiedOrderController.java](file://sys-payment/src/main/java/com/unipay/pay/ctrl/payorder/UnifiedOrderController.java#L64-L88)

## 统一订单请求对象UnifiedOrderRQ

`UnifiedOrderRQ`是统一下单接口的请求参数对象，继承自`AbstractMchAppRQ`，包含了发起支付所需的所有基本信息。该对象通过`buildBizRQ()`方法根据具体的支付方式转换为相应的业务请求对象。

### 核心字段说明

| 字段名 | 类型 | 必填 | 描述 |
| :--- | :--- | :--- | :--- |
| mchOrderNo | String | 是 | 商户订单号，由商户生成，需保证在商户系统内唯一 |
| wayCode | String | 是 | 支付方式代码，如`wxpay_jsapi`、`alipay_wap`等 |
| amount | Long | 是 | 支付金额，单位为分 |
| currency | String | 是 | 货币代码，目前通常为`CNY` |
| clientIp | String | 否 | 客户端IP地址，用于风控和渠道要求 |
| subject | String | 是 | 商品标题，用于支付渠道展示 |
| body | String | 是 | 商品描述信息，详细说明交易内容 |
| notifyUrl | String | 否 | 异步通知地址，支付结果将通过POST请求通知此URL |
| returnUrl | String | 否 | 跳转通知地址，支付完成后浏览器将重定向至此URL |
| expiredTime | Integer | 否 | 订单失效时间，单位为秒，从创建时开始计算 |
| channelExtra | String | 否 | 特定渠道发起的额外参数，以JSON格式传递 |
| extParam | String | 否 | 商户扩展参数，可用于传递自定义数据 |
| divisionMode | Byte | 否 | 分账模式：0-不分账，1-自动分账，2-手动分账 |

**本节来源**  
- [UnifiedOrderRQ.java](file://sys-payment/src/main/java/com/unipay/pay/rqrs/payorder/UnifiedOrderRQ.java#L23-L159)

### 业务请求对象转换

`buildBizRQ()`方法是`UnifiedOrderRQ`的核心功能，它根据`wayCode`的值将通用的`UnifiedOrderRQ`对象转换为特定支付方式的业务请求对象（如`AliBarOrderRQ`、`WxJsapiOrderRQ`等）。该方法通过`JSONObject.parseObject()`将`channelExtra`字段中的JSON字符串反序列化为目标业务请求对象，并使用`BeanUtils.copyProperties()`将父类的公共属性复制过去。这种设计实现了请求参数的灵活扩展，使得不同支付方式可以拥有自己独特的参数。

**本节来源**  
- [UnifiedOrderRQ.java](file://sys-payment/src/main/java/com/unipay/pay/rqrs/payorder/UnifiedOrderRQ.java#L74-L151)

## 统一订单响应对象UnifiedOrderRS

`UnifiedOrderRS`是统一下单接口的响应结果对象，继承自`AbstractRS`，封装了支付订单的创建结果和后续支付所需的参数。

### 核心字段说明

| 字段名 | 类型 | 描述 |
| :--- | :--- | :--- |
| payOrderId | String | 系统生成的支付订单号，全局唯一 |
| mchOrderNo | String | 商户订单号，与请求一致 |
| orderState | Byte | 订单状态：INIT(初始化), ING(支付中), SUCCESS(成功), FAIL(失败) |
| payDataType | String | 支付参数类型，指示前端如何处理payData |
| payData | String | 支付参数，具体内容取决于payDataType |
| errCode | String | 渠道返回的错误代码（如有） |
| errMsg | String | 渠道返回的错误信息（如有） |

**本节来源**  
- [UnifiedOrderRS.java](file://sys-payment/src/main/java/com/unipay/pay/rqrs/payorder/UnifiedOrderRS.java#L13-L52)

### 支付参数生成

`buildPayDataType()`和`buildPayData()`是两个抽象方法，由具体的支付方式实现类重写。`buildPayDataType()`返回支付参数的类型，如`NONE`（无参数）、`JSAPI`（调起JSAPI）、`REDIRECT`（重定向）或`CODE_IMG_URL`（二维码图片URL）。`buildPayData()`则根据支付方式生成具体的支付参数，例如支付宝的form表单、微信JSAPI的prepay_id等。在`UnifiedOrderController`中，只有当订单状态为`INIT`、`ING`或`SUCCESS`时，才会将这两个字段设置到最终的响应中。

**本节来源**  
- [UnifiedOrderRS.java](file://sys-payment/src/main/java/com/unipay/pay/rqrs/payorder/UnifiedOrderRS.java#L42-L49)

## 支付渠道选择与执行

支付渠道的选择与执行由`PayOrderProcessService`和`IPaymentService`接口共同完成。系统采用策略模式，通过`IPaymentService`的实现类来处理不同支付渠道的具体逻辑。

### IPaymentService接口

`IPaymentService`是所有支付服务的统一接口，定义了支付服务必须实现的核心方法：

```java
public interface IPaymentService {
    String getIfCode(); // 获取支付接口代码，如ALIPAY, WXPAY
    boolean isSupport(String wayCode); // 检查是否支持指定的支付方式
    String preCheck(UnifiedOrderRQ bizRQ, PayOrder payOrder); // 前置参数检查
    String customPayOrderId(UnifiedOrderRQ bizRQ, PayOrder payOrder, MchAppConfigContext mchAppConfigContext); // 自定义支付订单号
    AbstractRS pay(UnifiedOrderRQ bizRQ, PayOrder payOrder, MchAppConfigContext mchAppConfigContext) throws Exception; // 执行支付
}
```

**本节来源**  
- [IPaymentService.java](file://sys-payment/src/main/java/com/unipay/pay/channel/IPaymentService.java#L12-L30)

### 支付服务实现

每个支付渠道（如支付宝、微信）都有一个对应的主服务类（如`AlipayPaymentService`、`WxpayPaymentService`），这些类实现了`IPaymentService`接口。它们的主要职责是：
1.  **路由分发**：根据`wayCode`，通过`PaywayUtil`工具类动态获取具体的支付方式实现类。
2.  **参数预处理**：调用具体实现类的`preCheck`方法进行参数校验。
3.  **执行支付**：调用具体实现类的`pay`方法，完成与支付渠道的通信。

以`AlipayPaymentService`为例，其`pay()`方法通过`PaywayUtil.getRealPaywayService()`获取到如`AliBarPaymentService`或`AliJsapiPaymentService`的具体实例，并委托它们完成实际的支付请求。

```mermaid
classDiagram
class IPaymentService {
<<interface>>
+getIfCode() String
+isSupport(wayCode) boolean
+preCheck(bizRQ, payOrder) String
+customPayOrderId(bizRQ, payOrder, mchAppConfigContext) String
+pay(bizRQ, payOrder, mchAppConfigContext) AbstractRS
}
class AbstractPaymentService {
+configContextQueryService ConfigContextQueryService
+payMchNotifyService PayMchNotifyService
+payOrderService PayOrderService
}
class AlipayPaymentService
class WxpayPaymentService
class AliBarPaymentService
class AliJsapiPaymentService
class WxBarPaymentService
class WxJsapiPaymentService
IPaymentService <|-- AbstractPaymentService
AbstractPaymentService <|-- AlipayPaymentService
AbstractPaymentService <|-- WxpayPaymentService
AlipayPaymentService --> AliBarPaymentService : "通过PaywayUtil"
AlipayPaymentService --> AliJsapiPaymentService : "通过PaywayUtil"
WxpayPaymentService --> WxBarPaymentService : "通过PaywayUtil"
WxpayPaymentService --> WxJsapiPaymentService : "通过PaywayUtil"
```

**类图来源**  
- [IPaymentService.java](file://sys-payment/src/main/java/com/unipay/pay/channel/IPaymentService.java#L12-L30)
- [AlipayPaymentService.java](file://sys-payment/src/main/java/com/unipay/pay/channel/alipay/AlipayPaymentService.java#L14-L29)
- [WxpayPaymentService.java](file://sys-payment/src/main/java/com/unipay/pay/channel/wxpay/WxpayPaymentService.java#L14-L30)

### 动态服务调用

`PaywayUtil`工具类是实现动态服务调用的关键。它根据`wayCode`（如`ali_bar`）动态构造出对应的Spring Bean名称（如`AliBarPaymentService`），然后通过`SpringBeansUtil.getBean()`从Spring容器中获取该Bean的实例。这种方式避免了硬编码的if-else判断，使得系统可以轻松地通过添加新的实现类来支持新的支付方式。

**本节来源**  
- [PaywayUtil.java](file://sys-payment/src/main/java/com/unipay/pay/util/PaywayUtil.java#L1-L54)

## 不同支付方式的下单流程差异

不同的支付方式在下单流程上存在显著差异，主要体现在`payDataType`和`payData`的生成上。

### 支付宝支付流程

对于支付宝的PC网页支付（`ALI_PC`），`payDataType`为`REDIRECT`，`payData`是一个完整的HTML form表单。商户系统收到响应后，需要将此form表单输出到页面，用户提交表单后，浏览器会自动跳转到支付宝的收银台完成支付。

### 微信支付流程

对于微信JSAPI支付（`WX_JSAPI`），`payDataType`为`JSAPI`，`payData`是一个包含`prepay_id`的JSON字符串。商户系统需要将此`prepay_id`传递给前端的微信JS-SDK，调用`wx.requestPayment()`方法来拉起微信支付。

### 聚合收银台流程

对于聚合收银台（`QR_CASHIER`），`payDataType`为`CODE_IMG_URL`，`payData`是一个二维码图片的URL。系统会生成一个包含所有可用支付方式的二维码，用户扫码后进入收银台页面，自行选择支付方式完成支付。

**本节来源**  
- [UnifiedOrderRS.java](file://sys-payment/src/main/java/com/unipay/pay/rqrs/payorder/UnifiedOrderRS.java#L42-L49)
- [AlipayPaymentService.java](file://sys-payment/src/main/java/com/unipay/pay/channel/alipay/AlipayPaymentService.java#L14-L29)
- [WxpayPaymentService.java](file://sys-payment/src/main/java/com/unipay/pay/channel/wxpay/WxpayPaymentService.java#L14-L30)

## 订单状态管理与超时机制

系统通过`PayOrder`实体类管理订单的全生命周期状态，并通过后台任务处理超时订单。

### 订单状态流转

订单状态主要经历以下几个阶段：
1.  **INIT (初始化)**：订单创建成功，等待支付。
2.  **ING (支付中)**：已调用支付渠道API，等待渠道返回结果。
3.  **SUCCESS (成功)**：支付成功，资金已到账。
4.  **FAIL (失败)**：支付失败，交易终止。

状态流转由`PayOrderProcessService`中的`updateIngAndSuccessOrFailByCreatebyOrder()`方法控制。该方法在一个事务中，先调用`payOrderService.updateInit2Ing()`将订单从`INIT`更新为`ING`，再根据渠道返回结果调用`payOrderService.updateIng2SuccessOrFail()`更新为`SUCCESS`或`FAIL`。

```mermaid
stateDiagram-v2
[*] --> INIT
INIT --> ING : 调用支付API
ING --> SUCCESS : 支付成功
ING --> FAIL : 支付失败
SUCCESS --> [*]
FAIL --> [*]
```

**状态图来源**  
- [PayOrderProcessService.java](file://sys-payment/src/main/java/com/unipay/pay/service/PayOrderProcessService.java#L80-L95)
- [PayOrderService.java](file://service/src/main/java/com/unipay/service/impl/PayOrderService.java#L47-L62)

### 超时机制

系统通过`PayOrderExpiredTask`定时任务扫描数据库中状态为`INIT`且已超过`expiredTime`的订单，并将其状态更新为`FAIL`。这可以防止订单长时间占用系统资源，并及时释放库存等业务资源。

**本节来源**  
- [PayOrderProcessService.java](file://sys-payment/src/main/java/com/unipay/pay/service/PayOrderProcessService.java#L80-L95)

## 异常处理策略

系统采用分层的异常处理策略，确保任何环节的异常都能被妥善处理。

### 业务异常处理

系统定义了`BizException`作为业务异常的基类。在参数校验、商户认证、支付方式校验等环节，一旦发现非法请求，立即抛出`BizException`。该异常会被全局异常处理器`BizExceptionResolver`捕获，并转换为标准的API响应（如`ApiRes.error()`），返回给商户。

### 支付成功确认

当支付渠道返回支付成功的结果时，系统会调用`PayOrderProcessService.confirmSuccess()`方法。该方法首先查询最新的订单详情，然后设置订单状态为`SUCCESS`，接着处理自动分账逻辑（如果启用），最后调用`PayMchNotifyService.payOrderNotify()`向商户的`notifyUrl`发送异步通知。

### 异步通知机制

`PayMchNotifyService.payOrderNotify()`方法负责向商户发送支付结果通知。它首先检查商户是否配置了`notifyUrl`，然后创建一条`MchNotifyRecord`记录，并通过消息队列（MQ）异步发送通知。这种设计保证了主支付流程的高效性，即使通知发送失败也不会影响支付结果。

**本节来源**  
- [PayOrderProcessService.java](file://sys-payment/src/main/java/com/unipay/pay/service/PayOrderProcessService.java#L30-L44)
- [PayMchNotifyService.java](file://sys-payment/src/main/java/com/unipay/pay/service/PayMchNotifyService.java#L35-L83)