# 渠道集成

<cite>
**本文档引用的文件**   
- [AbstractPaymentService.java](file://sys-payment\src\main\java\com\unipay\pay\channel\AbstractPaymentService.java)
- [IPaymentService.java](file://sys-payment\src\main\java\com\unipay\pay\channel\IPaymentService.java)
- [AlipayPaymentService.java](file://sys-payment\src\main\java\com\unipay\pay\channel\alipay\AlipayPaymentService.java)
- [WxpayPaymentService.java](file://sys-payment\src\main\java\com\unipay\pay\channel\wxpay\WxpayPaymentService.java)
- [PaywayUtil.java](file://sys-payment\src\main\java\com\unipay\pay\util\PaywayUtil.java)
- [ConfigContextQueryService.java](file://sys-payment\src\main\java\com\unipay\pay\service\ConfigContextQueryService.java)
- [SysConfigService.java](file://service\src\main\java\com\unipay\service\impl\SysConfigService.java)
- [MchPayPassage.java](file://core\src\main\java\com\unipay\core\entity\MchPayPassage.java)
- [PayInterfaceConfig.java](file://core\src\main\java\com\unipay\core\entity\PayInterfaceConfig.java)
- [AbstractPayOrderController.java](file://sys-payment\src\main\java\com\unipay\pay\ctrl\payorder\AbstractPayOrderController.java)
</cite>

## 目录
1. [引言](#引言)
2. [架构设计](#架构设计)
3. [核心组件分析](#核心组件分析)
4. [支付渠道实现](#支付渠道实现)
5. [配置与动态启用](#配置与动态启用)
6. [新增支付渠道指南](#新增支付渠道指南)
7. [结论](#结论)

## 引言
本文档详细阐述了系统如何通过策略模式和工厂模式集成多个第三方支付渠道。重点分析了AlipayPaymentService和WxpayPaymentService的实现，包括API调用、签名验证、结果解析等公共逻辑的抽象（AbstractPaymentService）。同时，解释了如何通过配置动态启用或禁用某个支付渠道，并为开发者提供添加新支付渠道（如银联、PayPal）的完整指南，包括需要实现的接口、配置项以及测试方法。

## 架构设计
系统采用策略模式和工厂模式来集成多个第三方支付渠道。通过定义统一的接口`IPaymentService`，每个支付渠道实现该接口，从而实现了支付逻辑的解耦。工厂模式通过`PaywayUtil`类动态获取具体的支付服务实例，确保了系统的灵活性和可扩展性。

```mermaid
classDiagram
class IPaymentService {
+String getIfCode()
+boolean isSupport(String wayCode)
+String preCheck(UnifiedOrderRQ bizRQ, PayOrder payOrder)
+String customPayOrderId(UnifiedOrderRQ bizRQ, PayOrder payOrder, MchAppConfigContext mchAppConfigContext)
+AbstractRS pay(UnifiedOrderRQ bizRQ, PayOrder payOrder, MchAppConfigContext mchAppConfigContext) throws Exception
}
class AbstractPaymentService {
+SysConfigService sysConfigService
+ChannelCertConfigKitBean channelCertConfigKitBean
+ConfigContextQueryService configContextQueryService
+String customPayOrderId(UnifiedOrderRQ bizRQ, PayOrder payOrder, MchAppConfigContext mchAppConfigContext)
+boolean isDivisionOrder(PayOrder payOrder)
+String getNotifyUrl()
+String getNotifyUrl(String payOrderId)
+String getReturnUrl()
+String getReturnUrl(String payOrderId)
+String getReturnUrlOnlyJump(String payOrderId)
}
class AlipayPaymentService {
+String getIfCode()
+boolean isSupport(String wayCode)
+String preCheck(UnifiedOrderRQ rq, PayOrder payOrder)
+AbstractRS pay(UnifiedOrderRQ rq, PayOrder payOrder, MchAppConfigContext mchAppConfigContext) throws Exception
}
class WxpayPaymentService {
+String getIfCode()
+boolean isSupport(String wayCode)
+String preCheck(UnifiedOrderRQ rq, PayOrder payOrder)
+AbstractRS pay(UnifiedOrderRQ rq, PayOrder payOrder, MchAppConfigContext mchAppConfigContext) throws Exception
+WxPayUnifiedOrderRequest buildUnifiedOrderRequest(PayOrder payOrder, MchAppConfigContext mchAppConfigContext)
+WxpayV3OrderRequestModel buildV3OrderRequestModel(PayOrder payOrder, MchAppConfigContext mchAppConfigContext)
}
IPaymentService <|-- AbstractPaymentService : "extends"
AbstractPaymentService <|-- AlipayPaymentService : "extends"
AbstractPaymentService <|-- WxpayPaymentService : "extends"
```

**图源**
- [IPaymentService.java](file://sys-payment\src\main\java\com\unipay\pay\channel\IPaymentService.java)
- [AbstractPaymentService.java](file://sys-payment\src\main\java\com\unipay\pay\channel\AbstractPaymentService.java)
- [AlipayPaymentService.java](file://sys-payment\src\main\java\com\unipay\pay\channel\alipay\AlipayPaymentService.java)
- [WxpayPaymentService.java](file://sys-payment\src\main\java\com\unipay\pay\channel\wxpay\WxpayPaymentService.java)

## 核心组件分析
### IPaymentService 接口
`IPaymentService`接口定义了所有支付服务必须实现的方法，包括获取接口代码、检查是否支持特定支付方式、前置检查、自定义支付订单号以及调用支付接口并返回响应数据。

### AbstractPaymentService 抽象类
`AbstractPaymentService`抽象类实现了`IPaymentService`接口，提供了通用的支付逻辑，如生成通知URL、返回URL等。它还包含了处理分账订单的逻辑。

### AlipayPaymentService 实现
`AlipayPaymentService`类继承自`AbstractPaymentService`，实现了支付宝支付的具体逻辑。它通过`PaywayUtil`类动态获取具体的支付方式服务实例，并调用相应的支付接口。

### WxpayPaymentService 实现
`WxpayPaymentService`类同样继承自`AbstractPaymentService`，实现了微信支付的具体逻辑。它支持微信支付的V2和V3版本，并根据配置动态选择合适的API版本。

**节源**
- [IPaymentService.java](file://sys-payment\src\main\java\com\unipay\pay\channel\IPaymentService.java)
- [AbstractPaymentService.java](file://sys-payment\src\main\java\com\unipay\pay\channel\AbstractPaymentService.java)
- [AlipayPaymentService.java](file://sys-payment\src\main\java\com\unipay\pay\channel\alipay\AlipayPaymentService.java)
- [WxpayPaymentService.java](file://sys-payment\src\main\java\com\unipay\pay\channel\wxpay\WxpayPaymentService.java)

## 支付渠道实现
### 支付宝支付实现
支付宝支付通过`AlipayPaymentService`类实现。该类通过`PaywayUtil.getRealPaywayService`方法动态获取具体的支付方式服务实例，并调用相应的支付接口。例如，对于PC端支付，使用`AliPc`类；对于移动端支付，使用`AliApp`类。

### 微信支付实现
微信支付通过`WxpayPaymentService`类实现。该类支持微信支付的V2和V3版本。对于V2版本，使用`WxJsapi`、`WxH5`等类；对于V3版本，使用`WxJsapiV3`、`WxH5V3`等类。这些类通过`PaywayUtil.getRealPaywayService`和`PaywayUtil.getRealPaywayV3Service`方法动态获取具体的支付方式服务实例。

**节源**
- [AlipayPaymentService.java](file://sys-payment\src\main\java\com\unipay\pay\channel\alipay\AlipayPaymentService.java)
- [WxpayPaymentService.java](file://sys-payment\src\main\java\com\unipay\pay\channel\wxpay\WxpayPaymentService.java)

## 配置与动态启用
### 配置管理
系统通过`SysConfigService`类管理配置信息。`IS_USE_CACHE`字段控制是否启用缓存，`APPLICATION_CONFIG`字段存储数据库application配置参数。通过`getDBApplicationConfig`方法获取实际的配置数据。

### 动态启用
支付渠道的启用和禁用通过`MchPayPassage`表中的`state`字段控制。当`state`为1时，表示启用；为0时，表示禁用。`MchPayPassageService`类提供了`findMchPayPassage`方法，根据商户号、应用ID和支付方式查询可用的支付接口。

```mermaid
flowchart TD
Start([开始]) --> CheckConfig["检查配置"]
CheckConfig --> ConfigValid{"配置有效?"}
ConfigValid --> |否| ReturnError["返回错误"]
ConfigValid --> |是| CheckState["检查状态"]
CheckState --> StateEnabled{"状态启用?"}
StateEnabled --> |否| ReturnDisabled["返回禁用"]
StateEnabled --> |是| CallPayment["调用支付接口"]
CallPayment --> End([结束])
```

**图源**
- [SysConfigService.java](file://service\src\main\java\com\unipay\service\impl\SysConfigService.java)
- [MchPayPassage.java](file://core\src\main\java\com\unipay\core\entity\MchPayPassage.java)
- [MchPayPassageService.java](file://service\src\main\java\com\unipay\service\impl\MchPayPassageService.java)

## 新增支付渠道指南
### 实现接口
新增支付渠道需要实现`IPaymentService`接口，并继承`AbstractPaymentService`抽象类。具体实现类需要重写`getIfCode`、`isSupport`、`preCheck`和`pay`方法。

### 配置项
在`PayInterfaceConfig`表中添加新的支付接口配置，包括接口代码、接口配置参数、接口费率和状态。确保`infoType`、`infoId`、`ifCode`和`state`字段正确设置。

### 测试方法
编写单元测试，验证新增支付渠道的功能。测试应包括前置检查、支付调用、结果解析等环节。确保所有异常情况都能被正确处理。

**节源**
- [IPaymentService.java](file://sys-payment\src\main\java\com\unipay\pay\channel\IPaymentService.java)
- [AbstractPaymentService.java](file://sys-payment\src\main\java\com\unipay\pay\channel\AbstractPaymentService.java)
- [PayInterfaceConfig.java](file://core\src\main\java\com\unipay\core\entity\PayInterfaceConfig.java)

## 结论
本文档详细介绍了系统如何通过策略模式和工厂模式集成多个第三方支付渠道。通过分析`AlipayPaymentService`和`WxpayPaymentService`的实现，展示了API调用、签名验证、结果解析等公共逻辑的抽象。此外，还解释了如何通过配置动态启用或禁用某个支付渠道，并为开发者提供了添加新支付渠道的完整指南。希望本文档能帮助开发者更好地理解和使用系统中的支付功能。