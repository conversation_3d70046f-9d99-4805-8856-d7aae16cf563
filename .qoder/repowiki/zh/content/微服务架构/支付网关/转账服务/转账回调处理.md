# 转账回调处理

<cite>
**本文档引用的文件**  
- [TransferNoticeController.java](file://sys-payment/src/main/java/com/unipay/pay/ctrl/transfer/TransferNoticeController.java)
- [PayMchNotifyService.java](file://sys-payment/src/main/java/com/unipay/pay/service/PayMchNotifyService.java)
- [PayOrderMchNotifyMQ.java](file://components/components-mq/src/main/java/com/unipay/components/mq/model/PayOrderMchNotifyMQ.java)
- [PayOrderMchNotifyMQReceiver.java](file://sys-payment/src/main/java/com/unipay/pay/mq/PayOrderMchNotifyMQReceiver.java)
- [ITransferNoticeService.java](file://sys-payment/src/main/java/com/unipay/pay/channel/ITransferNoticeService.java)
- [TransferOrder.java](file://core/src/main/java/com/unipay/core/entity/TransferOrder.java)
- [MchNotifyRecord.java](file://core/src/main/java/com/unipay/core/entity/MchNotifyRecord.java)
- [TransferOrderReissueTask.java](file://sys-payment/src/main/java/com/unipay/pay/task/TransferOrderReissueTask.java)
</cite>

## 目录
1. [转账回调处理机制概述](#转账回调处理机制概述)
2. [回调接收与验证流程](#回调接收与验证流程)
3. [通知消息格式与签名机制](#通知消息格式与签名机制)
4. [异步通知与消息队列设计](#异步通知与消息队列设计)
5. [重试机制与补单任务](#重试机制与补单任务)
6. [常见问题排查指南](#常见问题排查指南)

## 转账回调处理机制概述

转账回调处理机制是支付系统中确保第三方支付渠道转账结果准确通知商户的核心流程。该机制通过 `TransferNoticeController` 接收第三方支付渠道的转账结果通知，经过签名验证、数据解密等安全校验后，更新转账订单状态，并通过消息队列触发后续的商户通知流程。整个流程保证了交易状态的最终一致性，是支付系统可靠性的关键组成部分。

**Section sources**
- [TransferNoticeController.java](file://sys-payment/src/main/java/com/unipay/pay/ctrl/transfer/TransferNoticeController.java#L30-L128)
- [PayMchNotifyService.java](file://sys-payment/src/main/java/com/unipay/pay/service/PayMchNotifyService.java#L25-L260)

## 回调接收与验证流程

`TransferNoticeController` 是处理第三方转账回调的核心控制器。它通过 `/api/transfer/notify/{ifCode}` 和 `/api/transfer/notify/{ifCode}/{transferId}` 两个端点接收来自不同支付渠道（由 `ifCode` 标识）的异步通知。

处理流程如下：
1.  **参数校验**：首先校验 `ifCode` 是否为空。
2.  **服务定位**：根据 `ifCode` 动态获取对应的 `ITransferNoticeService` 实现类（如 `WxpayTransferNoticeService` 或 `AlipayTransferNoticeService`）。
3.  **参数解析**：调用 `parseParams` 方法解析 HTTP 请求，提取出转账订单号（`transferId`）和原始参数。
4.  **订单查询**：根据 `transferId` 查询 `TransferOrder` 订单实体。
5.  **配置加载**：通过 `ConfigContextQueryService` 查询并加载商户应用的配置信息 `MchAppConfigContext`。
6.  **业务处理**：调用 `doNotice` 方法进行核心的业务处理，包括签名验证、数据解密和状态判断。

```mermaid
sequenceDiagram
participant 渠道 as 第三方支付渠道
participant 控制器 as TransferNoticeController
participant 服务 as ITransferNoticeService
participant 订单服务 as TransferOrderService
participant 通知服务 as PayMchNotifyService
渠道->>控制器 : POST /api/transfer/notify/{ifCode}
控制器->>控制器 : 校验 ifCode
控制器->>控制器 : 获取 ITransferNoticeService 实例
控制器->>服务 : parseParams(request)
服务-->>控制器 : 返回 transferId 和 params
控制器->>订单服务 : getById(transferId)
订单服务-->>控制器 : TransferOrder
控制器->>控制器 : queryMchInfoAndAppInfo()
控制器->>服务 : doNotice(request, params, order, config)
服务-->>控制器 : ChannelRetMsg
控制器->>订单服务 : updateIng2Success/Fail()
控制器->>通知服务 : transferOrderNotify()
通知服务-->>控制器 : 通知已入队
控制器-->>渠道 : 返回响应
```

**Diagram sources**
- [TransferNoticeController.java](file://sys-payment/src/main/java/com/unipay/pay/ctrl/transfer/TransferNoticeController.java#L40-L126)
- [ITransferNoticeService.java](file://sys-payment/src/main/java/com/unipay/pay/channel/ITransferNoticeService.java#L23-L30)

**Section sources**
- [TransferNoticeController.java](file://sys-payment/src/main/java/com/unipay/pay/ctrl/transfer/TransferNoticeController.java#L40-L126)
- [ITransferNoticeService.java](file://sys-payment/src/main/java/com/unipay/pay/channel/ITransferNoticeService.java#L23-L30)

## 通知消息格式与签名机制

当转账状态发生变更（成功或失败）时，系统会向商户配置的 `notifyUrl` 发送异步通知。通知消息以 GET 请求的形式发送，所有参数附加在 URL 查询字符串中。

### 通知消息格式
通知消息包含 `TransferOrder` 订单的详细信息以及一个签名字段 `sign`。关键字段包括：
- `transferId`: 系统生成的转账订单号
- `mchOrderNo`: 商户订单号
- `state`: 转账状态（1-转账中, 2-转账成功, 3-转账失败）
- `amount`: 转账金额（单位：分）
- `channelOrderNo`: 支付渠道订单号
- `reqTime`: 请求时间戳（毫秒）
- `sign`: 签名字符串

### 签名机制
为确保通知消息的完整性和来源可信，系统采用 MD5 签名算法。签名过程如下：
1.  将除 `sign` 外的所有通知参数按参数名进行字典序升序排列。
2.  将排序后的参数名和值用 `=` 连接，再用 `&` 连接所有参数，形成待签名字符串。
3.  在待签名字符串末尾拼接 `key=商户AppSecret`。
4.  对最终字符串进行 MD5 哈希计算，并将结果转换为大写，得到 `sign` 值。

商户在接收到通知后，应使用相同的算法和自己的 `AppSecret` 重新计算签名，并与通知中的 `sign` 值进行比对，以验证消息的合法性。

```mermaid
flowchart TD
A[开始] --> B[获取订单数据 TransferOrder]
B --> C[构建 QueryTransferOrderRS 响应对象]
C --> D[转换为 JSONObject]
D --> E[添加 reqTime 时间戳]
E --> F[对 JSONObject 进行字典序排序]
F --> G[拼接 "key=AppSecret"]
G --> H[执行 MD5 哈希]
H --> I[转换为大写]
I --> J[生成最终 sign]
J --> K[拼接到 notifyUrl]
K --> L[发送通知]
```

**Diagram sources**
- [PayMchNotifyService.java](file://sys-payment/src/main/java/com/unipay/pay/service/PayMchNotifyService.java#L225-L236)
- [JeepayKit.java](file://core/src/main/java/com/unipay/core/utils/JeepayKit.java#L48-L68)

**Section sources**
- [PayMchNotifyService.java](file://sys-payment/src/main/java/com/unipay/pay/service/PayMchNotifyService.java#L225-L236)
- [JeepayKit.java](file://core/src/main/java/com/unipay/core/utils/JeepayKit.java#L48-L68)

## 异步通知与消息队列设计

为了解耦核心支付流程与耗时的HTTP通知操作，系统采用消息队列（MQ）进行异步通信。

### 核心组件
- **`PayMchNotifyService`**: 该服务负责处理通知的持久化和入队。当转账订单状态变为成功或失败时，`transferOrderNotify` 方法会被调用。
- **`MchNotifyRecord`**: 该实体代表一条待发送的商户通知记录，存储了通知的URL、重试次数、状态等信息。
- **`PayOrderMchNotifyMQ`**: 这是一个消息模型类，定义了消息队列的名称 `QUEUE_PAY_ORDER_MCH_NOTIFY` 和消息体 `MsgPayload`，其中 `notifyId` 是 `MchNotifyRecord` 的主键。
- **`PayOrderMchNotifyMQReceiver`**: 这是一个消息队列的消费者，实现了 `IMQReceiver` 接口，负责从队列中取出消息并执行实际的HTTP通知。

### 处理流程
1.  `PayMchNotifyService` 创建一条 `MchNotifyRecord` 记录并保存到数据库，状态为“通知中”。
2.  将 `MchNotifyRecord` 的 `notifyId` 封装成 `PayOrderMchNotifyMQ` 消息，并通过 `IMQSender` 发送到消息队列。
3.  `PayOrderMchNotifyMQReceiver` 监听队列，接收到消息后，根据 `notifyId` 查询 `MchNotifyRecord`。
4.  执行HTTP POST请求，向商户的 `notifyUrl` 发送通知。
5.  根据HTTP响应结果更新 `MchNotifyRecord` 的状态。如果失败且未达到最大重试次数，则将消息重新发送到队列，并设置延迟时间。

```mermaid
classDiagram
class PayMchNotifyService {
+transferOrderNotify(TransferOrder)
+createNotifyUrl(TransferOrder, String)
}
class MchNotifyRecord {
+Long notifyId
+String orderId
+String notifyUrl
+Integer notifyCount
+Byte state
}
class PayOrderMchNotifyMQ {
+String MQ_NAME
+MsgPayload payload
+build(Long notifyId)
}
class PayOrderMchNotifyMQReceiver {
+receive(MsgPayload payload)
}
PayMchNotifyService --> MchNotifyRecord : 创建并保存
PayMchNotifyService --> PayOrderMchNotifyMQ : 构建并发送
PayOrderMchNotifyMQ --> PayOrderMchNotifyMQReceiver : 消费
PayOrderMchNotifyMQReceiver --> MchNotifyRecord : 更新状态
```

**Diagram sources**
- [PayMchNotifyService.java](file://sys-payment/src/main/java/com/unipay/pay/service/PayMchNotifyService.java#L138-L185)
- [PayOrderMchNotifyMQ.java](file://components/components-mq/src/main/java/com/unipay/components/mq/model/PayOrderMchNotifyMQ.java#L16-L68)
- [PayOrderMchNotifyMQReceiver.java](file://sys-payment/src/main/java/com/unipay/pay/mq/PayOrderMchNotifyMQReceiver.java#L23-L98)
- [MchNotifyRecord.java](file://core/src/main/java/com/unipay/core/entity/MchNotifyRecord.java#L24-L140)

**Section sources**
- [PayMchNotifyService.java](file://sys-payment/src/main/java/com/unipay/pay/service/PayMchNotifyService.java#L138-L185)
- [PayOrderMchNotifyMQ.java](file://components/components-mq/src/main/java/com/unipay/components/mq/model/PayOrderMchNotifyMQ.java#L16-L68)
- [PayOrderMchNotifyMQReceiver.java](file://sys-payment/src/main/java/com/unipay/pay/mq/PayOrderMchNotifyMQReceiver.java#L23-L98)

## 重试机制与补单任务

为了应对网络波动或商户服务器临时不可用的情况，系统设计了完善的重试和补单机制。

### 通知重试机制
`PayOrderMchNotifyMQReceiver` 在发送HTTP通知失败后，会启动重试逻辑：
- **最大重试次数**：默认为6次，由 `MchNotifyRecord` 的 `notifyCountLimit` 字段控制。
- **延迟策略**：采用递增延迟策略。每次重试的延迟时间（秒）为 `当前通知次数 * 30`。例如，第一次重试延迟30秒，第二次延迟60秒，以此类推。
- **终止条件**：当重试次数达到上限时，通知状态将被标记为“失败”，不再进行后续重试。

### 补单任务
`TransferOrderReissueTask` 是一个定时任务，每分钟执行一次，用于处理状态为“转账中”（`STATE_ING`）的订单。
- **目的**：防止因第三方支付渠道未发送回调或回调丢失，导致订单状态长期滞留。
- **流程**：任务会查询所有“转账中”且创建时间在一天内的订单，然后调用对应支付渠道的查询接口（`query` 方法）主动获取订单的最新状态。
- **状态更新**：根据查询结果，如果订单已成功或失败，则更新本地订单状态，并触发 `PayMchNotifyService` 发送通知。

```mermaid
flowchart TB
A[PayOrderMchNotifyMQReceiver 接收消息] --> B{通知成功?}
B --> |是| C[更新状态为成功]
B --> |否| D{重试次数 < 上限?}
D --> |是| E[计算延迟时间: count * 30s]
E --> F[延迟发送MQ消息]
F --> A
D --> |否| G[更新状态为失败]
H[TransferOrderReissueTask 定时任务] --> I[查询所有 STATE_ING 订单]
I --> J[调用渠道 query 接口]
J --> K{订单状态已确定?}
K --> |是| L[更新本地订单状态]
L --> M[触发 transferOrderNotify]
K --> |否| N[保持 STATE_ING 状态]
```

**Section sources**
- [PayOrderMchNotifyMQReceiver.java](file://sys-payment/src/main/java/com/unipay/pay/mq/PayOrderMchNotifyMQReceiver.java#L34-L97)
- [TransferOrderReissueTask.java](file://sys-payment/src/main/java/com/unipay/pay/task/TransferOrderReissueTask.java#L10-L68)
- [TransferOrderReissueService.java](file://sys-payment/src/main/java/com/unipay/pay/service/TransferOrderReissueService.java#L67-L98)

## 常见问题排查指南

当转账回调处理出现问题时，可参考以下指南进行排查：

1.  **回调未收到**:
    - **检查日志**：在 `TransferNoticeController` 的日志中搜索 `进入[xxx]转账回调`，确认是否有请求到达。
    - **网络问题**：检查商户服务器防火墙或安全组是否放行了支付系统的出口IP。
    - **URL配置**：确认商户在系统中配置的 `notifyUrl` 是否正确无误。

2.  **签名验证失败**:
    - **AppSecret**：核对商户的 `AppSecret` 是否与系统中配置的一致。
    - **签名算法**：确保商户端使用的签名算法（如MD5）和排序规则与文档描述完全一致。
    - **参数处理**：注意 `sign` 字段不应参与签名计算，且参数值应进行URL解码后再排序。

3.  **通知重试次数过多**:
    - **商户服务器**：检查商户的 `notifyUrl` 接口是否能正常响应，响应内容必须是纯文本 `SUCCESS`（不区分大小写）。
    - **超时问题**：确保商户接口能在20秒内完成处理并返回，避免因超时导致通知失败。

4.  **订单状态不更新**:
    - **补单任务**：如果长时间未收到回调，检查 `TransferOrderReissueTask` 是否正常运行，其日志中是否有查询记录。
    - **渠道问题**：登录第三方支付渠道的商户后台，确认该笔转账的真实状态，以判断是渠道未回调还是系统处理问题。

5.  **数据库记录异常**:
    - **MchNotifyRecord**：查询 `t_mch_notify_record` 表，检查 `notifyId` 对应的记录，查看 `notifyCount`、`state` 和 `resResult` 字段，了解通知的详细执行情况。

**Section sources**
- [TransferNoticeController.java](file://sys-payment/src/main/java/com/unipay/pay/ctrl/transfer/TransferNoticeController.java#L40-L126)
- [PayOrderMchNotifyMQReceiver.java](file://sys-payment/src/main/java/com/unipay/pay/mq/PayOrderMchNotifyMQReceiver.java#L34-L97)
- [TransferOrderReissueTask.java](file://sys-payment/src/main/java/com/unipay/pay/task/TransferOrderReissueTask.java#L10-L68)
- [MchNotifyRecord.java](file://core/src/main/java/com/unipay/core/entity/MchNotifyRecord.java#L24-L140)