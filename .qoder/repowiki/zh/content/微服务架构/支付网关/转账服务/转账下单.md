
# 转账下单

<cite>
**本文档引用文件**   
- [TransferOrderController.java](file://sys-payment/src/main/java/com/unipay/pay/ctrl/transfer/TransferOrderController.java)
- [TransferOrder.java](file://core/src/main/java/com/unipay/core/entity/TransferOrder.java)
- [TransferOrderRQ.java](file://sys-payment/src/main/java/com/unipay/pay/rqrs/transfer/TransferOrderRQ.java)
- [TransferOrderRS.java](file://sys-payment/src/main/java/com/unipay/pay/rqrs/transfer/TransferOrderRS.java)
- [TransferOrderService.java](file://service/src/main/java/com/unipay/service/impl/TransferOrderService.java)
- [ApiController.java](file://sys-payment/src/main/java/com/unipay/pay/ctrl/ApiController.java)
- [PayMchNotifyService.java](file://sys-payment/src/main/java/com/unipay/pay/service/PayMchNotifyService.java)
- [ChannelRetMsg.java](file://sys-payment/src/main/java/com/unipay/pay/rqrs/msg/ChannelRetMsg.java)
</cite>

## 目录
1. [转账下单流程概述](#转账下单流程概述)
2. [TransferOrderController实现逻辑](#transferordercontroller实现逻辑)
3. [TransferOrder实体类详解](#transferorder实体类详解)
4. [统一下单接口调用示例](#统一下单接口调用示例)
5. [返回结果结构与处理](#返回结果结构与处理)
6. [与支付下单流程对比](#与支付下单流程对比)
7. [资金安全校验要求](#资金安全校验要求)

## 转账下单流程概述

转账下单功能是统一支付系统中的重要组成部分，用于处理商户向指定收款方发起资金转账的业务场景。该功能通过统一下单接口接收转账请求，经过参数校验、签名验证、订单创建等环节后，调用上游支付通道完成实际转账操作，并通过异步通知机制将结果反馈给商户。

整个流程涉及多个核心组件的协同工作，包括控制器层的请求处理、服务层的业务逻辑实现、数据访问层的持久化操作以及消息队列的异步通知机制。系统设计充分考虑了资金安全性和交易可靠性，通过严格的参数校验和多重安全验证确保每一笔转账交易的合法性和安全性。

**Section sources**
- [TransferOrderController.java](file://sys-payment/src/main/java/com/unipay/pay/ctrl/transfer/TransferOrderController.java)
- [TransferOrder.java](file://core/src/main/java/com/unipay/core/entity/TransferOrder.java)

## TransferOrderController实现逻辑

转账下单的核心实现位于`TransferOrderController`类中，该控制器负责处理转账请求的完整生命周期。实现逻辑主要包括参数校验、订单创建、签名验证和上游接口调用等关键步骤。

### 参数校验流程

系统首先通过`getRQByWithMchSign`方法获取并验证请求参数。该方法继承自`ApiController`基类，实现了通用的参数验证机制。参数校验分为两个层次：基础字段验证和业务逻辑验证。

基础字段验证通过JSR-303注解实现，确保必填字段不为空且符合格式要求。例如，商户订单号、支付接口代码、入账方式等字段都标记了`@NotBlank`注解，金额字段则使用`@NotNull`和`@Min`注解确保数值有效性。

业务逻辑验证在控制器方法内部实现，主要包括：
- 检查商户订单号是否重复
- 验证异步通知地址的协议合法性
- 确认商户配置信息的可用性
- 验证支付接口是否已开通

```mermaid
flowchart TD
Start([开始]) --> ValidateParams["验证基础参数"]
ValidateParams --> CheckDuplicate["检查订单号重复"]
CheckDuplicate --> ValidateNotifyUrl["验证通知地址"]
ValidateNotifyUrl --> QueryMchConfig["查询商户配置"]
QueryMchConfig --> CheckIfCode["验证接口开通"]
CheckIfCode --> CheckSupport["验证入账方式支持"]
CheckSupport --> End([参数校验完成])
```

**Diagram sources**
- [TransferOrderController.java](file://sys-payment/src/main/java/com/unipay/pay/ctrl/transfer/TransferOrderController.java)
- [ApiController.java](file://sys-payment/src/main/java/com/unipay/pay/ctrl/ApiController.java)

### 订单创建流程

订单创建过程通过`genTransferOrder`私有方法实现。该方法接收请求参数和商户配置信息，生成完整的转账订单对象。订单创建的关键步骤包括：

1. 生成唯一转账订单号，使用`SeqKit.genTransferId()`方法确保全局唯一性
2. 填充商户基本信息，包括商户号、服务商号、应用ID等
3. 设置订单核心参数，如转账金额、收款账号、银行名称等
4. 初始化订单状态为"订单生成"状态
5. 记录创建时间戳

订单创建完成后，系统将其持久化到数据库中，为后续的转账操作提供数据支持。

### 签名验证机制

签名验证是保障交易安全的核心环节。系统采用基于商户密钥的MD5签名算法，确保请求数据的完整性和真实性。验证流程如下：

1. 从请求参数中提取签名值
2. 查询商户应用的密钥信息
3. 将除签名外的所有参数按字典序排序并拼接
4. 在拼接字符串末尾添加密钥
5. 计算MD5哈希值并与原始签名比对

签名验证失败将直接拒绝请求，返回"验签失败"错误信息，有效防止恶意篡改和重放攻击。

```mermaid
sequenceDiagram
participant Client as "商户系统"
participant Controller as "TransferOrderController"
participant Service as "TransferOrderService"
participant Channel as "支付通道"
Client->>Controller : 发送转账请求
Controller->>Controller : 参数校验
Controller->>Controller : 签名验证
Controller->>Service : 创建转账订单
Service->>Service : 生成订单号
Service->>Service : 持久化订单
Controller->>Channel : 调用上游接口
Channel-->>Controller : 返回渠道响应
Controller->>Service : 更新订单状态
Controller-->>Client : 返回处理结果
```

**Diagram sources**
- [TransferOrderController.java](file://sys-payment/src/main/java/com/unipay/pay/ctrl/transfer/TransferOrderController.java)
- [TransferOrderService.java](file://service/src/main/java/com/unipay/service/impl/TransferOrderService.java)

**Section sources**
- [TransferOrderController.java](file://sys-payment/src/main/java/com/unipay/pay/ctrl/transfer/TransferOrderController.java)
- [ApiController.java](file://sys-payment/src/main/java/com/unipay/pay/ctrl/ApiController.java)

## TransferOrder实体类详解

`TransferOrder`实体类定义了转账订单的核心数据结构，包含订单基本信息、资金信息、收款方信息和状态信息等多个维度的字段。

### 关键字段说明

| 字段名称 | 字段类型 | 业务含义 | 示例值 |
|---------|--------|---------|-------|
| transferId | String | 转账订单号，系统生成的唯一标识 | T20231201000001 |
| mchNo | String | 商户号，发起转账的商户标识 | MCH20230001 |
| appId | String | 应用ID，商户应用的唯一标识 | APP20230001 |
| mchOrderNo | String | 商户订单号，商户系统生成的订单编号 | ORD20231201001 |
| ifCode | String | 支付接口代码，指定使用的支付通道 | ALIPAY |
| entryType | String | 入账方式，指定资金到账方式 | BANK_CARD |
| amount | Long | 转账金额，单位为分 | 10000（表示100元） |
| currency | String | 三位货币代码 | CNY |
| accountNo | String | 收款账号 | **************** |
| accountName | String | 收款人姓名 | 张三 |
| bankName | String | 收款人开户行名称 | 中国工商银行 |
| transferDesc | String | 转账备注信息 | 商品采购款 |
| state | Byte | 支付状态 | 0（订单生成） |

### 入账方式枚举

系统定义了三种主要的入账方式，通过静态常量进行管理：

- **WX_CASH**: 微信零钱，资金直接转入收款方微信钱包
- **ALIPAY_CASH**: 支付宝转账，资金转入收款方支付宝账户
- **BANK_CARD**: 银行卡，资金转入指定的银行账户

### 订单状态定义

订单状态通过字节类型常量定义，确保存储效率和查询性能：

- **STATE_INIT (0)**: 订单生成，初始状态
- **STATE_ING (1)**: 转账中，正在处理转账请求
- **STATE_SUCCESS (2)**: 转账成功，资金已成功转出
- **STATE_FAIL (3)**: 转账失败，转账操作未成功
- **STATE_CLOSED (4)**: 订单关闭，订单被取消或过期

### 业务扩展字段

实体类还包含多个扩展字段，支持业务的灵活扩展：

- **channelExtra**: 特定渠道发起的额外参数，用于传递渠道特定的配置
- **extParam**: 商户扩展参数，允许商户传递自定义业务数据
- **channelResData**: 渠道响应数据，存储上游支付通道的原始响应
- **notifyUrl**: 异步通知地址，用于回调通知商户系统

```mermaid
classDiagram
class TransferOrder {
+String transferId
+String mchNo
+String isvNo
+String appId
+String mchName
+Byte mchType
+String mchOrderNo
+String ifCode
+String entryType
+Long amount
+String currency
+String accountNo
+String accountName
+String bankName
+String transferDesc
+String clientIp
+Byte state
+String channelExtra
+String channelOrderNo
+String channelResData
+String errCode
+String errMsg
+String extParam
+String notifyUrl
+Date successTime
+Date createdAt
+Date updatedAt
+static final String ENTRY_WX_CASH
+static final String ENTRY_ALIPAY_CASH
+static final String ENTRY_BANK_CARD
+static final byte STATE_INIT
+static final byte STATE_ING
+static final byte STATE_SUCCESS
+static final byte STATE_FAIL
+static final byte STATE_CLOSED
+static LambdaQueryWrapper<TransferOrder> gw()
}
```

**Diagram sources**
- [TransferOrder.java](file://core/src/main/java/com/unipay/core/entity/TransferOrder.java)

**Section sources**
- [TransferOrder.java](file://core/src/main/java/com/unipay/core/entity/TransferOrder.java)

## 统一下单接口调用示例

统一下单接口通过HTTP POST请求方式提供服务，商户需要按照规定的参数格式发起请求。以下是典型的调用示例和参数说明。

### 请求参数结构

请求参数封装在`TransferOrderRQ`对象中，继承自`AbstractMchAppRQ`基类。核心参数包括：

- **基础信息**: 商户号(mchNo)、应用ID(appId)
- **订单信息**: 商户订单号(mchOrderNo)、支付接口代码(ifCode)
- **资金信息**: 转账金额(amount)、货币代码(currency)
- **收款信息**: 收款账号(accountNo)、收款人姓名(accountName)、开户行名称(bankName)
- **业务信息**: 入账方式(entryType)、转账备注(transferDesc)
- **安全信息**: 签名(sign)、签名类型(signType)、版本号(version)

### 调用代码示例

```java
// 构建请求参数
TransferOrderRQ request = new TransferOrderRQ();
request.setMchNo("MCH20230001");
request.setAppId("APP20230001");
request.setMchOrderNo("ORD20231201001");
request.setIfCode("ALIPAY");
request.setEntryType("BANK_CARD");
request.setAmount(10000L);
request.setCurrency("CNY");
request.setAccountNo("****************");
request.setAccountName("张三");
request.setBankName("中国工商银行");
request.setTransferDesc("商品采购款");
request.setNotifyUrl("https://merchant.com/notify");

// 设置安全参数
request.setVersion("1.0");
request.setSignType("MD5");

// 计算签名
String sign = JeepayKit.getSign(JSONObject.toJSON(request), "商户密钥");
request.setSign(sign);

// 发起HTTP请求
HttpClient client = HttpClient.newHttpClient();
HttpRequest httpRequest = HttpRequest.newBuilder()
    .uri(URI.create("https://api.payment.com/api/transferOrder"))
    .header("Content-Type", "application/json")
    .POST(HttpRequest.BodyPublishers.ofString(JSONObject.toJSONString(request)))
    .build();

HttpResponse<String> response = client.send(httpRequest, HttpResponse.BodyHandlers.ofString());
```

### 请求头要求

调用统一下单接口时，需要设置必要的HTTP请求头：

- **Content-Type**: application/json，指定请求体格式
- **Accept**: application/json，指定期望的响应格式
- **User-Agent**: 标识客户端信息
- **X-Request-ID**: 可选的请求追踪ID

### 错误处理

系统对各种异常情况提供了详细的错误码和错误信息，常见的错误场景包括：

- 参数缺失或格式错误
- 商户订单号重复
- 商户未开通指定支付接口
- 签名验证失败
- 商