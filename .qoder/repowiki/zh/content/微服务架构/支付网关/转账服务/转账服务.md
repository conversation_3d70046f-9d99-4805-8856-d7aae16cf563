
# 转账服务

<cite>
**本文档引用的文件**  
- [TransferOrderController.java](file://sys-payment/src/main/java/com/unipay/pay/ctrl/transfer/TransferOrderController.java)
- [ITransferService.java](file://sys-payment/src/main/java/com/unipay/pay/channel/ITransferService.java)
- [TransferNoticeController.java](file://sys-payment/src/main/java/com/unipay/pay/ctrl/transfer/TransferNoticeController.java)
- [TransferOrderReissueTask.java](file://sys-payment/src/main/java/com/unipay/pay/task/TransferOrderReissueTask.java)
- [TransferOrder.java](file://core/src/main/java/com/unipay/core/entity/TransferOrder.java)
- [TransferOrderService.java](file://service/src/main/java/com/unipay/service/impl/TransferOrderService.java)
- [TransferOrderReissueService.java](file://sys-payment/src/main/java/com/unipay/pay/service/TransferOrderReissueService.java)
- [ITransferNoticeService.java](file://sys-payment/src/main/java/com/unipay/pay/channel/ITransferNoticeService.java)
</cite>

## 目录
1. [引言](#引言)
2. [转账业务场景与流程](#转账业务场景与流程)
3. [核心组件分析](#核心组件分析)
4. [转账订单状态流转](#转账订单状态流转)
5. [异步结果通知机制](#异步结果通知机制)
6. [自动补单机制](#自动补单机制)
7. [与支付、退款服务的异同](#与支付、退款服务的异同)
8. [资金安全考虑](#资金安全考虑)
9. [总结](#总结)

## 引言

转账服务是统一支付平台中的关键功能模块，用于实现商户向用户或第三方账户的资金划转。该服务支持多种入账方式，包括微信零钱、支付宝转账和银行卡转账，并具备完整的订单管理、状态追踪、异步通知和自动补单机制。本文档详细解析转账服务的实现原理、核心组件交互以及在资金安全方面的设计考量。

## 转账业务场景与流程

转账服务主要应用于商户需要向用户发放佣金、退款、奖励或进行其他资金划转的场景。其核心流程如下：

1. **商户发起请求**：商户通过API接口发起转账请求，提供必要的参数，如商户号、应用ID、支付接口代码、收款账号、金额等。
2. **参数校验与验签**：系统首先对请求参数进行完整性校验和签名验证，确保请求的合法性和数据的完整性。
3. **商户配置检查**：验证商户是否已正确配置所选的支付接口。
4. **订单生成**：创建转账订单记录，状态初始化为“订单生成”（STATE_INIT）。
5. **调用支付渠道**：根据支付接口代码动态获取对应的`ITransferService`实现，调用其`transfer`方法发起实际的转账请求。
6. **状态更新与响应**：根据渠道返回结果更新订单状态，并向商户返回响应。

该流程在`TransferOrderController`的`transferOrder`方法中实现，确保了操作的原子性和事务性。

**Section sources**
- [TransferOrderController.java](file://sys-payment/src/main/java/com/unipay/pay/ctrl/transfer/TransferOrderController.java#L34-L236)

## 核心组件分析

### TransferOrderController

`TransferOrderController`是转账服务的API入口，负责接收和处理来自商户的转账请求。它继承自`ApiController`，并注入了`ConfigContextQueryService`、`TransferOrderService`、`PayInterfaceConfigService`和`PayMchNotifyService`等核心服务。

其核心方法`transferOrder`处理转账请求，主要步骤包括：
- 获取并验证请求参数。
- 查询商户配置信息。
- 验证商户是否已开通指定的支付接口。
- 动态获取`ITransferService`实现并调用。
- 根据渠道返回结果更新订单状态。
- 向商户返回签名后的响应。

此外，该控制器还提供了查询订单列表和详情的接口，供不同角色（管理员、商户、代理商）使用。

**Section sources**
- [TransferOrderController.java](file://sys-payment/src/main/java/com/unipay/pay/ctrl/transfer/TransferOrderController.java#L34-L236)

### ITransferService 接口与实现

`ITransferService`是转账服务的核心接口，定义了所有支付渠道必须实现的方法。该接口的设计遵循了策略模式，允许系统动态加载和调用不同的支付渠道实现。

```mermaid
classDiagram
class ITransferService {
<<interface>>
+getIfCode() String
+isSupport(entryType) boolean
+preCheck(bizRQ, transferOrder) String
+transfer(bizRQ, refundOrder, mchAppConfigContext) ChannelRetMsg
+query(transferOrder, mchAppConfigContext) ChannelRetMsg
}
class AbstractTransferService {
<<abstract>>
}
class WxTransferService {
+getIfCode() String
+isSupport(entryType) boolean
+preCheck(bizRQ, transferOrder) String
+transfer(bizRQ, refundOrder, mchAppConfigContext) ChannelRetMsg
+query(transferOrder, mchAppConfigContext) ChannelRetMsg
}
class AlipayTransferService {
+getIfCode() String
+isSupport(entryType) boolean
+preCheck(bizRQ, transferOrder) String
+transfer(bizRQ, refundOrder, mchAppConfigContext) ChannelRetMsg
+query(transferOrder, mchAppConfigContext) ChannelRetMsg
}
ITransferService <|-- AbstractTransferService
AbstractTransferService <|-- WxTransferService
AbstractTransferService <|-- AlipayTransferService
```

**Diagram sources**
- [ITransferService.java](file://sys-payment/src/main/java/com/unipay/pay/channel/ITransferService.java#L12-L29)

**Section sources**
- [ITransferService.java](file://sys-payment/src/main/java/com/unipay/pay/channel/ITransferService.java#L12-L29)

#### 接口方法说明

- **getIfCode()**: 返回该实现对应的支付接口代码（如`WX_V3`、`ALIPAY`），用于Spring Bean的名称匹配。
- **isSupport(String entryType)**: 判断该支付渠道是否支持指定的入账方式（如`WX_CASH`、`BANK_CARD`）。
- **preCheck(TransferOrderRQ bizRQ, TransferOrder transferOrder)**: 对请求参数和订单信息进行前置检查，返回错误信息或抛出异常。
- **transfer(TransferOrderRQ bizRQ, TransferOrder refundOrder, MchAppConfigContext mchAppConfigContext)**: 调用上游支付渠道的转账接口，并返回结果。
- **query(TransferOrder transferOrder, MchAppConfigContext mchAppConfigContext)**: 调用上游支付渠道的查询接口，用于补单任务。

系统通过`SpringBeansUtil.getBean(ifCode + "TransferService", ITransferService.class)`动态获取具体的实现类，实现了高度的可扩展性。

## 转账订单状态流转

转账订单的状态由`TransferOrder`实体类中的`state`字段表示，其状态流转是整个服务的核心逻辑。

```mermaid
stateDiagram-v2
[*] --> STATE_INIT
STATE_INIT --> STATE_ING : 调用渠道接口成功
STATE_INIT --> STATE_FAIL : 调用渠道接口失败
STATE_ING --> STATE_SUCCESS : 渠道回调或查询确认成功
STATE_ING --> STATE_FAIL : 渠道回调或查询确认失败
STATE_ING --> STATE_ING : 渠道返回处理中/未知状态
STATE_ING --> STATE_INIT : 系统异常
STATE_SUCCESS --> [*]
STATE_FAIL --> [*]
```

**Diagram sources**
- [TransferOrder.java](file://core/src/main/java/com/unipay/core/entity/TransferOrder.java#L22-L211)

**Section sources**
- [TransferOrder.java](file://core/src/main/java/com/unipay/core/entity/TransferOrder.java#L22-L211)

### 状态定义

- **STATE_INIT (0)**: 订单生成。订单创建后的初始状态。
- **STATE_ING (1)**: 转账中。已成功调用上游渠道接口，等待结果。
- **STATE_SUCCESS (2)**: 转账成功。已收到上游渠道的成功确认。
- **STATE_FAIL (3)**: 转账失败。已收到上游渠道的失败确认。
- **STATE_CLOSED (4)**: 订单关闭。订单被主动关闭。

### 状态流转逻辑

1. **初始状态**: 订单创建时，`state`被设置为`STATE_INIT`。
2. **发起转账**: 在`TransferOrderController`中，调用`ITransferService.transfer`方法。如果调用成功，订单状态会通过`updateInit2Ing`方法更新为`STATE_ING`。
3. **最终确认**:
   - 如果渠道返回明确成功（`CONFIRM_SUCCESS`），则通过`updateIng2Success`方法将状态更新为`STATE_SUCCESS`。
   - 如果渠道返回明确失败（`CONFIRM_FAIL`），则通过`updateIng2Fail`方法将状态更新为`STATE_FAIL`。
   - 如果渠道返回处理中、未知或接口错误，订单状态保持为`STATE_ING`，等待后续的异步通知或补单任务。
4. **系统异常**: 如果在调用过程中发生系统内部异常，订单状态将保持为`STATE_INIT`，以便商户可以重试。

状态的更新操作在`TransferOrderService`中实现，确保了数据库操作的原子性。

**Section sources**
- [TransferOrderService.java](file://service/src/main/java/com/unipay/service/impl/TransferOrderService.java#L24-L143)

## 异步结果通知机制

由于网络延迟或渠道处理时间较长，转账结果可能无法在API调用时立即返回。因此，系统设计了异步结果通知机制，通过`TransferNoticeController`接收来自支付渠道的回调。

```mermaid
sequenceDiagram
participant 渠道 as 支付渠道
participant 通知控制器 as TransferNoticeController
participant 服务层 as TransferOrderReissueService
participant 订单服务 as TransferOrderService
participant 商户 as 商户系统
渠道->>通知控制器 : POST /api/transfer/notify/{ifCode}
通知控制器->>通知控制器 : 解析参数，获取transferId
通知控制器->>订单服务 : 查询TransferOrder
通知控制器->>服务层 : 获取ITransferNoticeService实现
服务层->>服务层 : parseParams() 解析请求
服务层->>服务层 : doNotice() 处理业务逻辑
服务层->>订单服务 : updateIng2Success/Fail()
通知控制器->>商户 : 返回响应给渠道
通知控制器->>通知控制器 : 触发payMchNotifyService.transferOrderNotify()
通知控制器->>商户 : 异步通知商户系统
```

**Diagram sources**
- [TransferNoticeController.java](file://sys-payment/src/main/java/com/unipay/pay/ctrl/transfer/TransferNoticeController.java#L30-L128)
- [TransferOrderReissueService.java](file://sys-payment/src/main/java/com/unipay/pay/service/TransferOrderReissueService.java#L19-L141)

**Section sources**
- [TransferNoticeController.java](file://sys-payment/src/main/java/com/unipay/pay/ctrl/transfer/TransferNoticeController.java#L30-L128)

### 工作流程

1. **入口**: `TransferNoticeController`的`doNotify`方法是回调的入口，URL路径为`/api/transfer/notify/{ifCode}`。
2. **获取实现**: 系统根据`ifCode`动态获取对应的`ITransferNoticeService`实现。
3. **参数解析**: 调用`parseParams`方法解析HTTP请求，提取出转账订单号（`transferId`）和原始参数。
4. **订单查询**: 根据`transferId`从数据库中查询`TransferOrder`对象。
5. **业务处理**: 调用`doNotice`方法处理回调逻辑，该方法会验证签名、解析结果，并返回一个`ChannelRetMsg`对象，其中包含渠道确认的状态。
6. **状态更新**: 如果订单当前状态为`STATE_ING`，且`ChannelRetMsg`表明转账成功或失败，则调用`TransferOrderService`更新订单状态，并触发对商户的异步通知。
7. **响应渠道**: 将`ChannelRetMsg`中的`ResponseEntity`返回给支付渠道，以确认已收到回调。

`ITransferNoticeService`接口的设计与`ITransferService`类似，保证了不同渠道回调处理的统一性和可扩展性。

**Section sources**
- [ITransferNoticeService.java](file://sys-payment/src/main/java/com/unipay/pay/channel/ITransferNoticeService.java#L15-L32)

## 自动补单机制

为了应对网络抖动、消息丢失或渠道未及时回调等极端情况，系统设计了`TransferOrderReissueTask`自动补单任务，确保所有“转账中”状态的订单最终都能得到确认。

```mermaid
flowchart TD
A[TransferOrderReissueTask.start] --> B[查询状态为STATE_ING且创建时间在24小时内的订单]
B --> C{查询到订单?}
C --> |是| D[遍历每个订单]
D --> E[获取对应的ITransferService实现]
E --> F[调用query方法查询订单状态]
F --> G{查询成功?}
G --> |是| H[根据结果更新订单状态]
H --> I[触发商户通知]
G --> |否| J[记录错误，继续处理下一个]
C --> |否| K[任务结束]
```

**Diagram sources**
- [TransferOrderReissueTask.java](file://sys-payment/src/main/java/com/unipay/pay/task/TransferOrderReissueTask.java#L21-L67)
- [TransferOrderReissueService.java](file://sys-payment/src/main/java/com/unipay/pay/service/TransferOrderReissueService.java#L19-L141)

**Section sources**
- [TransferOrderReissueTask.java](file://sys-payment/src/main/java/com/unipay/pay/task/TransferOrderReissueTask.java#L21-L67)

### 任务配置

该任务由`@Scheduled(cron="0 0/1 * * * ?")`注解驱动，每分钟执行一次。

### 执行逻辑

1. **查询条件**: 使用`LambdaQueryWrapper`查询所有状态为`STATE_ING`且创建时间在过去24小时内的转账订单。
2. **分页处理**: 为了避免一次性加载过多数据，任务采用分页查询，每页100条记录。
3. **逐个处理**: 遍历查询到的订单，调用`TransferOrderReissueService.processOrder`方法进行处理。
4. **查询上游**: 在`processOrder`方法中，系统会获取对应的`ITransferService`实现，并调用其`query`方法向支付渠道发起状态查询。
5. **结果处理**: 根据查询返回的`ChannelRetMsg`，如果确认成功或失败，则更新订单状态并通知商户。

该机制作为异步通知的有力补充，极大地提高了系统的健壮性和可靠性。

**Section sources**
- [TransferOrderReissueService.java](file://sys-payment/src/main/java/com/unipay/pay/service/TransferOrderReissueService.java#L19-L141)

## 与支付、退款服务的异同

转账服务与支付、退款服务在整体架构和设计模式上高度相似，但也存在关键差异。

### 相同点

1. **统一的架构模式**: 三者都采用了`Controller -> Service -> Channel Interface`的分层架构。
2. **动态实现加载**: 都通过`SpringBeansUtil.getBean(ifCode + "XXXService")`的方式动态加载具体渠道的实现。
3. **状态机管理**: 都使用类似的状态机来管理订单生命周期。
4. **异步通知**: 都设计了独立的`NoticeController`来处理渠道的异步回调。
5. **补单任务**: 都有对应的定时任务（如`PayOrderReissueTask`、`RefundOrderReissueTask`）来处理状态不一致的情况。

### 不同点

| 特性 | 支付服务 | 退款服务 | 转账服务 |
| :--- | :--- | :--- | :--- |
| **资金流向** | 用户 -> 商户 | 商户 -> 用户 | 商户 -> 用户/第三方 |
| **核心接口** | IPaymentService | IRefundService | ITransferService |
| **订单实体** | PayOrder | RefundOrder | TransferOrder |
| **典型场景** | 商品购买、服务付费 | 订单取消、售后退款 | 分佣、提现、奖励发放 |
| **入账方式** | 不适用 | 不适用 | 支持WX_CASH, ALIPAY_CASH, BANK_CARD |
| **安全要求** | 高 | 高 | 极高 |

**Section sources**
- [TransferOrder.java](file://core/src/main/java/com/unipay/core/entity/TransferOrder.java#L22-L211)
- [PayOrder.java](file://core/src/main/java/com/unipay/core/entity/PayOrder.java)
- [RefundOrder.java](file://core/src/main/java/com/unipay/core/entity/RefundOrder.java)

## 资金安全考虑

转账服务直接涉及资金划转，因此在设计上对安全性有极高的要求，主要体现在以下几个方面：

1. **幂等性控制**: 在`TransferOrderController`中，通过`transferOrderService.count()`检查商户订单号是否已存在，防止因网络重试导致的重复转账。
2. **严格的参数校验**: 对`notifyUrl`等关键参数进行协议校验（必须为`http://`或`https://`），防止恶意URL注入。
3. **双重状态更新**: 状态更新操作（如`updateInit2Ing`和`updateIng2SuccessOrFail`）都使用了条件更新（`eq(TransferOrder::getState, ...)`），确保只有在预期状态下才能进行状态变更，防止并发更新导致的状态错乱。
4. **敏感信息保护**: `TransferOrder`实体中不直接存储敏感的支付凭证，而是通过`channelResData`等字段存储加密或签名后的数据包。
5. **完整的审计日志**: 所有关键操作（如转账、状态更新、回调处理）都通过`@Slf4j`记录了详细的日志，便于事后审计和问题排查。
6. **异步通知的幂等性**: `payMchNotifyService.transferOrderNotify`在通知商户时，会确保通知的幂等性，避免因重试导致商户系统重复处理。

这些措施共同构成了一个安全、可靠的转账服务。

**Section sources**
- [TransferOrderController.java](file://sys-payment/src/main/java/com/unipay/pay/ctrl/transfer/TransferOrderController.java#L34-L236)
- [TransferOrderService.java](file://service/src/main/java/com/unipay/service/impl/TransferOrderService.java#L24-L143)

## 总结

本文档详细阐述了统一支付平台中转账服务的业务场景、核心实现和关键机制。通过`TransferOrderController`接收请求，`ITransferService`接口实现与不同支付渠道的对接，`TransferOrder`实体管理订单状态，`TransferNoticeController`处理异步回调，以及`TransferOrderReissueTask`保障最终