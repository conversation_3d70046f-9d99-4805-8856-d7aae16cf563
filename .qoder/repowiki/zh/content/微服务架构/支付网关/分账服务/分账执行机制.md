
# 分账执行机制

<cite>
**Referenced Files in This Document**   
- [PayOrderDivisionExecController.java](file://sys-payment\src\main\java\com\unipay\pay\ctrl\division\PayOrderDivisionExecController.java)
- [PayOrderDivisionProcessService.java](file://sys-payment\src\main\java\com\unipay\pay\service\PayOrderDivisionProcessService.java)
- [MchDivisionReceiverGroup.java](file://core\src\main\java\com\unipay\core\entity\MchDivisionReceiverGroup.java)
- [IDivisionService.java](file://sys-payment\src\main\java\com\unipay\pay\channel\IDivisionService.java)
- [AlipayDivisionService.java](file://sys-payment\src\main\java\com\unipay\pay\channel\alipay\AlipayDivisionService.java)
- [WxpayDivisionService.java](file://sys-payment\src\main\java\com\unipay\pay\channel\wxpay\WxpayDivisionService.java)
- [ApiController.java](file://sys-payment\src\main\java\com\unipay\pay\ctrl\ApiController.java)
- [PayOrder.java](file://core\src\main\java\com\unipay\core\entity\PayOrder.java)
- [PayOrderDivisionRecord.java](file://core\src\main\java\com\unipay\core\entity\PayOrderDivisionRecord.java)
- [MchDivisionReceiver.java](file://core\src\main\java\com\unipay\core\entity\MchDivisionReceiver.java)
- [ChannelRetMsg.java](file://sys-payment\src\main\java\com\unipay\pay\rqrs\msg\ChannelRetMsg.java)
- [ApiRes.java](file://core\src\main\java\com\unipay\core\model\ApiRes.java)
- [MchAppConfigContext.java](file://sys-payment\src\main\java\com\unipay\pay\model\MchAppConfigContext.java)
- [PayOrderDivisionExecRQ.java](file://sys-payment\src\main\java\com\unipay\pay\rqrs\division\PayOrderDivisionExecRQ.java)
- [PayOrderDivisionExecRS.java](file://sys-payment\src\main\java\com\unipay\pay\rqrs\division\PayOrderDivisionExecRS.java)
- [PayOrderDivisionMQ.java](file://components\components-mq\src\main\java\com\unipay\components\mq\model\PayOrderDivisionMQ.java)
</cite>

## 目录
1. [分账请求接收与验证](#分账请求接收与验证)
2. [分账处理流程](#分账处理流程)
3. [分账规则与明细生成](#分账规则与明细生成)
4. [策略模式与渠道分账执行](#策略模式与渠道分账执行)
5. [分账参数构造与签名验证](#分账参数构造与签名验证)
6. [异常处理机制](#异常处理机制)
7. [事务与状态一致性](#事务与状态一致性)

## 分账请求接收与验证

分账执行机制的入口是 `PayOrderDivisionExecController` 控制器。该控制器通过 `exec` 方法暴露 `/api/division/exec` 接口，接收来自商户的分账请求。

该方法首先调用父类 `ApiController` 的 `getRQByWithMchSign` 方法，完成请求参数的获取和验签。此过程包括：
1.  **参数校验**：检查 `mchNo`（商户号）、`appId`（应用ID）和 `sign`（签名）是否为空。
2.  **商户信息查询**：通过 `configContextQueryService.queryMchInfoAndAppInfo` 方法，根据商户号和应用ID查询商户及其应用的配置信息 `MchAppConfigContext`。
3.  **状态校验**：验证商户和商户应用的状态是否为可用（`CS.YES`）。
4.  **签名验证**：使用商户的 `appSecret` 对请求参数（除去 `sign` 字段）进行签名，并与请求中的 `sign` 进行比对，确保请求的合法性。

在验签通过后，方法会进行业务逻辑校验：
-   检查 `mchOrderNo` 和 `payOrderId` 是否至少有一个不为空。
-   调用 `payOrderService.queryMchOrder` 方法，根据商户号和订单号查询支付订单 `PayOrder`。
-   验证订单状态：订单必须处于支付成功（`PayOrder.STATE_SUCCESS`）、分账状态为未发生分账（`PayOrder.DIVISION_STATE_UNHAPPEN`）且分账模式为手动分账（`PayOrder.DIVISION_MODE_MANUAL`）。

**Section sources**
- [PayOrderDivisionExecController.java](file://sys-payment\src\main\java\com\unipay\pay\ctrl\division\PayOrderDivisionExecController.java#L48-L112)
- [ApiController.java](file://sys-payment\src\main\java\com\unipay\pay\ctrl\ApiController.java#L40-L86)
- [PayOrder.java](file://core\src\main\java\com\unipay\core\entity\PayOrder.java#L24-L278)

## 分账处理流程

当 `PayOrderDivisionExecController.exec` 方法完成初步校验后，会调用 `PayOrderDivisionProcessService.processPayOrderDivision` 方法来处理分账请求。

该方法是分账逻辑的核心，其主要流程如下：

1.  **状态检查与更新**：首先检查订单的分账状态是否为 `WAIT_TASK` 或 `UNHAPPEN`。如果符合，则将订单的分账状态更新为 `ING`（分账处理中），以防止并发请求。
2.  **分账接收者列表处理**：根据请求参数 `useSysAutoDivisionReceivers` 和 `receiverList`，调用 `queryReceiver` 方法查询并过滤出所有有效的分账接收者 `MchDivisionReceiver`。
3.  **分账金额计算**：调用 `payOrderService.calMchIncomeAmount` 方法计算本次分账的总金额，即商家实际可入账的金额（订单金额 - 手续费 - 退款金额 - 已分账金额）。
4.  **生成分账明细**：遍历所有分账接收者，为每个接收者创建一条 `PayOrderDivisionRecord` 记录。该记录包含分账金额、账号信息、分账比例等，并保存到数据库中。
5.  **调用渠道分账接口**：通过策略模式，根据订单的支付接口代码（`ifCode`）获取对应的 `IDivisionService` 实现（如 `AlipayDivisionService` 或 `WxpayDivisionService`），并调用其 `singleDivision` 方法执行实际的分账操作。
6.  **更新分账结果**：根据渠道返回的 `ChannelRetMsg` 结果，更新所有分账记录的状态（成功、失败或已受理）以及渠道返回的批次号和错误信息。
7.  **更新订单状态**：无论分账成功与否，最后都将支付订单的分账状态更新为 `FINISH`（分账任务已结束），并记录分账时间。

```mermaid
sequenceDiagram
participant 商户 as 商户系统
participant Controller as PayOrderDivisionExecController
participant Service as PayOrderDivisionProcessService
participant DB as 数据库
participant Channel as 支付渠道
商户->>Controller : POST /api/division/exec
Controller->>Controller : getRQByWithMchSign() 验签
Controller->>Service : processPayOrderDivision()
Service->>DB : 查询 PayOrder
Service->>Service : queryReceiver() 获取接收者
Service->>Service : calMchIncomeAmount() 计算分账金额
loop 为每个接收者
Service->>Service : genRecord() 生成分账记录
Service->>DB : 保存 PayOrderDivisionRecord
end
Service->>Channel : singleDivision() 调用渠道接口
Channel-->>Service : 返回 ChannelRetMsg
Service->>DB : updateRecordSuccessOrFail() 更新记录状态
Service->>DB : 更新 PayOrder 分账状态为 FINISH
Service-->>Controller : 返回结果
Controller-->>商户 : 返回签名后的响应
```

**Diagram sources **
- [PayOrderDivisionExecController.java](file://sys-payment\src\main\java\com\unipay\pay\ctrl\division\PayOrderDivisionExecController.java#L48-L112)
- [PayOrderDivisionProcessService.java](file://sys-payment\src\main\java\com\unipay\pay\service\PayOrderDivisionProcessService.java#L58-L188)
- [PayOrder.java](file://core\src\main\java\com\unipay\core\entity\PayOrder.java#L24-L278)
- [PayOrderDivisionRecord.java](file://core\src\main\java\com\unipay\core\entity\PayOrderDivisionRecord.java#L23-L205)

**Section sources**
- [PayOrderDivisionProcessService.java](file://sys-payment\src\main\java\com\unipay\pay\service\PayOrderDivisionProcessService.java#L58-L188)

## 分账规则与明细生成

分账明细的生成依赖于商户配置的分账规则，主要通过 `MchDivisionReceiverGroup`（分账账号组）和 `MchDivisionReceiver`（分账接收者）两个实体来管理。

`PayOrderDivisionProcessService` 通过 `queryReceiver` 方法来确定最终的分账接收者列表：
-   **使用系统自动分账组**：当请求参数 `useSysAutoDivisionReceivers` 为 `CS.YES` 时，系统会查询该商户下 `autoDivisionFlag` 为 `CS.YES` 的 `MchDivisionReceiverGroup`，并获取该组内所有状态为可用的 `MchDivisionReceiver`。
-   **使用自定义列表**：当 `useSysAutoDivisionReceivers` 为 `CS.NO` 时，系统会根据请求中的 `receiverList`（包含 `receiverId` 或 `receiverGroupId`）来过滤出 `MchDivisionReceiver`。如果 `receiverList` 中指定了 `divisionProfit`，则会覆盖 `MchDivisionReceiver` 中配置的默认分账比例。

在获取到所有分账接收者后，系统会计算总分账比例，并调用 `genRecord` 方法生成 `PayOrderDivisionRecord`。该方法会：
1.  为本次分账生成一个唯一的系统分账批次号 `batchOrderId`。
2.  计算每个接收者的分账金额 `calDivisionAmount`，使用 `AmountUtil.calPercentageFee` 方法根据分账比例和总分账金额进行计算。
3.  为避免金额溢出，系统会向下取整（`BigDecimal.ROUND_FLOOR`），并将计算出的金额从剩余待分账金额中扣除，确保总分账金额不超过商家可入账金额。

```mermaid
classDiagram
class PayOrderDivisionRecord {
+Long recordId
+String mchNo
+String appId
+String payOrderId
+String batchOrderId
+Byte state
+Long receiverId
+Long receiverGroupId
+String accNo
+String accName
+BigDecimal divisionProfit
+Long calDivisionAmount
}
class MchDivisionReceiver {
+Long receiverId
+Long receiverGroupId
+String accNo
+String accName
+BigDecimal divisionProfit
+Byte state
}
class MchDivisionReceiverGroup {
+Long receiverGroupId
+String receiverGroupName
+Byte autoDivisionFlag
}
MchDivisionReceiverGroup "1" -- "0..*" MchDivisionReceiver : 包含
PayOrderDivisionRecord --> MchDivisionReceiver : 快照
```

**Diagram sources **
- [PayOrderDivisionProcessService.java](file://sys-payment\src\main\java\com\unipay\pay\service\PayOrderDivisionProcessService.java#L233-L296)
- [PayOrderDivisionProcessService.java](file://sys-payment\src\main\java\com\unipay\pay\service\PayOrderDivisionProcessService.java#L192-L230)
- [MchDivisionReceiverGroup.java](file://core\src\main\java\com\unipay\core\entity\MchDivisionReceiverGroup.java#L22-L87)
- [MchDivisionReceiver.java](file://core\src\main\java\com\unipay\core\entity\MchDivisionReceiver.java#L23-L159)
- [PayOrderDivisionRecord.java](file://core\src\main\java\com\unipay\core\entity\PayOrderDivisionRecord.java#L23-L205)

**Section sources**
- [PayOrderDivisionProcessService.java](file://sys-payment\src\main\java\com\unipay\pay\service\PayOrderDivisionProcessService.java#L192-L230)
- [PayOrderDivisionProcessService.java](file://sys-payment\src\main\java\com\unipay\pay\service\PayOrderDivisionProcessService.java#L233-L296)

## 策略模式与渠道分账执行

系统采用策略模式来支持不同的支付渠道（如支付宝、微信）的分账功能。其核心是 `IDivisionService` 接口。

```mermaid
classDiagram
class IDivisionService {
<<interface>>
+String getIfCode()
+boolean isSupport()
+ChannelRetMsg bind()
+ChannelRetMsg singleDivision()
+HashMap<Long, ChannelRetMsg> queryDivision()
}
class AlipayDivisionService {
+ChannelRetMsg singleDivision()
}
class WxpayDivisionService {
+ChannelRetMsg singleDivision()
}
IDivisionService <|-- AlipayDivisionService
IDivisionService <|-- WxpayDivisionService
```

**Diagram sources **
- [IDivisionService.java](file://sys-payment\src\main\java\com\unipay\pay\channel\IDivisionService.java#L16-L33)
- [AlipayDivisionService.java](file://sys-payment\src\main\java\com\unipay\pay\channel\alipay\AlipayDivisionService.java#L35-L264)
- [WxpayDivisionService.java](file://sys-payment\src\main\java\com\unipay\pay\channel\wxpay\WxpayDivisionService.java#L35-L381)

`PayOrderDivisionProcessService` 在执行分账时，通过 `SpringBeansUtil.getBean(payOrder.getIfCode() + "DivisionService", IDivisionService.class)` 动态获取对应的渠道分账服务实现。

-   **支付宝分账** (`AlipayDivisionService`)：调用支付宝的 `alipay.trade.order.settle` 接口。系统将分账明细封装为 `OpenApiRoyaltyDetailInfoPojo` 列表，并设置 `royaltyMode` 为 `sync`（同步分账）。
-   **微信分账** (`WxpayDivisionService`)：调用微信的 `profitsharing` 接口。根据微信API版本（V2或V3）调用不同的方法，并将分账明细封装为 `Profit