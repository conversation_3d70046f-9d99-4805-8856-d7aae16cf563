# 分账结果通知

<cite>
**本文档引用文件**   
- [DivisionRecordChannelNotifyController.java](file://sys-payment/src/main/java/com/unipay/pay/ctrl/division/DivisionRecordChannelNotifyController.java)
- [PayOrderDivisionMQ.java](file://components/components-mq/src/main/java/com/unipay/components/mq/model/PayOrderDivisionMQ.java)
- [PayOrderDivisionMQReceiver.java](file://sys-payment/src/main/java/com/unipay/pay/mq/PayOrderDivisionMQReceiver.java)
- [PayOrderDivisionProcessService.java](file://sys-payment/src/main/java/com/unipay/pay/service/PayOrderDivisionProcessService.java)
- [PayOrderDivisionRecordService.java](file://service/src/main/java/com/unipay/service/impl/PayOrderDivisionRecordService.java)
- [AbstractDivisionRecordChannelNotifyService.java](file://sys-payment/src/main/java/com/unipay/pay/channel/AbstractDivisionRecordChannelNotifyService.java)
- [DivisionChannelNotifyModel.java](file://sys-payment/src/main/java/com/unipay/pay/rqrs/msg/DivisionChannelNotifyModel.java)
- [ChannelRetMsg.java](file://sys-payment/src/main/java/com/unipay/pay/rqrs/msg/ChannelRetMsg.java)
- [PayOrderDivisionRecord.java](file://core/src/main/java/com/unipay/core/entity/PayOrderDivisionRecord.java)
- [PayOrder.java](file://core/src/main/java/com/unipay/core/entity/PayOrder.java)
</cite>

## 目录
1. [分账结果通知流程概述](#分账结果通知流程概述)
2. [支付渠道回调通知](#支付渠道回调通知)
3. [验签与数据解析](#验签与数据解析)
4. [异步消息传递机制](#异步消息传递机制)
5. [消息消费与状态更新](#消息消费与状态更新)
6. [幂等性处理机制](#幂等性处理机制)
7. [状态流转与最终一致性](#状态流转与最终一致性)

## 分账结果通知流程概述

分账结果通知机制是统一支付系统中确保分账操作最终一致性的核心流程。该流程始于支付渠道（如支付宝、微信）的异步回调，经过验签、数据解析后，通过消息队列异步传递结果，最终由消息消费者更新分账记录状态。整个过程设计了严格的幂等性处理，防止重复通知导致的重复分账问题。

**Section sources**
- [DivisionRecordChannelNotifyController.java](file://sys-payment/src/main/java/com/unipay/pay/ctrl/division/DivisionRecordChannelNotifyController.java#L32-L141)
- [PayOrderDivisionMQ.java](file://components/components-mq/src/main/java/com/unipay/components/mq/model/PayOrderDivisionMQ.java#L19-L114)

## 支付渠道回调通知

支付渠道分账结果通知由 `DivisionRecordChannelNotifyController` 统一处理。系统通过 `/api/divisionRecordChannelNotify/{ifCode}` 接口接收来自不同支付渠道的异步回调请求。

```mermaid
sequenceDiagram
participant 支付渠道 as 支付渠道(支付宝/微信)
participant 通知控制器 as DivisionRecordChannelNotifyController
participant 通知服务 as AbstractDivisionRecordChannelNotifyService
participant 分账记录服务 as PayOrderDivisionRecordService
支付渠道->>通知控制器 : POST /api/divisionRecordChannelNotify/alipay
通知控制器->>通知控制器 : 校验ifCode参数
通知控制器->>通知控制器 : 获取对应渠道的NotifyService
通知控制器->>通知服务 : parseParams(request)
通知服务-->>通知控制器 : 返回批次号和参数
通知控制器->>分账记录服务 : 查询待处理的分账记录
分账记录服务-->>通知控制器 : 返回记录列表
通知控制器->>通知服务 : doNotify(request, params, records, context)
通知服务-->>通知控制器 : 返回处理结果
通知控制器->>通知控制器 : 更新各分账记录状态
通知控制器-->>支付渠道 : 返回响应结果
```

**Diagram sources**
- [DivisionRecordChannelNotifyController.java](file://sys-payment/src/main/java/com/unipay/pay/ctrl/division/DivisionRecordChannelNotifyController.java#L42-L139)
- [AbstractDivisionRecordChannelNotifyService.java](file://sys-payment/src/main/java/com/unipay/pay/channel/AbstractDivisionRecordChannelNotifyService.java#L25-L92)

**Section sources**
- [DivisionRecordChannelNotifyController.java](file://sys-payment/src/main/java/com/unipay/pay/ctrl/division/DivisionRecordChannelNotifyController.java#L42-L139)

## 验签与数据解析

分账结果通知的验签和数据解析由具体的支付渠道实现类完成，这些类继承自 `AbstractDivisionRecordChannelNotifyService` 抽象基类。

### 验签流程
1. 各支付渠道实现类（如 `AlipayDivisionRecordChannelNotifyService`）重写 `parseParams` 方法
2. 在 `parseParams` 方法中，从 `HttpServletRequest` 中提取原始请求参数
3. 使用商户配置的私钥或平台公钥对请求数据进行签名验证
4. 验证通过后，解析出分账批次号（`divisionBatchId`）和原始参数

### 数据解析
- 解析结果封装在 `MutablePair<String, Object>` 中，左侧为批次号，右侧为解析后的参数对象
- 若验签失败或解析异常，实现类应自行处理响应，返回 `null` 并抛出 `ResponseException`
- 基类提供了 `textResp` 和 `jsonResp` 工具方法，用于构造文本或JSON格式的响应

```mermaid
flowchart TD
Start([开始]) --> ExtractParams["提取请求参数"]
ExtractParams --> ValidateSign["验证签名"]
ValidateSign --> SignValid{"签名有效?"}
SignValid --> |否| HandleInvalid["处理无效签名"]
HandleInvalid --> ReturnError["返回错误响应"]
SignValid --> |是| ParseData["解析数据"]
ParseData --> ExtractBatchId["提取分账批次号"]
ExtractBatchId --> ReturnResult["返回批次号和参数"]
ReturnResult --> End([结束])
ReturnError --> End
```

**Diagram sources**
- [AbstractDivisionRecordChannelNotifyService.java](file://sys-payment/src/main/java/com/unipay/pay/channel/AbstractDivisionRecordChannelNotifyService.java#L25-L92)
- [DivisionChannelNotifyModel.java](file://sys-payment/src/main/java/com/unipay/pay/rqrs/msg/DivisionChannelNotifyModel.java#L13-L22)

**Section sources**
- [AbstractDivisionRecordChannelNotifyService.java](file://sys-payment/src/main/java/com/unipay/pay/channel/AbstractDivisionRecordChannelNotifyService.java#L25-L92)

## 异步消息传递机制

分账结果通过 `PayOrderDivisionMQ` 消息模型进行异步传递，实现系统解耦和最终一致性。

### 消息模型定义
`PayOrderDivisionMQ` 类定义了分账消息的结构和行为：

```mermaid
classDiagram
class PayOrderDivisionMQ {
+static final String MQ_NAME
+MsgPayload payload
+getMQName() String
+getMQType() MQSendTypeEnum
+toMessage() String
+build(payOrderId, useSysAutoDivisionReceivers, receiverList) PayOrderDivisionMQ
+parse(msg) MsgPayload
}
class MsgPayload {
+String payOrderId
+Byte useSysAutoDivisionReceivers
+CustomerDivisionReceiver[] receiverList
+Boolean isResend
}
class CustomerDivisionReceiver {
+Long receiverId
+Long receiverGroupId
+BigDecimal divisionProfit
}
PayOrderDivisionMQ --> MsgPayload : "包含"
MsgPayload --> CustomerDivisionReceiver : "包含"
PayOrderDivisionMQ <|-- AbstractMQ : "继承"
```

**Diagram sources**
- [PayOrderDivisionMQ.java](file://components/components-mq/src/main/java/com/unipay/components/mq/model/PayOrderDivisionMQ.java#L19-L114)

**Section sources**
- [PayOrderDivisionMQ.java](file://components/components-mq/src/main/java/com/unipay/components/mq/model/PayOrderDivisionMQ.java#L19-L114)

## 消息消费与状态更新

`PayOrderDivisionMQReceiver` 作为消息消费者，负责处理分账消息并更新系统状态。

### 消费流程
1. 接收 `PayOrderDivisionMQ.MsgPayload` 消息
2. 调用 `PayOrderDivisionProcessService.processPayOrderDivision` 方法处理分账逻辑
3. 处理过程中可能调用支付渠道的分账接口
4. 根据渠道返回结果更新分账记录状态

```mermaid
sequenceDiagram
participant MQBroker as 消息队列
participant MQReceiver as PayOrderDivisionMQReceiver
participant ProcessService as PayOrderDivisionProcessService
participant DB as 数据库
MQBroker->>MQReceiver : 发送分账消息
MQReceiver->>MQReceiver : 接收消息并记录日志
MQReceiver->>ProcessService : processPayOrderDivision(payload)
ProcessService->>ProcessService : 查询支付订单
ProcessService->>ProcessService : 验证分账状态
ProcessService->>ProcessService : 更新订单状态为处理中
ProcessService->>ProcessService : 查询分账接收方
ProcessService->>ProcessService : 生成分账记录
ProcessService->>ProcessService : 调用渠道分账接口
ProcessService->>DB : 更新分账记录状态
ProcessService->>DB : 更新支付订单状态
ProcessService-->>MQReceiver : 返回处理结果
MQReceiver-->>MQBroker : 确认消息消费
```

**Diagram sources**
- [PayOrderDivisionMQReceiver.java](file://sys-payment/src/main/java/com/unipay/pay/mq/PayOrderDivisionMQReceiver.java#L21-L31)
- [PayOrderDivisionProcessService.java](file://sys-payment/src/main/java/com/unipay/pay/service/PayOrderDivisionProcessService.java#L58-L188)

**Section sources**
- [PayOrderDivisionMQReceiver.java](file://sys-payment/src/main/java/com/unipay/pay/mq/PayOrderDivisionMQReceiver.java#L21-L31)
- [PayOrderDivisionProcessService.java](file://sys-payment/src/main/java/com/unipay/pay/service/PayOrderDivisionProcessService.java#L58-L188)

## 幂等性处理机制

系统通过多层次的幂等性设计，确保即使收到重复的分账通知，也不会导致重复分账。

### 状态机控制
分账记录的状态机设计是幂等性的核心：

```mermaid
stateDiagram-v2
[*] --> 待分账
待分账 --> 分账成功 : 明确成功
待分账 --> 分账失败 : 明确失败
待分账 --> 已受理 : 上游受理
已受理 --> 分账成功 : 明确成功
已受理 --> 分账失败 : 明确失败
分账成功 --> 分账成功 : 重复通知
分账失败 --> 分账失败 : 重复通知
note right of 分账成功
状态为成功时，忽略后续通知
end note
note right of 分账失败
状态为失败时，可重试
end note
```

### 关键实现
1. **数据库更新条件**：在更新分账记录状态时，使用 `update` 方法并指定 `where` 条件，确保只有在特定状态下才能更新
   - 成功更新：`eq(PayOrderDivisionRecord::getState, PayOrderDivisionRecord.STATE_ACCEPT)`
   - 批量更新：`eq(PayOrderDivisionRecord::getState, PayOrderDivisionRecord.STATE_WAIT)`

2. **批次号机制**：每次重试分账时，重新生成 `batchOrderId`，避免因 `out_trade_no` 重复导致的问题

3. **事务控制**：关键状态更新操作使用 `@Transactional` 注解，确保数据一致性

**Diagram sources**
- [PayOrderDivisionRecord.java](file://core/src/main/java/com/unipay/core/entity/PayOrderDivisionRecord.java#L23-L205)
- [PayOrderDivisionRecordService.java](file://service/src/main/java/com/unipay/service/impl/PayOrderDivisionRecordService.java#L31-L38)

**Section sources**
- [PayOrderDivisionRecord.java](file://core/src/main/java/com/unipay/core/entity/PayOrderDivisionRecord.java#L23-L205)
- [PayOrderDivisionRecordService.java](file://service/src/main/java/com/unipay/service/impl/PayOrderDivisionRecordService.java#L60-L85)

## 状态流转与最终一致性

分账系统的状态流转设计确保了业务的最终一致性。

### 状态定义
| 状态 | 说明 | 来源 |
|------|------|------|
| STATE_WAIT (0) | 待分账 | 初始状态 |
| STATE_SUCCESS (1) | 分账成功 | 渠道明确返回成功 |
| STATE_FAIL (2) | 分账失败 | 渠道明确返回失败 |
| STATE_ACCEPT (3) | 已受理 | 渠道受理，等待最终结果 |

### 状态流转规则
1. **通知处理**：当收到渠道通知时，只处理状态为 `STATE_ACCEPT` 的记录
2. **成功处理**：将状态从 `STATE_ACCEPT` 更新为 `STATE_SUCCESS`，并清空错误信息
3. **失败处理**：将状态从 `STATE_ACCEPT` 更新为 `STATE_FAIL`，并记录错误信息
4. **幂等保护**：对于已为 `STATE_SUCCESS` 或 `STATE_FAIL` 的记录，忽略后续通知

### 最终一致性保障
- **异步解耦**：通过消息队列实现通知处理与业务处理的解耦
- **重试机制**：对于失败的分账操作，可通过 `updateResendState` 方法重置状态并重新发起
- **监控告警**：系统可监控长时间处于 `STATE_ACCEPT` 状态的记录，及时发现异常

```mermaid
flowchart LR
A[支付渠道通知] --> B{验签成功?}
B --> |否| C[返回错误]
B --> |是| D[解析批次号]
D --> E[查询待处理记录]
E --> F{记录存在?}
F --> |否| G[返回订单不存在]
F --> |是| H[调用渠道处理]
H --> I{处理结果}
I --> |成功| J[更新为成功状态]
I --> |失败| K[更新为失败状态]
I --> |受理| L[更新为已受理]
J --> M[返回成功响应]
K --> M
L --> M
```

**Diagram sources**
- [PayOrderDivisionRecord.java](file://core/src/main/java/com/unipay/core/entity/PayOrderDivisionRecord.java#L30-L33)
- [ChannelRetMsg.java](file://sys-payment/src/main/java/com/unipay/pay/rqrs/msg/ChannelRetMsg.java#L13-L108)

**Section sources**
- [PayOrderDivisionRecordService.java](file://service/src/main/java/com/unipay/service/impl/PayOrderDivisionRecordService.java#L42-L57)
- [ChannelRetMsg.java](file://sys-payment/src/main/java/com/unipay/pay/rqrs/msg/ChannelRetMsg.java#L64-L86)