# 微服务架构

<cite>
**本文档中引用的文件**  
- [sys-payment](file://sys-payment)
- [sys-merchant](file://sys-merchant)
- [sys-agent](file://sys-agent)
- [sys-manager](file://sys-manager)
- [components-mq](file://components/components-mq)
- [ResetAppConfigMQ.java](file://components/components-mq/src/main/java/com/unipay/components/mq/model/ResetAppConfigMQ.java)
- [application.yml](file://sys-payment/src/main/resources/application.yml)
- [PayOrderMchNotifyMQ.java](file://components/components-mq/src/main/java/com/unipay/components/mq/model/PayOrderMchNotifyMQ.java)
- [CleanMchLoginAuthCacheMQ.java](file://components/components-mq/src/main/java/com/unipay/components/mq/model/CleanMchLoginAuthCacheMQ.java)
- [core](file://core)
- [service](file://service)
</cite>

## 目录
1. [引言](#引言)
2. [项目结构](#项目结构)
3. [核心组件](#核心组件)
4. [架构概述](#架构概述)
5. [详细组件分析](#详细组件分析)
6. [依赖分析](#依赖分析)
7. [性能考虑](#性能考虑)
8. [故障排除指南](#故障排除指南)
9. [结论](#结论)

## 引言
本文档详细描述了统一支付系统（uni-pay）的微服务架构设计。该系统采用微服务架构模式，将复杂的支付业务拆分为多个职责清晰、独立部署的服务。核心服务包括支付网关（sys-payment）、商户系统（sys-merchant）、运营平台（sys-manager）和代理商系统（sys-agent）。这些服务通过RESTful API进行同步通信，并利用消息队列（MQ）实现异步解耦，确保系统的高可用性和可扩展性。文档旨在为架构师提供设计决策背后的权衡考量，为开发者提供服务间调用的最佳实践和错误处理策略。

## 项目结构
项目采用典型的微服务架构布局，每个微服务拥有独立的代码库和构建配置。核心微服务位于根目录下，包括`sys-payment`、`sys-merchant`、`sys-agent`和`sys-manager`。共享组件（如消息队列和对象存储）被抽象为独立的`components`模块，供所有服务复用。`core`模块包含所有服务共享的通用实体、常量和工具类，而`service`模块则封装了数据访问层（DAO）的实现。这种分层设计确保了代码的高内聚和低耦合。

```mermaid
graph TB
subgraph "前端界面"
UI[统一前端 unipay-web-ui]
Portal[门户 unipay-portal]
end
subgraph "微服务"
Payment[支付网关 sys-payment]
Merchant[商户系统 sys-merchant]
Agent[代理商系统 sys-agent]
Manager[运营平台 sys-manager]
end
subgraph "共享组件"
MQ[消息队列 components-mq]
OSS[对象存储 components-oss]
Core[核心库 core]
Service[数据服务 service]
end
UI --> Payment
UI --> Merchant
UI --> Agent
UI --> Manager
Payment --> MQ
Merchant --> MQ
Agent --> MQ
Manager --> MQ
Payment --> Core
Merchant --> Core
Agent --> Core
Manager --> Core
Payment --> Service
Merchant --> Service
Agent --> Service
Manager --> Service
```

**图示来源**
- [sys-payment](file://sys-payment)
- [sys-merchant](file://sys-merchant)
- [sys-agent](file://sys-agent)
- [sys-manager](file://sys-manager)
- [components-mq](file://components/components-mq)
- [core](file://core)
- [service](file://service)

**本节来源**
- [sys-payment](file://sys-payment)
- [sys-merchant](file://sys-merchant)
- [sys-agent](file://sys-agent)
- [sys-manager](file://sys-manager)

## 核心组件
系统的核心由四个微服务构成，每个服务负责特定的业务领域。支付网关（sys-payment）是处理所有支付交易的核心，负责订单创建、支付通道调用和异步通知。商户系统（sys-merchant）为商户提供自助管理功能，包括应用配置、订单查询和分账管理。代理商系统（sys-agent）服务于渠道代理商，提供商户入驻、分润计算和渠道管理功能。运营平台（sys-manager）是后台管理中枢，负责系统配置、权限管理和数据监控。这些服务通过共享的`core`和`service`模块访问统一的数据模型和持久化逻辑，确保了业务语义的一致性。

**本节来源**
- [sys-payment](file://sys-payment)
- [sys-merchant](file://sys-merchant)
- [sys-agent](file://sys-agent)
- [sys-manager](file://sys-manager)
- [core](file://core)
- [service](file://service)

## 架构概述
该系统采用基于Spring Boot的微服务架构，服务间通信主要依赖RESTful API和消息队列两种模式。同步操作（如创建支付订单）通过HTTP API直接调用，保证了请求的即时响应。异步操作（如订单状态变更通知、配置更新广播）则通过消息队列进行解耦，提高了系统的弹性和容错能力。系统利用Redis作为分布式缓存和消息中间件，实现了会话共享和高性能的数据访问。服务发现和配置中心功能目前由各服务的`application.yml`文件和Redis状态共享实现，架构上为未来引入Nacos或Consul等专业组件预留了空间。

```mermaid
graph TD
A[客户端] --> B[API网关 (Nginx)]
B --> C[支付网关 sys-payment]
B --> D[商户系统 sys-merchant]
B --> E[代理商系统 sys-agent]
B --> F[运营平台 sys-manager]
C --> G[(MySQL数据库)]
D --> G
E --> G
F --> G
C --> H[(Redis缓存)]
D --> H
E --> H
F --> H
C --> I[消息队列 (Redis/RocketMQ)]
D --> I
E --> I
F --> I
I --> J[支付网关消费者]
I --> K[商户系统消费者]
I --> L[代理商系统消费者]
I --> M[运营平台消费者]
style A fill:#f9f,stroke:#333
style B fill:#ffcc00,stroke:#333
style C fill:#4CAF50,stroke:#333,color:#fff
style D fill:#2196F3,stroke:#333,color:#fff
style E fill:#9C27B0,stroke:#333,color:#fff
style F fill:#FF9800,stroke:#333,color:#fff
style G fill:#607D8B,stroke:#333,color:#fff
style H fill:#00BCD4,stroke:#333,color:#fff
style I fill:#E91E63,stroke:#333,color:#fff
```

**图示来源**
- [sys-payment](file://sys-payment)
- [sys-merchant](file://sys-merchant)
- [sys-agent](file://sys-agent)
- [sys-manager](file://sys-manager)
- [components-mq](file://components/components-mq)
- [application.yml](file://sys-payment/src/main/resources/application.yml)

## 详细组件分析

### 支付网关分析
支付网关是整个系统的核心交易引擎。它接收来自商户或前端的支付请求，执行复杂的业务逻辑，包括参数校验、金额计算、支付通道选择，并最终调用第三方支付渠道（如微信、支付宝）完成支付。支付成功后，网关会通过消息队列发布`PayOrderMchNotifyMQ`消息，通知商户系统进行后续处理。对于配置变更，如支付通道参数更新，网关会发布`ResetAppConfigMQ`消息，触发所有相关服务刷新本地缓存。

#### 服务间通信序列图
```mermaid
sequenceDiagram
participant Merchant as 商户系统(sys-merchant)
participant Payment as 支付网关(sys-payment)
participant Channel as 第三方支付渠道
participant MQ as 消息队列(components-mq)
participant Cache as Redis缓存
Merchant->>Payment : POST /api/pay/orders (创建订单)
Payment->>Payment : 校验参数、查询商户配置
Payment->>Payment : 选择支付通道、生成订单
Payment->>Channel : 调用渠道API
Channel-->>Payment : 返回支付参数
Payment->>Merchant : 返回支付结果
Payment->>MQ : 发布 PayOrderMchNotifyMQ
MQ->>Merchant : 消费通知
Merchant->>Merchant : 更新订单状态
Payment->>Cache : 缓存订单信息
```

**图示来源**
- [sys-payment](file://sys-payment)
- [sys-merchant](file://sys-merchant)
- [PayOrderMchNotifyMQ.java](file://components/components-mq/src/main/java/com/unipay/components/mq/model/PayOrderMchNotifyMQ.java)

#### 配置广播流程
```mermaid
flowchart TD
Start([运营平台修改配置]) --> Save["保存到数据库"]
Save --> Publish["发布 ResetAppConfigMQ 消息"]
Publish --> MQ["消息队列"]
MQ --> Payment["支付网关\n接收并处理"]
MQ --> Merchant["商户系统\n接收并处理"]
MQ --> Agent["代理商系统\n接收并处理"]
MQ --> Manager["运营平台\n接收并处理"]
Payment --> Clear["清除本地缓存"]
Merchant --> Clear
Agent --> Clear
Manager --> Clear
Clear --> Load["从数据库重新加载配置"]
Load --> End([配置更新完成])
```

**图示来源**
- [ResetAppConfigMQ.java](file://components/components-mq/src/main/java/com/unipay/components/mq/model/ResetAppConfigMQ.java)
- [sys-payment](file://sys-payment)
- [sys-merchant](file://sys-merchant)
- [sys-agent](file://sys-agent)
- [sys-manager](file://sys-manager)

**本节来源**
- [sys-payment](file://sys-payment)
- [PayOrderMchNotifyMQ.java](file://components/components-mq/src/main/java/com/unipay/components/mq/model/PayOrderMchNotifyMQ.java)
- [ResetAppConfigMQ.java](file://components/components-mq/src/main/java/com/unipay/components/mq/model/ResetAppConfigMQ.java)

### 商户系统分析
商户系统为商户提供了一个自助服务平台。商户可以通过该系统管理其应用、查看交易订单、配置支付方式和管理分账规则。当商户登录时，系统会检查其会话状态。如果运营平台或代理商系统修改了该商户的权限或配置，会发布`CleanMchLoginAuthCacheMQ`消息，强制该商户的登录会话在下次请求时重新进行权限校验，确保了权限变更的实时生效。

**本节来源**
- [sys-merchant](file://sys-merchant)
- [CleanMchLoginAuthCacheMQ.java](file://components/components-mq/src/main/java/com/unipay/components/mq/model/CleanMchLoginAuthCacheMQ.java)

### 代理商系统分析
代理商系统专注于渠道管理和分润计算。代理商可以创建和管理其下属的商户，并根据交易流水获得分润。系统通过`AgentProfitRecord`实体记录每一笔分润，并通过定时任务进行结算。该系统与支付网关紧密协作，确保分账请求（`PayOrderDivisionMQ`）被正确处理。

**本节来源**
- [sys-agent](file://sys-agent)
- [core/entity/AgentProfitRecord.java](file://core/src/main/java/com/unipay/core/entity/AgentProfitRecord.java)
- [components-mq/model/PayOrderDivisionMQ.java](file://components/components-mq/src/main/java/com/unipay/components/mq/model/PayOrderDivisionMQ.java)

### 运营平台分析
运营平台是系统的超级管理员控制台，拥有对所有数据和配置的最高权限。它可以管理商户、代理商、系统参数和操作员。所有在运营平台进行的全局配置修改，都会通过`ResetAppConfigMQ`消息广播到所有服务，保证了配置的一致性。

**本节来源**
- [sys-manager](file://sys-manager)
- [ResetAppConfigMQ.java](file://components/components-mq/src/main/java/com/unipay/components/mq/model/ResetAppConfigMQ.java)

## 依赖分析
系统各组件之间的依赖关系清晰。所有微服务都依赖于`core`模块，以获取统一的实体模型和常量定义。数据访问逻辑被封装在`service`模块中，通过MyBatis的Mapper接口提供。`components-mq`模块为所有服务提供了统一的消息生产和消费能力，实现了基于Redis或RocketMQ的抽象。这种依赖管理方式避免了代码重复，确保了核心逻辑的统一。

```mermaid
graph LR
Payment --> Core
Payment --> Service
Payment --> ComponentsMQ
Merchant --> Core
Merchant --> Service
Merchant --> ComponentsMQ
Agent --> Core
Agent --> Service
Agent --> ComponentsMQ
Manager --> Core
Manager --> Service
Manager --> ComponentsMQ
ComponentsMQ --> Core
style Core fill:#FFD700,stroke:#333
style Service fill:#AD1457,stroke:#333,color:#fff
style ComponentsMQ fill:#1976D2,stroke:#333,color:#fff
```

**图示来源**
- [core](file://core)
- [service](file://service)
- [components-mq](file://components/components-mq)

**本节来源**
- [pom.xml](file://sys-payment/pom.xml)
- [pom.xml](file://sys-merchant/pom.xml)
- [pom.xml](file://sys-agent/pom.xml)
- [pom.xml](file://sys-manager/pom.xml)
- [components/components-mq/pom.xml](file://components/components-mq/pom.xml)

## 性能考虑
系统在设计时充分考虑了性能和可扩展性。通过将核心交易（支付网关）与管理功能（商户、代理、运营）分离，可以独立地对高并发的支付服务进行水平扩展。利用Redis作为缓存，极大地减少了对数据库的直接访问，提升了响应速度。异步消息队列的使用，使得耗时的操作（如通知、日志记录）不会阻塞主交易流程。然而，当前的架构将服务发现和配置中心的功能内置于应用中，随着服务数量的增加，可能会成为管理瓶颈，建议未来引入专业的服务网格或注册中心解决方案。

## 故障排除指南
当遇到服务间通信问题时，应首先检查消息队列的状态。确认`components-mq`模块的生产者和消费者是否正常工作。对于配置未生效的问题，检查`ResetAppConfigMQ`消息是否被成功发布和消费。如果涉及权限问题，检查`CleanMchLoginAuthCacheMQ`消息的处理情况。日志文件（位于各服务的`logback-spring.xml`配置的路径下）是排查问题的关键，应重点查看API调用日志和MQ处理日志。

**本节来源**
- [components-mq](file://components/components-mq)
- [ResetAppConfigMQ.java](file://components/components-mq/src/main/java/com/unipay/components/mq/model/ResetAppConfigMQ.java)
- [CleanMchLoginAuthCacheMQ.java](file://components/components-mq/src/main/java/com/unipay/components/mq/model/CleanMchLoginAuthCacheMQ.java)
- [logback-spring.xml](file://sys-payment/src/main/resources/logback-spring.xml)

## 结论
该微服务架构通过清晰的职责划分和有效的解耦机制，成功地将一个复杂的支付系统分解为可管理、可扩展的独立单元。RESTful API提供了直接、高效的同步通信，而基于MQ的异步消息传递则增强了系统的健壮性和灵活性。尽管当前的配置和发现机制较为简单，但整体架构设计合理，为未来的演进奠定了坚实的基础。对于开发者而言，遵循统一的MQ消息规范和API设计约定，是确保系统稳定运行的关键。