# 应用配置管理

<cite>
**本文档引用的文件**  
- [MchApp.java](file://core/src/main/java/com/unipay/core/entity/MchApp.java)
- [MchAppController.java](file://sys-manager/src/main/java/com/unipay/mgr/ctrl/merchant/MchAppController.java)
- [MchAppController.java](file://sys-merchant/src/main/java/com/unipay/mch/ctrl/merchant/MchAppController.java)
- [MchAppController.java](file://sys-agent/src/main/java/com/unipay/agent/ctrl/merchant/MchAppController.java)
- [MchPayPassage.java](file://core/src/main/java/com/unipay/core/entity/MchPayPassage.java)
- [PayInterfaceConfig.java](file://core/src/main/java/com/unipay/core/entity/PayInterfaceConfig.java)
- [MchAppService.java](file://service/src/main/java/com/unipay/service/impl/MchAppService.java)
- [MchPayPassageService.java](file://service/src/main/java/com/unipay/service/impl/MchPayPassageService.java)
- [PayInterfaceConfigService.java](file://service/src/main/java/com/unipay/service/impl/PayInterfaceConfigService.java)
- [ResetIsvMchAppInfoConfigMQ.java](file://components/components-mq/src/main/java/com/unipay/components/mq/model/ResetIsvMchAppInfoConfigMQ.java)
- [ConfigContextService.java](file://sys-payment/src/main/java/com/unipay/pay/service/ConfigContextService.java)
- [ConfigContextQueryService.java](file://sys-payment/src/main/java/com/unipay/pay/service/ConfigContextQueryService.java)
</cite>

## 目录
1. [商户应用实体结构](#商户应用实体结构)
2. [应用管理API接口](#应用管理api接口)
3. [支付通道配置管理](#支付通道配置管理)
4. [支付接口参数配置](#支付接口参数配置)
5. [批量配置支付通道](#批量配置支付通道)
6. [配置变更验证与同步](#配置变更验证与同步)

## 商户应用实体结构

`MchApp`实体类定义了商户应用的核心配置信息，存储在`t_mch_app`数据库表中。该实体包含以下关键字段：

- **应用ID (appId)**: 应用的唯一标识符，作为主键使用
- **应用名称 (appName)**: 应用的显示名称，用于识别和管理
- **商户号 (mchNo)**: 关联的商户编号，建立应用与商户的关系
- **应用状态 (state)**: 状态标识，0表示停用，1表示正常启用
- **应用私钥 (appSecret)**: 应用的认证密钥，用于API调用的身份验证
- **备注 (remark)**: 应用的附加说明信息
- **创建信息**: 包括创建者用户ID(createdUid)、创建者姓名(createdBy)和创建时间(createdAt)
- **更新时间 (updatedAt)**: 记录最后一次修改的时间戳

该实体通过`BaseModel`继承了基础的审计字段，并使用MyBatis-Plus的`@TableName`注解映射到数据库表。`@Accessors(chain = true)`注解启用了链式调用，提高了代码的可读性和流畅性。

**Section sources**
- [MchApp.java](file://core/src/main/java/com/unipay/core/entity/MchApp.java#L22-L98)

## 应用管理API接口

系统提供了多套`MchAppController`实现，分别服务于不同角色的用户，实现了应用的全生命周期管理。

### 管理端应用管理
`sys-manager`模块中的`MchAppController`为系统管理员提供完整的应用管理功能，包括创建、查询、更新和删除操作。创建应用时需要指定商户号、应用名称和私钥等必要信息，并验证商户号的有效性。

### 商户端应用管理
`sys-merchant`模块中的`MchAppController`为商户自身提供应用管理功能。与管理端不同，商户在创建应用时无需指定商户号，系统会自动使用当前登录商户的商户号，确保商户只能管理自己的应用。

### 代理商应用管理
`sys-agent`模块中的`MchAppController`为代理商提供管理其下属商户应用的功能。代理商在操作时需要验证权限，确保只能为已关联的商户创建、修改或删除应用。在查询列表时，系统会自动过滤出代理商有权访问的商户应用，并对应用私钥进行脱敏处理。

所有角色的更新和删除操作完成后，都会通过消息队列发送`ResetIsvMchAppInfoConfigMQ`消息，通知系统刷新相关配置缓存，确保配置变更的实时生效。

```mermaid
sequenceDiagram
participant 前端 as 前端界面
participant 控制器 as MchAppController
participant 服务层 as MchAppService
participant 数据库 as 数据库
participant 消息队列 as MQ
前端->>控制器 : 发送更新请求
控制器->>控制器 : 验证用户权限
控制器->>服务层 : 调用updateById
服务层->>数据库 : 执行更新操作
数据库-->>服务层 : 返回结果
服务层->>消息队列 : 发送重置配置消息
消息队列-->>控制器 : 消息发送成功
控制器-->>前端 : 返回成功响应
```

**Diagram sources**
- [MchAppController.java](file://sys-manager/src/main/java/com/unipay/mgr/ctrl/merchant/MchAppController.java#L121-L144)
- [MchAppService.java](file://service/src/main/java/com/unipay/service/impl/MchAppService.java#L27-L96)
- [ResetIsvMchAppInfoConfigMQ.java](file://components/components-mq/src/main/java/com/unipay/components/mq/model/ResetIsvMchAppInfoConfigMQ.java#L16-L83)

**Section sources**
- [MchAppController.java](file://sys-manager/src/main/java/com/unipay/mgr/ctrl/merchant/MchAppController.java#L30-L169)
- [MchAppController.java](file://sys-merchant/src/main/java/com/unipay/mch/ctrl/merchant/MchAppController.java#L30-L174)
- [MchAppController.java](file://sys-agent/src/main/java/com/unipay/agent/ctrl/merchant/MchAppController.java#L35-L266)

## 支付通道配置管理

`MchPayPassage`实体类用于管理商户应用的支付通道配置，存储在`t_mch_pay_passage`数据库表中。该配置将支付方式与具体的支付接口关联起来，并设置相应的费率。

### 核心字段
- **商户号 (mchNo)**: 关联的商户编号
- **应用ID (appId)**: 关联的应用编号
- **支付接口 (ifCode)**: 支付接口的代码标识
- **支付方式 (wayCode)**: 支付方式的代码标识
- **支付方式费率 (rate)**: 该通道的交易费率
- **状态 (state)**: 通道状态，0停用，1启用

### 服务实现
`MchPayPassageService`提供了核心的业务逻辑：
- `selectAvailablePayInterfaceList`: 根据支付方式查询可用的支付接口列表，同时返回已配置的通道状态和费率
- `saveOrUpdateBatchSelf`: 批量保存或更新支付通道配置，支持事务性操作，确保数据一致性
- `findMchPayPassage`: 根据商户号、应用ID和支付方式查找可用的支付通道

当为特约商户配置支付通道时，系统会优先检查服务商是否已配置相应的支付参数，确保通道的可用性。

**Section sources**
- [MchPayPassage.java](file://core/src/main/java/com/unipay/core/entity/MchPayPassage.java#L25-L99)
- [MchPayPassageService.java](file://service/src/main/java/com/unipay/service/impl/MchPayPassageService.java#L29-L124)

## 支付接口参数配置

`PayInterfaceConfig`实体类用于存储支付接口的具体配置参数，支持不同支付渠道的差异化配置。

### 核心字段
- **账号类型 (infoType)**: 1表示服务商，2表示商户
- **账号编号 (infoId)**: 服务商号或应用AppId
- **支付接口代码 (ifCode)**: 支付接口的唯一标识
- **接口配置参数 (ifParams)**: JSON格式的配置参数字符串
- **支付接口费率 (ifRate)**: 接口级别的费率
- **状态 (state)**: 配置状态，0停用，1启用

### 配置查询
`PayInterfaceConfigService`提供了灵活的查询方法：
- `getByInfoIdAndIfCode`: 根据账号类型、账号编号和接口代码获取特定配置
- `selectAllPayIfConfigListByAppId`: 获取指定应用的所有支付接口配置列表，同时包含配置状态信息

对于特约商户，系统会同时检查商户自身的配置和其服务商的配置，确保支付参数的完整性。配置参数以JSON格式存储，可以灵活适应不同支付渠道的参数需求，如微信支付的API密钥、支付宝的私钥等。

```mermaid
classDiagram
class PayInterfaceConfig {
+Long id
+Byte infoType
+String infoId
+String ifCode
+String ifParams
+BigDecimal ifRate
+Byte state
+String remark
+Long createdUid
+String createdBy
+Date createdAt
+Long updatedUid
+String updatedBy
+Date updatedAt
}
class MchPayPassage {
+Long id
+String mchNo
+String appId
+String ifCode
+String wayCode
+BigDecimal rate
+String riskConfig
+Byte state
+Date createdAt
+Date updatedAt
}
class MchApp {
+String appId
+String appName
+String mchNo
+Byte state
+String appSecret
+String remark
+Long createdUid
+String createdBy
+Date createdAt
+Date updatedAt
}
MchApp "1" -- "0..*" MchPayPassage : 包含
MchApp "1" -- "0..*" PayInterfaceConfig : 配置
MchPayPassage "1" --> PayInterfaceConfig : 关联
```

**Diagram sources**
- [PayInterfaceConfig.java](file://core/src/main/java/com/unipay/core/entity/PayInterfaceConfig.java#L25-L124)
- [MchPayPassage.java](file://core/src/main/java/com/unipay/core/entity/MchPayPassage.java#L25-L99)
- [MchApp.java](file://core/src/main/java/com/unipay/core/entity/MchApp.java#L22-L98)

**Section sources**
- [PayInterfaceConfig.java](file://core/src/main/java/com/unipay/core/entity/PayInterfaceConfig.java#L25-L124)
- [PayInterfaceConfigService.java](file://service/src/main/java/com/unipay/service/impl/PayInterfaceConfigService.java#L28-L160)

## 批量配置支付通道

系统支持为商户应用批量配置支付通道，满足代理商为多个商户快速设置支付方式的需求。

### 实现机制
批量配置通过`MchPayPassageService`的`saveOrUpdateBatchSelf`方法实现，该方法接受支付通道配置列表作为参数，逐个处理每个配置项：
1. 跳过未启用且无ID的新建通道
2. 为商户系统配置添加商户号参数
3. 将费率转换为小数存储
4. 事务性地保存或更新每个通道配置

### 使用场景
代理商可以通过前端界面选择多个商户应用，然后为它们统一配置相同的支付通道。系统会验证代理商对每个商户的管理权限，确保操作的安全性。批量配置完成后，系统会为每个受影响的应用发送配置重置消息，确保新的支付通道配置能够立即生效。

```mermaid
flowchart TD
Start([开始批量配置]) --> ValidateAuth["验证代理商权限"]
ValidateAuth --> CheckMchList["检查商户列表"]
CheckMchList --> LoopStart{遍历每个商户应用}
LoopStart --> CreateConfig["创建支付通道配置"]
CreateConfig --> SetRate["设置费率"]
SetRate --> SaveConfig["保存配置到数据库"]
SaveConfig --> CheckResult{"保存成功?"}
CheckResult --> |是| NextApp["处理下一个应用"]
CheckResult --> |否| ThrowException["抛出业务异常"]
NextApp --> LoopEnd
LoopEnd --> |完成| SendMQ["发送配置重置消息"]
SendMQ --> End([结束])
ThrowException --> End
```

**Diagram sources**
- [MchPayPassageService.java](file://service/src/main/java/com/unipay/service/impl/MchPayPassageService.java#L71-L87)
- [MchPayPassageConfigController.java](file://sys-agent/src/main/java/com/unipay/agent/ctrl/merchant/MchPayPassageConfigController.java#L165-L184)

**Section sources**
- [MchPayPassageService.java](file://service/src/main/java/com/unipay/service/impl/MchPayPassageService.java#L71-L87)

## 配置变更验证与同步

系统通过多层次的验证和同步机制确保应用配置变更的安全性和一致性。

### 验证规则
- **删除验证**: 删除应用前会检查是否存在交易数据，若有则禁止删除
- **权限验证**: 不同角色的操作都需通过权限验证，确保只能操作有权访问的资源
- **数据完整性验证**: 创建和更新操作会验证必要字段的完整性和有效性

### 权限控制
系统采用基于角色的访问控制(RBAC)：
- 管理员拥有所有权限
- 商户只能管理自己的应用
- 代理商只能管理其下属商户的应用
- 通过`@PreAuthorize`注解实现细粒度的权限控制

### 配置同步
配置变更通过消息队列实现跨服务的实时同步：
1. 当应用配置变更时，发送`ResetIsvMchAppInfoConfigMQ`广播消息
2. 各业务服务节点监听该消息
3. 收到消息后，调用`ConfigContextService`刷新本地缓存
4. `ConfigContextQueryService`从缓存或数据库获取最新的配置信息

`ConfigContextService`使用ConcurrentHashMap维护配置缓存，确保高并发下的线程安全。缓存包含商户配置、应用配置和服务商配置三个层级，通过`initMchAppConfigContext`等方法实现按需初始化和更新。

```mermaid
sequenceDiagram
participant 应用A as 应用A
participant 应用B as 应用B
participant 消息队列 as MQ
participant 缓存服务 as ConfigContextService
应用A->>应用A : 更新应用配置
应用A->>消息队列 : 发送重置消息
消息队列->>应用B : 广播消息
消息队列->>缓存服务 : 广播消息
应用B->>应用B : 刷新本地配置
缓存服务->>缓存服务 : 更新应用配置缓存
缓存服务->>应用B : 提供最新配置
```

**Diagram sources**
- [ResetIsvMchAppInfoConfigMQ.java](file://components/components-mq/src/main/java/com/unipay/components/mq/model/ResetIsvMchAppInfoConfigMQ.java#L16-L83)
- [ConfigContextService.java](file://sys-payment/src/main/java/com/unipay/pay/service/ConfigContextService.java#L32-L314)
- [ConfigContextQueryService.java](file://sys-payment/src/main/java/com/unipay/pay/service/ConfigContextQueryService.java#L29-L199)

**Section sources**
- [MchAppService.java](file://service/src/main/java/com/unipay/service/impl/MchAppService.java#L27-L96)
- [ResetIsvMchAppInfoConfigMQ.java](file://components/components-mq/src/main/java/com/unipay/components/mq/model/ResetIsvMchAppInfoConfigMQ.java#L16-L83)
- [ConfigContextService.java](file://sys-payment/src/main/java/com/unipay/pay/service/ConfigContextService.java#L32-L314)