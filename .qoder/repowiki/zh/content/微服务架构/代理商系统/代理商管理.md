
# 代理商管理

<cite>
**本文档引用文件**  
- [AgentInfo.java](file://core/src/main/java/com/unipay/core/entity/AgentInfo.java)
- [AgentInfoController.java](file://sys-manager/src/main/java/com/unipay/mgr/ctrl/agent/AgentInfoController.java)
- [AgentInfoController.java](file://sys-agent/src/main/java/com/unipay/agent/ctrl/agent/AgentInfoController.java)
- [AgentInfoService.java](file://service/src/main/java/com/unipay/service/impl/AgentInfoService.java)
- [AgentInfoMapper.java](file://service/src/main/java/com/unipay/service/mapper/AgentInfoMapper.java)
- [AgentInfoMapper.xml](file://service/src/main/resources/mapper/AgentInfoMapper.xml)
</cite>

## 目录
1. [引言](#引言)
2. [代理商实体类详解](#代理商实体类详解)
3. [REST API设计与实现](#rest-api设计与实现)
4. [增删改查操作实现](#增删改查操作实现)
5. [代理商层级关系维护](#代理商层级关系维护)
6. [状态管理与认证信息处理](#状态管理与认证信息处理)
7. [权限控制机制](#权限控制机制)
8. [代码示例](#代码示例)
9. [总结](#总结)

## 引言

本系统为支付平台提供完整的代理商管理体系，支持多级代理商架构。系统通过`AgentInfo`实体类定义代理商信息，结合`AgentInfoController`控制器和`AgentInfoService`服务层实现完整的增删改查功能。系统支持运营平台和代理商平台双重视角，确保不同角色拥有相应的操作权限和数据访问范围。

**本文档引用文件**  
- [AgentInfo.java](file://core/src/main/java/com/unipay/core/entity/AgentInfo.java)
- [AgentInfoController.java](file://sys-manager/src/main/java/com/unipay/mgr/ctrl/agent/AgentInfoController.java)

## 代理商实体类详解

`AgentInfo`类是代理商管理的核心数据模型，定义了代理商的所有属性和业务规则。

```mermaid
classDiagram
class AgentInfo {
+String agentNo
+String agentName
+String agentShortName
+String loginUsername
+Byte agentType
+String parentAgentNo
+Byte agentLevel
+String agentPath
+String contactName
+String contactTel
+String contactEmail
+String province
+String city
+String district
+String address
+BigDecimal profitRate
+Byte canDevelopAgent
+Byte canDevelopMch
+Byte state
+String remark
+Long initUserId
+Long createdUid
+String createdBy
+Date createdAt
+Date updatedAt
+static final byte STATE_STOP = 0
+static final byte STATE_NORMAL = 1
+static final byte TYPE_LEVEL_1 = 1
+static final byte TYPE_LEVEL_2 = 2
+static final byte TYPE_LEVEL_3 = 3
}
```

**图表来源**  
- [AgentInfo.java](file://core/src/main/java/com/unipay/core/entity/AgentInfo.java#L25-L201)

### 字段含义与数据约束

| 字段名 | 中文名称 | 数据类型 | 约束条件 | 业务规则 |
|-------|--------|--------|--------|--------|
| **agentNo** | 代理商号 | String | 主键，唯一 | 自动生成或手动设置，全局唯一 |
| **agentName** | 代理商名称 | String | 必填 | 代理商全称，用于展示 |
| **agentShortName** | 代理商简称 | String | 可选 | 代理商简称，用于界面显示 |
| **loginUsername** | 登录用户名 | String | 可选 | 代理商登录系统的用户名 |
| **agentType** | 代理商类型 | Byte | 可选 | 1-一级代理商, 2-二级代理商, 3-三级代理商等 |
| **parentAgentNo** | 上级代理商号 | String | 可选 | 指向上级代理商的agentNo |
| **agentLevel** | 代理商层级 | Byte | 自动计算 | 1-一级, 2-二级, 3-三级等 |
| **agentPath** | 代理商层级路径 | String | 自动构建 | 如: /A001/A002/A003 |
| **contactName** | 联系人姓名 | String | 必填 | 代理商联系人姓名 |
| **contactTel** | 联系人手机号 | String | 必填 | 11位手机号码 |
| **contactEmail** | 联系人邮箱 | String | 可选 | 邮箱地址格式验证 |
| **province** | 省份 | String | 可选 | 省级行政区划 |
| **city** | 城市 | String | 可选 | 市级行政区划 |
| **district** | 区县 | String | 可选 | 区县级行政区划 |
| **address** | 详细地址 | String | 可选 | 详细街道地址 |
| **profitRate** | 分润比例 | BigDecimal | 可选 | 0.00-100.00之间的数值 |
| **canDevelopAgent** | 是否允许发展下级代理商 | Byte | 0或1 | 0-否, 1-是 |
| **canDevelopMch** | 是否允许发展下级商户 | Byte | 0或1 | 0-否, 1-是 |
| **state** | 代理商状态 | Byte | 0或1 | 0-停用, 1-正常 |
| **remark** | 备注 | String | 可选 | 任意备注信息 |
| **initUserId** | 初始用户ID | Long | 可选 | 创建代理商时关联的系统用户ID |
| **createdUid** | 创建者用户ID | Long | 必填 | 创建该代理商的用户ID |
| **createdBy** | 创建者姓名 | String | 必填 | 创建该代理商的用户名 |
| **createdAt** | 创建时间 | Date | 必填 | 记录创建时间 |
| **updatedAt** | 更新时间 | Date | 必填 | 记录最后更新时间 |

**本节来源**  
- [AgentInfo.java](file://core/src/main/java/com/unipay/core/entity/AgentInfo.java#L25-L201)

### 业务规则说明

1. **状态管理**：代理商状态由`state`字段控制，`STATE_STOP(0)`表示停用，`STATE_NORMAL(1)`表示正常。
2. **层级控制**：通过`parentAgentNo`和`agentLevel`字段实现多级代理结构，`agentLevel`根据上级代理商自动计算。
3. **路径构建**：`agentPath`字段存储代理商的层级路径，如`/A001/A002/A003`，用于高效查询下级代理商。
4. **发展权限**：`canDevelopAgent`和`canDevelopMch`字段控制代理商是否可以发展下级代理商和商户。

## REST API设计与实现

系统提供了两套API接口，分别服务于运营平台和代理商平台，实现了统一的代理商管理功能。

```mermaid
sequenceDiagram
participant Client as "客户端"
participant Controller as "AgentInfoController"
participant Service as "AgentInfoService"
participant Mapper as "AgentInfoMapper"
participant DB as "数据库"
Client->>Controller : HTTP请求
Controller->>Service : 调用服务方法
Service->>Mapper : 执行数据操作
Mapper->>DB : SQL查询/更新
DB-->>Mapper : 返回结果
Mapper-->>Service : 返回实体
Service-->>Controller : 返回业务结果
Controller-->>Client : 返回API响应
```

**图表来源**  
- [AgentInfoController.java](file://sys-manager/src/main/java/com/unipay/mgr/ctrl/agent/AgentInfoController.java#L38-L239)
- [AgentInfoService.java](file://service/src/main/java/com/unipay/service/impl/AgentInfoService.java#L38-L389)

### API端点设计

| HTTP方法 | 路径 | 操作 | 权限要求 | 平台支持 |
|--------|-----|-----|--------|--------|
| **GET** | /api/agentInfo | 代理商信息列表 | ENT_AGENT_LIST | 运营平台、代理商平台 |
| **GET** | /api/agentInfo/{agentNo} | 代理商详情 | ENT_AGENT_INFO_VIEW | 运营平台、代理商平台 |
| **POST** | /api/agentInfo | 新增代理商 | ENT_AGENT_INFO_ADD | 运营平台、代理商平台 |
| **PUT** | /api/agentInfo/{agentNo} | 更新代理商信息 | ENT_AGENT_INFO_EDIT | 运营平台、代理商平台 |
| **DELETE** | /api/agentInfo/{agentNo} | 删除代理商 | ENT_AGENT_INFO_DELETE | 运营平台、代理商平台 |

### 参数验证机制

API接口通过以下方式实现参数验证：

1. **基础验证**：使用`@Parameter`注解定义参数要求，包括是否必填、数据类型等。
2. **业务验证**：在服务层进行业务逻辑验证，如代理商号重复检查。
3. **权限验证**：使用`@PreAuthorize`注解进行权限控制。
4. **日志记录**：使用`@MethodLog`注解记录操作日志。

```java
[AgentInfoController.add()](file://sys-manager/src/main/java/com/unipay/mgr/ctrl/agent/AgentInfoController.java#L104-L154)
```

**本节来源**  
- [AgentInfoController.java](file://sys-manager/src/main/java/com/unipay/mgr/ctrl/agent/AgentInfoController.java#L38-L239)
- [AgentInfoController.java](file://sys-agent/src/main/java/com/unipay/agent/ctrl/agent/AgentInfoController.java#L31-L184)

## 增删改查操作实现

### 创建新代理商

创建代理商操作涉及多个步骤，确保数据完整性和一致性。

```mermaid
flowchart TD
Start([开始创建代理商]) --> ValidateInput["验证输入参数"]
ValidateInput --> CheckAgentNo["检查代理商号是否重复"]
CheckAgentNo --> |存在| ReturnError["返回错误：代理商号已存在"]
CheckAgentNo --> |不存在| SetParent["设置上级代理商"]
SetParent --> SetCreateInfo["设置创建信息"]
SetCreateInfo --> SetDefaultState["设置默认状态"]
SetDefaultState --> SaveAgent["保存代理商信息"]
SaveAgent --> CreateUser["创建系统用户"]
CreateUser --> SetUserRole["分配角色权限"]
SetUserRole --> UpdateAgent["更新代理商信息"]
UpdateAgent --> End([创建完成])
```

**图表来源**  
- [AgentInfoService.createAgentWithUser()](file://service/src/main/java/com/unipay/service/impl/AgentInfoService.java#L64-L157)

#### 实现细节

1. **自动生成代理商号**：如果未提供`agentNo`，则调用`SeqKit.genAgentNo()`生成。
2. **构建层级路径**：调用`buildAgentPath()`方法构建`agentPath`。
3. **设置代理商层级**：根据是否有上级代理商自动计算`agentLevel`。
4. **创建关联用户**：同时创建系统用户并分配角色。

```java
[AgentInfoService.createAgentWithUser()](file://service/src/main/java/com/unipay/service/impl/AgentInfoService.java#L64-L157)
```

**本节来源**  
- [AgentInfoService.java](file://service/src/main/java/com/unipay/service/impl/AgentInfoService.java#L64-L157)
- [AgentInfoController.java](file://sys-agent/src/main/java/com/unipay/agent/ctrl/agent/AgentInfoController.java#L98-L137)

### 更新代理商信息

更新操作相对简单，主要更新代理商的基本信息。

```mermaid
flowchart TD
Start([开始更新]) --> GetAgentInfo["获取代理商信息"]
GetAgentInfo --> ValidateAgent["验证代理商存在"]
ValidateAgent --> |不存在| ReturnError["返回错误：代理商不存在"]
ValidateAgent --> |存在| UpdateFields["更新指定字段"]
UpdateFields --> SetUpdateTime["设置更新时间"]
SetUpdateTime --> SaveChanges["保存更改"]
SaveChanges --> |成功| ReturnSuccess["返回成功"]
SaveChanges --> |失败| ReturnError["返回错误：更新失败"]
```

**图表来源**  
- [AgentInfoService.updateAgent()](file://service/src/main/java/com/unipay/service/impl/AgentInfoService.java#L226-L229)

#### 实现细节

1. **强制更新时间**：每次更新都会自动设置`updatedAt`为当前时间。
2. **部分更新**：只更新提供的字段，其他字段保持不变。
3. **事务安全**：更新操作在事务中执行，确保数据一致性。

```java
[AgentInfoService.updateAgent()](file://service/src/main/java/com/unipay/service/impl/AgentInfoService.java#L226-L229)
```

**本节来源**  
- [AgentInfoService.java](file://service/src/main/java/com/unipay/service/impl/AgentInfoService.java#L226-L229)
- [AgentInfoController.java](file://sys-manager/src/main/java/com/unipay/mgr/ctrl/agent/AgentInfoController.java#L160-L178)

### 查询代理商列表

查询操作支持分页和多种查询条件。

```mermaid
flowchart TD
Start([开始查询]) --> BuildQuery["构建查询条件"]
BuildQuery --> AddPagination["添加分页参数"]
AddPagination --> ApplyFilters["应用过滤条件"]
ApplyFilters --> SortResults["按创建时间倒序"]
SortResults --> ExecuteQuery["执行数据库查询"]
ExecuteQuery --> ProcessResults["处理查询结果"]
ProcessResults --> ReturnResults["返回分页结果"]
```

**图表来源**  
- [AgentInfoService.selectPage()](file://service/src/main/java/com/unipay/service/impl/AgentInfoService.java#L162-L207)

#### 实现细节

1. **多条件查询**：支持按代理商号、名称、状态等条件查询。
2. **分页支持**：使用`IPage`接口实现分页功能。
3. **排序规则**：默认按创建时间倒序排列。

```java
[AgentInfoService.selectPage()