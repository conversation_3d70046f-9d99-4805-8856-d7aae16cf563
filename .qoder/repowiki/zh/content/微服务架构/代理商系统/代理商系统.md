# 代理商系统

<cite>
**本文档引用文件**   
- [AgentInfo.java](file://core/src/main/java/com/unipay/core/entity/AgentInfo.java)
- [AgentMchRelation.java](file://core/src/main/java/com/unipay/core/entity/AgentMchRelation.java)
- [AgentProfitRecord.java](file://core/src/main/java/com/unipay/core/entity/AgentProfitRecord.java)
- [PayOrderDivisionRecord.java](file://core/src/main/java/com/unipay/core/entity/PayOrderDivisionRecord.java)
- [AgentInfoService.java](file://service/src/main/java/com/unipay/service/impl/AgentInfoService.java)
- [AgentMchRelationService.java](file://service/src/main/java/com/unipay/service/impl/AgentMchRelationService.java)
- [AgentProfitRecordService.java](file://service/src/main/java/com/unipay/service/impl/AgentProfitRecordService.java)
- [PayOrderService.java](file://service/src/main/java/com/unipay/service/impl/PayOrderService.java)
- [PayOrderDivisionProcessService.java](file://sys-payment/src/main/java/com/unipay/pay/service/PayOrderDivisionProcessService.java)
- [CleanMchLoginAuthCacheMQ.java](file://components/components-mq/src/main/java/com/unipay/components/mq/model/CleanMchLoginAuthCacheMQ.java)
- [CleanMchLoginAuthCacheMQReceiver.java](file://sys-merchant/src/main/java/com/unipay/mch/mq/CleanMchLoginAuthCacheMQReceiver.java)
</cite>

## 目录
1. [系统概述](#系统概述)
2. [核心数据模型](#核心数据模型)
3. [代理商层级管理](#代理商层级管理)
4. [下级商户管理](#下级商户管理)
5. [分润计算与报表](#分润计算与报表)
6. [支付网关交互](#支付网关交互)
7. [消息处理机制](#消息处理机制)
8. [技术实现细节](#技术实现细节)

## 系统概述

代理商系统是统一支付平台的核心组成部分，支持多级代理模式的业务逻辑。该系统实现了代理商信息管理、下级商户管理、分润计算和报表生成等核心功能。系统通过与支付网关和运营平台的交互获取交易数据，用于计算各级代理商的分润。系统采用微服务架构，通过消息队列实现各服务间的异步通信，确保系统的高可用性和可扩展性。

**Section sources**
- [AgentInfo.java](file://core/src/main/java/com/unipay/core/entity/AgentInfo.java)
- [AgentMchRelation.java](file://core/src/main/java/com/unipay/core/entity/AgentMchRelation.java)
- [AgentProfitRecord.java](file://core/src/main/java/com/unipay/core/entity/AgentProfitRecord.java)

## 核心数据模型

### 代理商信息模型 (AgentInfo)
代理商信息模型定义了代理商的基本属性和层级关系，是多级代理模式的基础。

```mermaid
classDiagram
class AgentInfo {
+String agentNo
+String agentName
+String loginUsername
+Byte agentType
+String parentAgentNo
+Byte agentLevel
+String agentPath
+BigDecimal profitRate
+Byte canDevelopAgent
+Byte canDevelopMch
+Byte state
+String contactName
+String contactTel
+String contactEmail
}
```

**Diagram sources**
- [AgentInfo.java](file://core/src/main/java/com/unipay/core/entity/AgentInfo.java)

### 代理商商户关系模型 (AgentMchRelation)
该模型定义了代理商与商户之间的关联关系，支持直接和间接关系的管理。

```mermaid
classDiagram
class AgentMchRelation {
+Long id
+String agentNo
+String mchNo
+Byte relationType
+BigDecimal profitRate
+Byte state
}
```

**Diagram sources**
- [AgentMchRelation.java](file://core/src/main/java/com/unipay/core/entity/AgentMchRelation.java)

### 代理商分润记录模型 (AgentProfitRecord)
该模型用于记录代理商的分润明细，支持分润结算和报表生成。

```mermaid
classDiagram
class AgentProfitRecord {
+Long id
+String agentNo
+String mchNo
+String payOrderId
+Long orderAmount
+Long mchFeeAmount
+BigDecimal profitRate
+Long profitAmount
+Date profitDate
+Byte state
+Date settleTime
}
```

**Diagram sources**
- [AgentProfitRecord.java](file://core/src/main/java/com/unipay/core/entity/AgentProfitRecord.java)

## 代理商层级管理

### 代理商创建与层级构建
代理商系统支持多级代理模式，通过`AgentInfoService`实现代理商的创建和层级管理。当创建新代理商时，系统会自动构建其层级路径和层级编号。

```mermaid
sequenceDiagram
participant 前端 as 前端应用
participant AgentInfoService as AgentInfoService
participant 数据库 as 数据库
前端->>AgentInfoService : createAgentWithUser(agentInfo, password)
AgentInfoService->>AgentInfoService : 自动生成agentNo
AgentInfoService->>AgentInfoService : 构建agentPath
AgentInfoService->>AgentInfoService : 计算agentLevel
AgentInfoService->>数据库 : 保存代理商信息
AgentInfoService->>数据库 : 创建关联系统用户
数据库-->>AgentInfoService : 操作结果
AgentInfoService-->>前端 : 返回创建结果
```

**Diagram sources**
- [AgentInfoService.java](file://service/src/main/java/com/unipay/service/impl/AgentInfoService.java#L38-L389)

### 代理商层级路径管理
系统使用`agentPath`字段存储代理商的层级路径，格式为`/A001/A002/A003`，便于快速查询所有下级代理商。

```mermaid
flowchart TD
Start([创建代理商]) --> CheckParent{"是否有上级代理商?"}
CheckParent --> |否| SetLevel1["设置agentLevel=1<br/>agentPath=/[agentNo]"]
CheckParent --> |是| GetParent["获取上级代理商信息"]
GetParent --> CalcLevel["计算agentLevel=parentLevel+1"]
CalcLevel --> BuildPath["构建agentPath=parentPath/[agentNo]"]
BuildPath --> Save["保存代理商信息"]
Save --> End([完成])
```

**Diagram sources**
- [AgentInfoService.java](file://service/src/main/java/com/unipay/service/impl/AgentInfoService.java#L331-L342)

### 代理商查询与统计
系统提供多种查询方法，支持按代理商号、名称、状态等条件进行查询，并可统计下级代理商数量。

```mermaid
classDiagram
class AgentInfoService {
+AgentInfo getByAgentNo(String agentNo)
+IPage<AgentInfo> selectPage(IPage<?> page, AgentInfo agentInfo, String currentAgentNo)
+boolean isExistAgentNo(String agentNo)
+List<String> getAllSubAgentNos(String agentNo)
+long countAllSubAgents(String agentNo)
}
AgentInfoService --> AgentInfo : "管理"
```

**Diagram sources**
- [AgentInfoService.java](file://service/src/main/java/com/unipay/service/impl/AgentInfoService.java)

## 下级商户管理

### 代理商商户关系创建
系统通过`AgentMchRelationService`管理代理商与商户的关系，支持创建直接和间接关系。

```mermaid
sequenceDiagram
participant 前端 as 前端应用
participant AgentMchRelationService as AgentMchRelationService
participant AgentInfoService as AgentInfoService
participant 数据库 as 数据库
前端->>AgentMchRelationService : createHierarchicalRelations(agentNo, mchNo, profitRate)
AgentMchRelationService->>AgentMchRelationService : 创建直接关系
AgentMchRelationService->>AgentInfoService : 获取上级代理商
loop 为每个上级代理商创建间接关系
AgentMchRelationService->>数据库 : 检查间接关系是否存在
数据库-->>AgentMchRelationService : 查询结果
AgentMchRelationService->>AgentMchRelationService : 创建间接关系(分润比例=0)
end
数据库-->>AgentMchRelationService : 操作结果
AgentMchRelationService-->>前端 : 返回创建结果
```

**Diagram sources**
- [AgentMchRelationService.java](file://service/src/main/java/com/unipay/service/impl/AgentMchRelationService.java#L125-L170)

### 代理商商户关系模型
系统区分直接商户和间接商户，确保分润计算的准确性。

```mermaid
classDiagram
class AgentMchRelationService {
+List<String> getMchNosByAgentNo(String agentNo)
+List<String> getAgentNosByMchNo(String mchNo)
+IPage<AgentMchRelation> selectPage(IPage<AgentMchRelation> page, String agentNo, String mchNo, Byte relationType)
+boolean createRelation(String agentNo, String mchNo, Byte relationType, BigDecimal profitRate, Long createdUid, String createdBy)
+boolean createHierarchicalRelations(String agentNo, String mchNo, BigDecimal profitRate, Long createdUid, String createdBy)
+void fixExistingHierarchicalRelations()
+boolean updateProfitRate(String agentNo, String mchNo, BigDecimal profitRate)
+boolean deleteRelation(String agentNo, String mchNo)
}
AgentMchRelationService --> AgentMchRelation : "管理"
AgentMchRelationService --> AgentInfoService : "依赖"
```

**Diagram sources**
- [AgentMchRelationService.java](file://service/src/main/java/com/unipay/service/impl/AgentMchRelationService.java)

## 分润计算与报表

### 分润记录创建
系统在交易完成后自动创建分润记录，基于商户手续费和分润比例计算分润金额。

```mermaid
sequenceDiagram
participant 前端 as 前端应用
participant AgentProfitRecordService as AgentProfitRecordService
participant 数据库 as 数据库
前端->>AgentProfitRecordService : createProfitRecord(agentNo, mchNo, payOrderId, orderAmount, mchFeeAmount, profitRate)
AgentProfitRecordService->>AgentProfitRecordService : 计算profitAmount = mchFeeAmount * profitRate / 100
AgentProfitRecordService->>数据库 : 创建分润记录
数据库-->>AgentProfitRecordService : 操作结果
AgentProfitRecordService-->>前端 : 返回创建结果
```

**Diagram sources**
- [AgentProfitRecordService.java](file://service/src/main/java/com/unipay/service/impl/AgentProfitRecordService.java#L65-L83)

### 分润报表与统计
系统提供丰富的分润统计功能，支持按时间范围、状态等条件进行查询和统计。

```mermaid
classDiagram
class AgentProfitRecordService {
+IPage<AgentProfitRecord> selectProfitRecordPage(Page<AgentProfitRecord> page, String agentNo, String mchNo, Byte state, Date startDate, Date endDate)
+Map<String, Object> sumProfitAmount(String agentNo, Byte state, Date startDate, Date endDate)
+boolean createProfitRecord(String agentNo, String mchNo, String payOrderId, Long orderAmount, Long mchFeeAmount, BigDecimal profitRate)
+boolean batchCreateProfitRecords(List<AgentProfitRecord> records)
+boolean batchSettleProfitRecords(List<Long> ids)
+boolean batchCancelProfitRecords(List<Long> ids)
+List<AgentProfitRecord> getByPayOrderId(String payOrderId)
+boolean existsProfitRecord(String agentNo, String payOrderId)
+Map<String, Object> getTodayProfitStat(String agentNo)
+Map<String, Object> getMonthProfitStat(String agentNo)
}
AgentProfitRecordService --> AgentProfitRecord : "管理"
```

**Diagram sources**
- [AgentProfitRecordService.java](file://service/src/main/java/com/unipay/service/impl/AgentProfitRecordService.java)

## 支付网关交互

### 分账处理流程
系统通过`PayOrderDivisionProcessService`与支付网关交互，处理支付订单的分账请求。

```mermaid
sequenceDiagram
participant 前端 as 前端应用
participant PayOrderDivisionMQReceiver as PayOrderDivisionMQReceiver
participant PayOrderDivisionProcessService as PayOrderDivisionProcessService
participant 支付网关 as 支付网关
前端->>支付网关 : 发起支付请求
支付网关-->>前端 : 支付成功
支付网关->>PayOrderDivisionMQReceiver : 发送分账通知
PayOrderDivisionMQReceiver->>PayOrderDivisionProcessService : 处理分账请求
PayOrderDivisionProcessService->>PayOrderDivisionProcessService : 查询分账接收方
PayOrderDivisionProcessService->>PayOrderDivisionProcessService : 计算分账金额
PayOrderDivisionProcessService->>支付网关 : 调用分账接口
支付网关-->>PayOrderDivisionProcessService : 返回分账结果
PayOrderDivisionProcessService->>数据库 : 更新分账记录状态
PayOrderDivisionProcessService-->>PayOrderDivisionMQReceiver : 返回处理结果
```

**Diagram sources**
- [PayOrderDivisionProcessService.java](file://sys-payment/src/main/java/com/unipay/pay/service/PayOrderDivisionProcessService.java#L58-L188)

### 分账金额计算
系统精确计算分账金额，确保资金安全。

```mermaid
flowchart TD
Start([开始分账处理]) --> GetOrder["获取支付订单信息"]
GetOrder --> CheckState{"分账状态正确?"}
CheckState --> |否| Error([抛出异常])
CheckState --> |是| UpdateState["更新订单状态为分账处理中"]
UpdateState --> CheckResend{"是否重发?"}
CheckResend --> |是| QueryDB["从数据库查询分账记录"]
CheckResend --> |否| QueryReceiver["查询分账接收方"]
QueryReceiver --> CalcAmount["计算分账总金额"]
CalcAmount --> GenRecords["生成分账记录"]
GenRecords --> CallGateway["调用支付网关分账接口"]
CallGateway --> HandleResult{"处理网关返回结果"}
HandleResult --> |成功| UpdateSuccess["更新分账记录为成功"]
HandleResult --> |失败| UpdateFail["更新分账记录为失败"]
HandleResult --> |受理| UpdateAccept["更新分账记录为已受理"]
UpdateSuccess --> FinishState["更新订单分账状态为完成"]
UpdateFail --> FinishState
UpdateAccept --> FinishState
FinishState --> End([返回处理结果])
```

**Diagram sources**
- [PayOrderDivisionProcessService.java](file://sys-payment/src/main/java/com/unipay/pay/service/PayOrderDivisionProcessService.java)

## 消息处理机制

### 商户登录缓存清理
系统通过消息队列实现跨服务的商户登录缓存清理，确保数据一致性。

```mermaid
sequenceDiagram
participant 发送方 as 消息发送方
participant MQ as 消息队列
participant 接收方 as CleanMchLoginAuthCacheMQReceiver
发送方->>MQ : 发送CleanMchLoginAuthCacheMQ消息
MQ->>接收方 : 接收消息
接收方->>接收方 : 解析userIdList
loop 遍历每个用户ID
接收方->>Redis : 查询用户缓存键
Redis-->>接收方 : 返回缓存键列表
loop 遍历每个缓存键
接收方->>Redis : 删除用户缓存
end
end
接收方-->>MQ : 确认消息处理完成
```

**Diagram sources**
- [CleanMchLoginAuthCacheMQ.java](file://components/components-mq/src/main/java/com/unipay/components/mq/model/CleanMchLoginAuthCacheMQ.java)
- [CleanMchLoginAuthCacheMQReceiver.java](file://sys-merchant/src/main/java/com/unipay/mch/mq/CleanMchLoginAuthCacheMQReceiver.java)

### 消息模型定义
消息模型定义了消息的结构和序列化方式。

```mermaid
classDiagram
class CleanMchLoginAuthCacheMQ {
+String MQ_NAME
+MsgPayload payload
+String getMQName()
+MQSendTypeEnum getMQType()
+String toMessage()
+static CleanMchLoginAuthCacheMQ build(List<Long> userIdList)
+static MsgPayload parse(String msg)
}
class MsgPayload {
+List<Long> userIdList
}
CleanMchLoginAuthCacheMQ --> MsgPayload : "包含"
CleanMchLoginAuthCacheMQ : "实现 IMQReceiver"
```

**Diagram sources**
- [CleanMchLoginAuthCacheMQ.java](file://components/components-mq/src/main/java/com/unipay/components/mq/model/CleanMchLoginAuthCacheMQ.java)

## 技术实现细节

### 多级分润算法实现
系统实现了高效的多级分润算法，支持批量处理和事务管理。

```mermaid
classDiagram
class PayOrderDivisionProcessService {
+ChannelRetMsg processPayOrderDivision(String payOrderId, Byte useSysAutoDivisionReceivers, List<CustomerDivisionReceiver> receiverList, Boolean isResend)
+PayOrderDivisionRecord genRecord(String batchOrderId, PayOrder payOrder, MchDivisionReceiver receiver, Long payOrderDivisionAmount, Long subDivisionAmount)
+List<MchDivisionReceiver> queryReceiver(Byte useSysAutoDivisionReceivers, PayOrder payOrder, List<CustomerDivisionReceiver> customerDivisionReceiverList)
}
class PayOrderService {
+Long calMchIncomeAmount(PayOrder dbPayOrder)
}
PayOrderDivisionProcessService --> PayOrderService : "依赖"
PayOrderDivisionProcessService --> MchDivisionReceiverService : "依赖"
PayOrderDivisionProcessService --> PayOrderDivisionRecordService : "依赖"
```

**Diagram sources**
- [PayOrderDivisionProcessService.java](file://sys-payment/src/main/java/com/unipay/pay/service/PayOrderDivisionProcessService.java)

### 代理商层级管理技术细节
系统采用路径前缀匹配的方式高效查询下级代理商，避免递归查询的性能问题。

```mermaid
flowchart TD
Start([查询下级代理商]) --> GetAgent["获取当前代理商信息"]
GetAgent --> CheckPath{"agentPath存在?"}
CheckPath --> |否| QueryDirect["查询直接下级代理商"]
CheckPath --> |是| BuildQuery["构建like查询条件: agentPath + '/%'"]
BuildQuery --> ExecuteQuery["执行数据库查询"]
ExecuteQuery --> ReturnResult["返回查询结果"]
QueryDirect --> ReturnResult
```

**Diagram sources**
- [AgentInfoService.java](file://service/src/main/java/com/unipay/service/impl/AgentInfoService.java#L162-L207)