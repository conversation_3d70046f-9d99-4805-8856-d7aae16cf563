# 消息处理

<cite>
**本文档引用的文件**  
- [CleanMchLoginAuthCacheMQ.java](file://components/components-mq/src/main/java/com/unipay/components/mq/model/CleanMchLoginAuthCacheMQ.java)
- [ResetAppConfigMQ.java](file://components/components-mq/src/main/java/com/unipay/components/mq/model/ResetAppConfigMQ.java)
- [CleanMchLoginAuthCacheMQReceiver.java](file://sys-merchant/src/main/java/com/unipay/mch/mq/CleanMchLoginAuthCacheMQReceiver.java)
- [ResetAppConfigMQReceiver.java](file://sys-merchant/src/main/java/com/unipay/mch/mq/ResetAppConfigMQReceiver.java)
- [RedisUtil.java](file://core/src/main/java/com/unipay/core/cache/RedisUtil.java)
- [CS.java](file://core/src/main/java/com/unipay/core/constants/CS.java)
- [SysConfigService.java](file://service/src/main/java/com/unipay/service/impl/SysConfigService.java)
- [AbstractMQ.java](file://components/components-mq/src/main/java/com/unipay/components/mq/model/AbstractMQ.java)
- [IMQSender.java](file://components/components-mq/src/main/java/com/unipay/components/mq/vender/IMQSender.java)
- [IMQMsgReceiver.java](file://components/components-mq/src/main/java/com/unipay/components/mq/vender/IMQMsgReceiver.java)
</cite>

## 目录
1. [引言](#引言)
2. [消息机制概述](#消息机制概述)
3. [核心消息模型](#核心消息模型)
4. [商户登录认证缓存清理机制](#商户登录认证缓存清理机制)
5. [系统配置重置机制](#系统配置重置机制)
6. [消息消费可靠性保障](#消息消费可靠性保障)
7. [跨系统消息处理架构](#跨系统消息处理架构)
8. [总结](#总结)

## 引言
本系统采用消息队列（MQ）实现服务间的异步通信，确保在分布式环境下数据的一致性与系统的高可用性。本文档重点阐述 `CleanMchLoginAuthCacheMQ` 和 `ResetAppConfigMQ` 两种核心消息的处理机制，涵盖消息定义、接收流程、缓存一致性维护及配置同步策略。

## 消息机制概述

系统通过统一的消息中间件组件 `components-mq` 实现跨服务通信。该机制基于发布/订阅模式，支持点对点（QUEUE）和广播（BROADCAST）两种消息类型，确保消息能够准确送达目标服务。

```mermaid
graph TB
Publisher["消息发布者<br/>(如：运营平台)"] --> MQ["消息队列<br/>(ActiveMQ/RocketMQ等)"]
MQ --> Receiver1["消息接收者1<br/>(商户系统)"]
MQ --> Receiver2["消息接收者2<br/>(代理商系统)"]
MQ --> Receiver3["消息接收者3<br/>(支付系统)"]
style Publisher fill:#9f9,stroke:#333
style MQ fill:#f9f,stroke:#333
style Receiver1 fill:#9cf,stroke:#333
style Receiver2 fill:#9cf,stroke:#333
style Receiver3 fill:#9cf,stroke:#333
```

**图示来源**  
- [IMQSender.java](file://components/components-mq/src/main/java/com/unipay/components/mq/vender/IMQSender.java)
- [IMQMsgReceiver.java](file://components/components-mq/src/main/java/com/unipay/components/mq/vender/IMQMsgReceiver.java)

## 核心消息模型

### CleanMchLoginAuthCacheMQ
该消息用于通知各子系统清理指定商户用户的登录认证缓存，确保用户权限变更后无法继续访问。

```mermaid
classDiagram
class CleanMchLoginAuthCacheMQ {
+static final String MQ_NAME = "QUEUE_CLEAN_MCH_LOGIN_AUTH_CACHE"
+MsgPayload payload
+getMQName() String
+getMQType() MQSendTypeEnum
+toMessage() String
+static build(Long[]) CleanMchLoginAuthCacheMQ
+static parse(String) MsgPayload
}
class MsgPayload {
-Long[] userIdList
}
class MQSendTypeEnum {
QUEUE
BROADCAST
}
CleanMchLoginAuthCacheMQ *-- MsgPayload : 包含
CleanMchLoginAuthCacheMQ --> MQSendTypeEnum : 使用
```

**图示来源**  
- [CleanMchLoginAuthCacheMQ.java](file://components/components-mq/src/main/java/com/unipay/components/mq/model/CleanMchLoginAuthCacheMQ.java)

### ResetAppConfigMQ
该消息用于广播系统配置重置指令，使所有子系统同步更新本地配置状态。

```mermaid
classDiagram
class ResetAppConfigMQ {
+static final String MQ_NAME = "BROADCAST_RESET_APP_CONFIG"
+MsgPayload payload
+getMQName() String
+getMQType() MQSendTypeEnum
+toMessage() String
+static build(String) ResetAppConfigMQ
+static parse(String) MsgPayload
}
class MsgPayload {
-String groupKey
}
ResetAppConfigMQ *-- MsgPayload : 包含
```

**图示来源**  
- [ResetAppConfigMQ.java](file://components/components-mq/src/main/java/com/unipay/components/mq/model/ResetAppConfigMQ.java)

## 商户登录认证缓存清理机制

当用户权限被撤销或账户被禁用时，系统通过 `CleanMchLoginAuthCacheMQ` 消息通知所有相关服务清理其Redis缓存中的登录令牌。

### 处理流程
```mermaid
flowchart TD
Start([接收到 CleanMchLoginAuthCacheMQ]) --> Parse["解析消息体<br/>获取 userIdList"]
Parse --> CheckEmpty{"用户ID列表为空?"}
CheckEmpty --> |是| LogEmpty["记录日志：用户ID为空"]
CheckEmpty --> |否| LoopStart["遍历每个用户ID"]
LoopStart --> FindKeys["根据用户ID查找Redis缓存键<br/>CS.getCacheKeyToken(sysUserId, '*')"]
FindKeys --> CheckKeys{"存在匹配的缓存键?"}
CheckKeys --> |否| NextUser
CheckKeys --> |是| DeleteKey["删除缓存键<br/>RedisUtil.del(cacheKey)"]
DeleteKey --> NextUser["处理下一个用户"]
NextUser --> EndLoop{"遍历完成?"}
EndLoop --> |否| LoopStart
EndLoop --> |是| LogSuccess["记录日志：缓存已清除"]
LogEmpty --> End([结束])
LogSuccess --> End
```

### 关键实现
- **消息类型**：`QUEUE`（点对点），确保仅一个消费者处理。
- **缓存键生成**：使用 `CS.getCacheKeyToken(sysUserId, "*")` 通配符匹配所有该用户的token。
- **缓存操作**：通过 `RedisUtil.keys()` 和 `RedisUtil.del()` 完成批量删除。

**本节来源**  
- [CleanMchLoginAuthCacheMQReceiver.java](file://sys-merchant/src/main/java/com/unipay/mch/mq/CleanMchLoginAuthCacheMQReceiver.java)
- [RedisUtil.java](file://core/src/main/java/com/unipay/core/cache/RedisUtil.java)
- [CS.java](file://core/src/main/java/com/unipay/core/constants/CS.java)

## 系统配置重置机制

当系统配置发生变更时，通过 `ResetAppConfigMQ` 消息广播通知所有子系统重新加载配置。

### 处理流程
```mermaid
sequenceDiagram
participant Publisher as 配置发布者
participant MQ as 消息队列
participant Receiver as 消息接收者
Publisher->>MQ : 发送 ResetAppConfigMQ<br/>(groupKey="applicationConfig")
MQ->>Receiver : 推送消息
Receiver->>Receiver : receive(payload)
Receiver->>SysConfigService : sysConfigService.initDBConfig(groupKey)
SysConfigService->>SysConfigService : selectByGroupKey(groupKey)
SysConfigService->>SysConfigService : 更新 APPLICATION_CONFIG 缓存
SysConfigService-->>Receiver : 返回
Receiver-->>MQ : 确认消费
```

### 关键实现
- **消息类型**：`BROADCAST`（广播），确保所有订阅者都能收到。
- **配置加载**：调用 `SysConfigService.initDBConfig()` 方法更新本地缓存。
- **线程安全**：`initDBConfig` 方法使用 `synchronized` 保证并发安全。

**本节来源**  
- [ResetAppConfigMQReceiver.java](file://sys-merchant/src/main/java/com/unipay/mch/mq/ResetAppConfigMQReceiver.java)
- [SysConfigService.java](file://service/src/main/java/com/unipay/service/impl/SysConfigService.java)

## 消息消费可靠性保障

为确保消息不丢失且能正确处理，系统实现了以下可靠性机制：

### 异常重试机制
`RedisUtil` 类内置了操作重试逻辑，防止因短暂网络波动导致缓存操作失败。

```java
private static <T> T executeWithRetry(Supplier<T> operation, int maxRetries) {
    for (int i = 0; i < maxRetries; i++) {
        try {
            return operation.get();
        } catch (Exception e) {
            if (i < maxRetries - 1) {
                Thread.sleep(100 * (i + 1)); // 递增等待
            }
        }
    }
    throw new RuntimeException("操作失败");
}
```

### 死信队列处理
虽然当前代码未直接体现，但底层MQ中间件（如ActiveMQ、RabbitMQ）支持配置死信队列（DLQ），用于捕获无法正常消费的消息，便于后续人工干预或重放。

### 消费确认机制
基于MQ中间件的ACK机制，确保只有当消息被成功处理后才从队列中移除，避免消息丢失。

**本节来源**  
- [RedisUtil.java](file://core/src/main/java/com/unipay/core/cache/RedisUtil.java)

## 跨系统消息处理架构

多个子系统（商户、代理商、运营平台、支付系统）均实现了相同的消息接收器，形成统一的分布式事件响应机制。

```mermaid
graph TD
subgraph "运营平台 (sys-manager)"
MGR[ResetAppConfigMQReceiver]
end
subgraph "商户系统 (sys-merchant)"
MCH[CleanMchLoginAuthCacheMQReceiver<br/>ResetAppConfigMQReceiver]
end
subgraph "代理商系统 (sys-agent)"
AGENT[CleanMchLoginAuthCacheMQReceiver<br/>ResetAppConfigMQReceiver]
end
subgraph "支付系统 (sys-payment)"
PAY[ResetAppConfigMQReceiver]
end
MQ["消息队列"] --> MGR
MQ --> MCH
MQ --> AGENT
MQ --> PAY
style MGR fill:#f96,stroke:#333
style MCH fill:#69f,stroke:#333
style AGENT fill:#69f,stroke:#333
style PAY fill:#69f,stroke:#333
style MQ fill:#f9f,stroke:#333
```

此架构确保了：
- **一致性**：所有系统对同一事件做出一致响应。
- **解耦**：发布者无需知道具体有哪些订阅者。
- **可扩展性**：新增系统只需实现对应接收器即可接入。

**图示来源**  
- [CleanMchLoginAuthCacheMQReceiver.java](file://sys-merchant/src/main/java/com/unipay/mch/mq/CleanMchLoginAuthCacheMQReceiver.java)
- [ResetAppConfigMQReceiver.java](file://sys-merchant/src/main/java/com/unipay/mch/mq/ResetAppConfigMQReceiver.java)

## 总结
本系统通过精心设计的消息队列机制，有效解决了分布式环境下的缓存一致性与配置同步难题。`CleanMchLoginAuthCacheMQ` 和 `ResetAppConfigMQ` 作为核心事件载体，分别保障了用户权限的即时生效与系统配置的全局一致。结合Redis操作重试等可靠性措施，构建了一个健壮、高效的异步通信体系，为系统的稳定运行提供了坚实基础。