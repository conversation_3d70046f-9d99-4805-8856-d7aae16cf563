
# 支付通道配置

<cite>
**本文档引用文件**   
- [MchPayPassage.java](file://core/src/main/java/com/unipay/core/entity/MchPayPassage.java)
- [MchPayPassageService.java](file://service/src/main/java/com/unipay/service/impl/MchPayPassageService.java)
- [MchPayPassageMapper.java](file://service/src/main/java/com/unipay/service/mapper/MchPayPassageMapper.java)
- [MchPayPassageConfigController.java](file://sys-manager/src/main/java/com/unipay/mgr/ctrl/merchant/MchPayPassageConfigController.java)
- [MchPayPassageConfigController.java](file://sys-merchant/src/main/java/com/unipay/mch/ctrl/merchant/MchPayPassageConfigController.java)
- [MchPayPassageConfigController.java](file://sys-agent/src/main/java/com/unipay/agent/ctrl/merchant/MchPayPassageConfigController.java)
- [PayInterfaceDefine.java](file://core/src/main/java/com/unipay/core/entity/PayInterfaceDefine.java)
- [PayWay.java](file://core/src/main/java/com/unipay/core/entity/PayWay.java)
- [PayOrderProcessService.java](file://sys-payment/src/main/java/com/unipay/pay/service/PayOrderProcessService.java)
- [ConfigContextQueryService.java](file://sys-payment/src/main/java/com/unipay/pay/service/ConfigContextQueryService.java)
- [MchAppConfigContext.java](file://sys-payment/src/main/java/com/unipay/pay/model/MchAppConfigContext.java)
- [ChannelCertConfigKitBean.java](file://sys-payment/src/main/java/com/unipay/pay/util/ChannelCertConfigKitBean.java)
- [PaywayUtil.java](file://sys-payment/src/main/java/com/unipay/pay/util/PaywayUtil.java)
</cite>

## 目录
1. [引言](#引言)
2. [核心实体类 MchPayPassage](#核心实体类-mchpaypassage)
3. [支付通道配置接口](#支付通道配置接口)
4. [支付通道选择策略](#支付通道选择策略)
5. [支付通道路由机制](#支付通道路由机制)
6. [支付渠道认证信息管理](#支付渠道认证信息管理)
7. [配置示例](#配置示例)
8. [总结](#总结)

## 引言
支付通道配置子系统是统一支付平台的核心组件之一，负责将商户应用与具体的支付方式（如微信支付、支付宝）进行关联，并管理其启用状态和优先级。本系统通过灵活的配置机制，支持商户根据业务需求启用或禁用特定的支付方式，并通过统一的接口进行管理。本文档详细阐述了MchPayPassage实体类的设计、配置接口的实现、支付通道的选择策略以及在统一下单时的路由机制，同时提供了实际配置示例和安全的认证信息管理方案。

## 核心实体类 MchPayPassage

`MchPayPassage` 实体类是支付通道配置的核心数据模型，用于表示商户应用与支付接口之间的关联关系。该类定义了支付通道的基本属性，包括商户号、应用ID、支付接口代码、支付方式、费率、风控数据和状态等。

```mermaid
classDiagram
class MchPayPassage {
+Long id
+String mchNo
+String appId
+String ifCode
+String wayCode
+BigDecimal rate
+String riskConfig
+Byte state
+Date createdAt
+Date updatedAt
+static LambdaQueryWrapper<MchPayPassage> gw()
}
```

**图源**
- [MchPayPassage.java](file://core/src/main/java/com/unipay/core/entity/MchPayPassage.java#L25-L99)

**说明**：
- `mchNo`：商户号，标识商户的唯一编号。
- `appId`：应用ID，标识商户应用的唯一编号。
- `ifCode`：支付接口代码，如 `wxpay`、`alipay` 等。
- `wayCode`：支付方式代码，如 `wxpay_jsapi`、`alipay_pc` 等。
- `rate`：支付方式费率，表示该支付方式的手续费率。
- `state`：状态，0表示停用，1表示启用。

**本节源**
- [MchPayPassage.java](file://core/src/main/java/com/unipay/core/entity/MchPayPassage.java#L25-L99)

## 支付通道配置接口

支付通道配置接口由 `MchPayPassageConfigController` 提供，支持商户通过API对支付通道进行增删改查操作。该控制器提供了多个端点，用于查询支付方式列表、查询可用的支付接口列表以及更新商户支付通道。

### 查询支付方式列表

该接口用于查询商户应用支持的支付方式列表，并添加是否已配置支付通道的状态信息。

```java
@GetMapping
public ApiPageRes<PayWay> list() {
    // 查询支付方式列表
    IPage<PayWay> payWayPage = payWayService.page(getIPage(), wrapper);
    // 添加支付通道状态
    for (PayWay payWay : payWayPage.getRecords()) {
        payWay.addExt("passageState", CS.NO);
        for (MchPayPassage mchPayPassage : mchPayPassageList) {
            if (payWay.getWayCode().equals(mchPayPassage.getWayCode()) && mchPayPassage.getState() == CS.YES) {
                payWay.addExt("passageState", CS.YES);
                break;
            }
        }
    }
    return ApiPageRes.pages(payWayPage);
}
```

### 查询可用的支付接口列表

该接口根据应用ID和支付方式代码，查询可用的支付接口列表，并返回每个接口的通道状态和费率信息。

```java
@GetMapping("/availablePayInterface/{appId}/{wayCode}")
public ApiRes availablePayInterface(@PathVariable("appId") String appId, @PathVariable("wayCode") String wayCode) {
    // 查询可用支付接口列表
    List<JSONObject> list = mchPayPassageService.selectAvailablePayInterfaceList(wayCode, appId, CS.INFO_TYPE_MCH_APP, mchInfo.getType());
    return ApiRes.ok(list);
}
```

### 更新商户支付通道

该接口用于更新商户支付通道的配置，支持批量操作。商户可以通过该接口启用或禁用特定的支付方式。

```java
@PostMapping
public ApiRes saveOrUpdate() {
    String reqParams = getValStringRequired("reqParams");
    List<MchPayPassage> mchPayPassageList = JSONArray.parseArray(reqParams, MchPayPassage.class);
    mchPayPassageService.saveOrUpdateBatchSelf(mchPayPassageList, getCurrentMchNo());
    return ApiRes.ok();
}
```

**本节源**
- [MchPayPassageConfigController.java](file://sys-manager/src/main/java/com/unipay/mgr/ctrl/merchant/MchPayPassageConfigController.java#L42-L178)
- [MchPayPassageConfigController.java](file://sys-merchant/src/main/java/com/unipay/mch/ctrl/merchant/MchPayPassageConfigController.java#L40-L186)
- [MchPayPassageConfigController.java](file://sys-agent/src/main/java/com/unipay/agent/ctrl/merchant/MchPayPassageConfigController.java#L40-L186)

## 支付通道选择策略

支付通道选择策略由 `MchPayPassageService` 实现，主要通过 `findMchPayPassage` 方法来确定商户在特定支付方式下应使用的支付接口。该方法首先查询商户在指定应用和支付方式下的所有可用支付通道，然后根据支付接口的启用状态选择一个可用的通道。

```java
public MchPayPassage findMchPayPassage(String mchNo, String appId, String wayCode) {
    List<MchPayPassage> list = list(MchPayPassage.gw()
            .eq(MchPayPassage::getMchNo, mchNo)
            .eq(MchPayPassage::getAppId, appId)
            .eq(MchPayPassage::getState, CS.YES)
            .eq(MchPayPassage::getWayCode, wayCode)
    );
    if (list.isEmpty()) {
        return null;
    } else {
        HashMap<String, MchPayPassage> mchPayPassageMap = new HashMap<>();
        for (MchPayPassage mchPayPassage : list) {
            mchPayPassageMap.put(mchPayPassage.getIfCode(), mchPayPassage);
        }
        PayInterfaceDefine interfaceDefine = payInterfaceDefineService
                .getOne(PayInterfaceDefine.gw()
                        .select(PayInterfaceDefine::getIfCode, PayInterfaceDefine::getState)
                        .eq(PayInterfaceDefine::getState, CS.YES)
                        .in(PayInterfaceDefine::getIfCode, mchPayPassageMap.keySet()), false);
        if (interfaceDefine != null) {
            return mchPayPassageMap.get(interfaceDefine.getIfCode());
        }
    }
    return null;
}
```

**本节源**
- [MchPayPassageService.java](file://service/src/main/java/com/unipay/service/impl/MchPayPassageService.java#L29-L124)

## 支付通道路由机制

在统一下单时，系统根据商户的支付通道配置，将支付请求路由到正确的支付服务。这一过程由 `PayOrderProcessService` 和 `ConfigContextQueryService` 协同完成。

### 支付订单处理

`PayOrderProcessService` 负责处理支付订单的生命周期，包括支付成功后的业务逻辑和自动分账处理。

```java
public void confirmSuccess(PayOrder payOrder) {
    payOrder = payOrderService.getById(payOrder.getPayOrderId());
    payOrder.setState(PayOrder.STATE_SUCCESS);
    this.updatePayOrderAutoDivision(payOrder);
    payMchNotifyService.payOrderNotify(payOrder);
}
```

### 配置上下文查询

`ConfigContextQueryService` 负责查询商户的配置上下文，包括商户信息、应用信息和支付接口配置等。

```java
public MchAppConfigContext queryMchInfoAndAppInfo(String mchNo, String mchAppId) {
    if (isCache()) {
        return configContextService.getMchAppConfigContext(mchNo, mchAppId);
    }
    MchInfo mchInfo = mchInfoService.getById(mchNo);
    MchApp mchApp = queryMchApp(mchNo, mchAppId);
    if (mchInfo == null || mchApp == null) {
        return null;
    }
    MchAppConfigContext result = new MchAppConfigContext();
    result.setMchInfo(mchInfo);
    result.setMchNo(mchNo);
    result.setMchType(mchInfo.getType());
    result.setMchApp(mchApp);
    result.setAppId(mchAppId);
    return result;
}
```

**本节源**
- [PayOrderProcessService.java](file://sys-payment/src/main/java/com/unipay/pay/service/PayOrderProcessService.java#L20-L97)
- [ConfigContextQueryService.java](file://sys-payment/src/main/java/com/unipay/pay/service/ConfigContextQueryService.java#L29-L199)

## 支付渠道认证信息管理

支付渠道的认证信息（如证书、密钥）通过 `ChannelCertConfigKitBean` 进行管理。该类提供了获取证书文件路径和下载证书文件的方法，确保认证信息的安全性和可访问性。

```java
@Component
public class ChannelCertConfigKitBean {
    @Autowired private OssYmlConfig ossYmlConfig;
    @Autowired private IOssService ossService;

    public String getCertFilePath(String certFilePath) {
        return getCertFile(certFilePath).getAbsolutePath();
    }

    public File getCertFile(String certFilePath) {
        File certFile = new File(ossYmlConfig.getOss().getFilePrivatePath() + File.separator + certFilePath);
        if (certFile.exists()) {
            return certFile;
        }
        boolean isLocalSave = OssServiceTypeEnum.LOCAL.getServiceName().equals(ossYmlConfig.getOss().getServiceType());
        if (isLocalSave) {
            return certFile;
        }
        if (new File(certFile.getAbsolutePath() + ".notexists").exists()) {
            return certFile;
        }
        if (!certFile.getParentFile().exists()) {
            certFile.getParentFile().mkdirs();
        }
        return downloadFile(certFilePath, certFile);
    }

    private synchronized File downloadFile(String dbCertFilePath, File certFile) {
        boolean isSuccess = ossService.downloadFile(OssSavePlaceEnum.PRIVATE, dbCertFilePath, certFile.getAbsolutePath());
        if (isSuccess) {
            return new File(certFile.getAbsolutePath());
        }
        try {
            new File(certFile.getAbsolutePath() + ".notexists").createNewFile();
        } catch (IOException e) {
        }
        return certFile;
    }
}
```

**本节源**
- [ChannelCertConfigKitBean.java](file://sys-payment/src/main/java/com/unipay/pay/util/ChannelCertConfigKitBean.java#L16-L79)

## 配置示例

以下是一个实际的支付通道配置示例，展示了如何为商户应用启用微信支付和支付宝支付。

```json
[
    {
        "mchNo": "MCH123456",
        "appId": "APP789012",
        "ifCode": "wxpay",
        "wayCode": "wxpay_jsapi",
        "rate": 0.006,
        "state": 1
    },
    {
        "mchNo": "MCH123456",
        "appId": "APP789012",
        "ifCode": "alipay",
        "wayCode": "alipay_pc",
        "rate": 0.005,
        "state": 1
    }
]
```

**本节源**
- [MchPayPassage.java](file://core/src/main/java/com/unipay/core/entity/MchPayPassage.java#L25-L99)

## 总结

支付通道配置子系统通过 `MchPayPassage` 实体类和 `MchPay