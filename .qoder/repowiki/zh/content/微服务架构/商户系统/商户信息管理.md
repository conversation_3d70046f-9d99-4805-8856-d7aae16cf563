# 商户信息管理

<cite>
**本文档引用的文件**   
- [MchInfo.java](file://core/src/main/java/com/unipay/core/entity/MchInfo.java)
- [MchInfoController.java](file://sys-manager/src/main/java/com/unipay/mgr/ctrl/merchant/MchInfoController.java)
- [MchInfoController.java](file://sys-agent/src/main/java/com/unipay/agent/ctrl/merchant/MchInfoController.java)
- [MchInfoService.java](file://service/src/main/java/com/unipay/service/impl/MchInfoService.java)
- [CS.java](file://core/src/main/java/com/unipay/core/constants/CS.java)
- [SysUser.java](file://core/src/main/java/com/unipay/core/entity/SysUser.java)
- [SysUserAuthService.java](file://service/src/main/java/com/unipay/service/impl/SysUserAuthService.java)
- [MchApp.java](file://core/src/main/java/com/unipay/core/entity/MchApp.java)
</cite>

## 目录
1. [引言](#引言)
2. [MchInfo实体类详解](#mchinfo实体类详解)
3. [商户信息管理API接口](#商户信息管理api接口)
4. [MchInfoService业务逻辑](#mchinfoservice业务逻辑)
5. [系统集成与数据一致性](#系统集成与数据一致性)
6. [常见问题与解决方案](#常见问题与解决方案)
7. [总结](#总结)

## 引言

商户信息管理子系统是统一支付平台的核心模块之一，负责商户的全生命周期管理。该系统为运营平台（sys-manager）和代理商系统（sys-agent）提供统一的商户信息管理接口，支持商户的创建、查询、更新和删除操作。本系统通过RESTful API提供服务，确保商户信息的完整性和一致性，同时与核心模块和数据访问层紧密集成，实现高效的数据管理和业务处理。

**Section sources**
- [MchInfo.java](file://core/src/main/java/com/unipay/core/entity/MchInfo.java#L1-L20)
- [MchInfoController.java](file://sys-manager/src/main/java/com/unipay/mgr/ctrl/merchant/MchInfoController.java#L1-L30)

## MchInfo实体类详解

### 字段含义与业务约束

`MchInfo`实体类定义了商户信息的核心数据结构，包含商户的基本信息、联系人信息和状态管理等字段。

```mermaid
classDiagram
class MchInfo {
+String mchNo
+String mchName
+String mchShortName
+Byte type
+String isvNo
+String agentNo
+String contactName
+String contactTel
+String contactEmail
+Byte state
+String remark
+Long initUserId
+Long createdUid
+String createdBy
+Date createdAt
+Date updatedAt
}
```

**Diagram sources**
- [MchInfo.java](file://core/src/main/java/com/unipay/core/entity/MchInfo.java#L24-L140)

#### 核心字段说明

- **商户号 (mchNo)**: 商户的唯一标识符，系统自动生成，以"M"开头后接时间戳，确保全局唯一性。
- **商户名称 (mchName)**: 商户的全称，用于标识商户身份，必填字段。
- **商户简称 (mchShortName)**: 商户的简称，用于界面显示，必填字段。
- **类型 (type)**: 商户类型，1表示普通商户，2表示特约商户（服务商模式），决定商户的业务模式。
- **服务商号 (isvNo)**: 当商户类型为特约商户时，关联的服务商编号，用于服务商模式下的业务处理。
- **所属代理商号 (agentNo)**: 商户所属代理商的编号，用于代理商层级管理。
- **联系人信息**: 包括姓名、手机号和邮箱，用于商户的日常联系和通知。
- **状态 (state)**: 商户状态，0表示停用，1表示正常，控制商户的业务可用性。

#### 业务约束与常量定义

系统通过`CS`常量类定义了商户相关的业务规则和约束：

```mermaid
classDiagram
class CS {
+byte MCH_TYPE_NORMAL
+byte MCH_TYPE_ISVSUB
+int PUB_USABLE
+int PUB_DISABLE
+String DEFAULT_PWD
}
```

**Diagram sources**
- [CS.java](file://core/src/main/java/com/unipay/core/constants/CS.java#L13-L203)

- **商户类型约束**: `MCH_TYPE_NORMAL`（1）表示普通商户，`MCH_TYPE_ISVSUB`（2）表示特约商户，确保商户类型的合法性。
- **状态约束**: `PUB_USABLE`（1）表示正常状态，`PUB_DISABLE`（0）表示停用状态，统一管理商户的启用和停用。
- **默认密码**: `DEFAULT_PWD`定义了系统的默认密码，用于新创建商户的初始登录。

**Section sources**
- [MchInfo.java](file://core/src/main/java/com/unipay/core/entity/MchInfo.java#L24-L140)
- [CS.java](file://core/src/main/java/com/unipay/core/constants/CS.java#L13-L203)

## 商户信息管理API接口

### RESTful API端点

`MchInfoController`提供了完整的RESTful API接口，支持商户信息的CRUD操作。系统为运营平台和代理商系统提供了不同的控制器实现，确保权限隔离和业务逻辑的独立性。

```mermaid
sequenceDiagram
participant Client as "客户端"
participant Controller as "MchInfoController"
participant Service as "MchInfoService"
participant DB as "数据库"
Client->>Controller : GET /api/mchInfo
Controller->>Service : 查询商户列表
Service->>DB : 执行查询
DB-->>Service : 返回商户数据
Service-->>Controller : 返回结果
Controller-->>Client : 返回分页数据
Client->>Controller : POST /api/mchInfo
Controller->>Service : 创建商户
Service->>DB : 保存商户信息
DB-->>Service : 返回结果
Service-->>Controller : 返回成功
Controller-->>Client : 返回成功响应
```

**Diagram sources**
- [MchInfoController.java](file://sys-manager/src/main/java/com/unipay/mgr/ctrl/merchant/MchInfoController.java#L45-L254)
- [MchInfoService.java](file://service/src/main/java/com/unipay/service/impl/MchInfoService.java#L29-L166)

#### 运营平台API接口

运营平台的`MchInfoController`提供了全面的商户管理功能：

- **查询商户列表**: `GET /api/mchInfo` 支持按商户号、名称、代理商号、状态和类型进行筛选，返回分页结果。
- **新增商户信息**: `POST /api/mchInfo` 创建新商户，需要提供商户名称、简称、联系人信息等必填字段。
- **删除商户信息**: `DELETE /api/mchInfo/{mchNo}` 删除指定商户，同时清理相关数据。
- **更新商户信息**: `PUT /api/mchInfo/{mchNo}` 更新商户信息，支持重置密码等操作。
- **查询商户详情**: `GET /api/mchInfo/{mchNo}` 获取指定商户的详细信息。

#### 代理商系统API接口

代理商系统的`MchInfoController`在运营平台的基础上增加了权限控制：

- **权限隔离**: 代理商只能查看和管理自己代理的商户，通过`agentMchRelationService`验证权限。
- **新增商户**: 创建商户时自动关联当前代理商，简化代理商的商户管理流程。
- **密码设置**: 支持在创建商户时直接设置登录密码，提高用户体验。

**Section sources**
- [MchInfoController.java](file://sys-manager/src/main/java/com/unipay/mgr/ctrl/merchant/MchInfoController.java#L45-L254)
- [MchInfoController.java](file://sys-agent/src/main/java/com/unipay/agent/ctrl/merchant/MchInfoController.java#L39-L281)

## MchInfoService业务逻辑

### 数据验证与唯一性检查

`MchInfoService`实现了商户管理的核心业务逻辑，确保数据的完整性和一致性。

```mermaid
flowchart TD
Start([开始]) --> ValidateInput["验证输入参数"]
ValidateInput --> CheckIsv["检查服务商状态"]
CheckIsv --> SaveMch["保存商户基本信息"]
SaveMch --> CreateSysUser["创建系统用户"]
CreateSysUser --> CreateApp["创建默认应用"]
CreateApp --> UpdateInitUser["更新初始用户ID"]
UpdateInitUser --> End([成功])
CheckIsv --> |服务商不可用| ThrowError["抛出业务异常"]
SaveMch --> |保存失败| ThrowError
CreateSysUser --> |创建失败| ThrowError
CreateApp --> |创建失败| ThrowError
UpdateInitUser --> |更新失败| ThrowError
```

**Diagram sources**
- [MchInfoService.java](file://service/src/main/java/com/unipay/service/impl/MchInfoService.java#L46-L99)

#### 创建商户业务流程

`addMch`方法实现了创建商户的完整业务流程：

1. **服务商验证**: 当商户类型为特约商户时，检查关联的服务商状态是否正常。
2. **商户信息保存**: 将商户基本信息保存到数据库。
3. **系统用户创建**: 创建商户的初始登录用户，关联商户信息。
4. **默认应用创建**: 为商户创建默认应用，生成应用ID和密钥。
5. **初始用户更新**: 将创建的用户ID更新到商户记录中。

该流程通过`@Transactional`注解确保所有操作的原子性，任何一步失败都会回滚整个事务。

### 状态管理与数据清理

```mermaid
flowchart TD
Start([开始]) --> CheckExist["检查商户是否存在"]
CheckExist --> CheckPayData["检查交易数据"]
CheckPayData --> |存在交易数据| ThrowError["抛出异常"]
CheckPayData --> |无交易数据| RemovePayPassage["删除支付通道"]
RemovePayPassage --> RemoveInterfaceConfig["删除接口配置"]
RemoveInterfaceConfig --> RemoveApp["删除应用信息"]
RemoveApp --> RemoveUserAuth["删除用户认证"]
RemoveUserAuth --> RemoveUser["删除用户"]
RemoveUser --> RemoveMch["删除商户"]
RemoveMch --> End([成功])
```

**Diagram sources**
- [MchInfoService.java](file://service/src/main/java/com/unipay/service/impl/MchInfoService.java#L102-L165)

`removeByMchNo`方法实现了安全的商户删除逻辑：

1. **存在性检查**: 验证商户是否存在。
2. **交易数据检查**: 检查商户是否有交易数据，有则禁止删除。
3. **级联删除**: 按顺序删除支付通道、接口配置、应用信息、用户认证和用户。
4. **最终删除**: 删除商户记录本身。

该方法确保了数据的一致性和完整性，防止因删除操作导致的数据不一致问题。

**Section sources**
- [MchInfoService.java](file://service/src/main/java/com/unipay/service/impl/MchInfoService.java#L29-L166)

## 系统集成与数据一致性

### 与核心模块的集成

商户信息管理模块与系统核心模块紧密集成，确保数据的一致性和业务的连贯性。

```mermaid
graph TB
subgraph "商户信息管理"
MchInfoController[MchInfoController]
MchInfoService[MchInfoService]
end
subgraph "核心模块"
SysUser[SysUser]
MchApp[MchApp]
PayOrder[PayOrder]
end
MchInfoService --> SysUser
MchInfoService --> MchApp
MchInfoService --> PayOrder
MchInfoController --> MchInfoService
```

**Diagram sources**
- [MchInfoController.java](file://sys-manager/src/main/java/com/unipay/mgr/ctrl/merchant/MchInfoController.java#L45-L254)
- [MchInfoService.java](file://service/src/main/java/com/unipay/service/impl/MchInfoService.java#L29-L166)
- [SysUser.java](file://core/src/main/java/com/unipay/core/entity/SysUser.java#L23-L117)
- [MchApp.java](file://core/src/main/java/com/unipay/core/entity/MchApp.java#L22-L98)

- **用户系统集成**: 通过`SysUserService`创建和管理商户的登录用户，确保用户信息的一致性。
- **应用系统集成**: 通过`MchAppService`为商户创建默认应用，支持商户的业务扩展。
- **交易系统集成**: 通过`PayOrderService`检查商户的交易数据，确保数据完整性。

### 与数据访问层的集成

系统通过MyBatis Plus实现与数据库的交互，确保高效的数据访问和操作。

```mermaid
classDiagram
class MchInfoMapper {
+insert(MchInfo)
+updateById(MchInfo)
+deleteById(String)
+selectById(String)
+selectPage(IPage, Wrapper)
}
class MchInfoService {
+save(MchInfo)
+updateById(MchInfo)
+removeById(String)
+getById(String)
+page(IPage, Wrapper)
}
MchInfoService --> MchInfoMapper : "依赖"
```

**Diagram sources**
- [MchInfoMapper.java](file://service/src/main/java/com/unipay/service/mapper/MchInfoMapper.java#L14-L16)
- [MchInfoService.java](file://service/src/main/java/com/unipay/service/impl/MchInfoService.java#L29-L166)

- **数据访问层**: `MchInfoMapper`继承`BaseMapper`，提供基本的CRUD操作。
- **服务层**: `MchInfoService`继承`ServiceImpl`，封装业务逻辑，调用Mapper进行数据操作。
- **事务管理**: 通过`@Transactional`注解确保数据操作的原子性。

**Section sources**
- [MchInfoService.java](file://service/src/main/java/com/unipay/service/impl/MchInfoService.java#L29-L166)
- [MchInfoMapper.java](file://service/src/main/java/com/unipay/service/mapper/MchInfoMapper.java#L14-L16)

## 常见问题与解决方案

### 商户信息更新冲突

在多用户并发更新商户信息时，可能出现数据覆盖问题。系统通过以下机制解决：

1. **乐观锁**: 在`MchInfo`实体中包含`updatedAt`字段，更新时检查时间戳，防止数据覆盖。
2. **权限控制**: 不同角色只能修改特定字段，如运营平台可以修改所有字段，代理商只能修改部分字段。
3. **字段保护**: 在更新操作中，明确设置`setType(null)`和`setIsvNo(null)`，防止关键业务字段被意外修改。

### 数据一致性维护

系统通过多种机制确保数据的一致性：

1. **事务管理**: 所有涉及多个数据表的操作都使用`@Transactional`注解，确保操作的原子性。
2. **级联操作**: 删除商户时，自动清理相关联的用户、应用和配置信息。
3. **消息队列**: 使用MQ通知其他系统更新缓存，如删除用户登录缓存和重置应用配置。
4. **前置检查**: 在删除商户前检查是否存在交易数据，防止业务数据丢失。

**Section sources**
- [MchInfoService.java](file://service/src/main/java/com/unipay/service/impl/MchInfoService.java#L29-L166)
- [MchInfoController.java](file://sys-manager/src/main/java/com/unipay/mgr/ctrl/merchant/MchInfoController.java#L45-L254)

## 总结

商户信息管理子系统通过完善的实体设计、RESTful API接口和业务逻辑处理，实现了商户的全生命周期管理。系统通过`MchInfo`实体类定义了商户的核心数据结构，通过`MchInfoController`提供了标准化的API接口，通过`MchInfoService`实现了复杂的业务逻辑。系统与核心模块和数据访问层紧密集成，确保了数据的一致性和业务的完整性。通过权限控制、事务管理和消息队列等机制，解决了常见的数据一致性问题，为统一支付平台提供了稳定可靠的商户管理服务。

**Section sources**
- [MchInfo.java](file://core/src/main/java/com/unipay/core/entity/MchInfo.java#L1-L140)
- [MchInfoController.java](file://sys-manager/src/main/java/com/unipay/mgr/ctrl/merchant/MchInfoController.java#L1-L254)
- [MchInfoService.java](file://service/src/main/java/com/unipay/service/impl/MchInfoService.java#L1-L166)