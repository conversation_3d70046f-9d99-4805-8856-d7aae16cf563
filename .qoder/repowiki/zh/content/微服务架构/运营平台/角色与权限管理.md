
# 角色与权限管理

<cite>
**本文档引用文件**   
- [SysRoleController.java](file://sys-manager/src/main/java/com/unipay/mgr/ctrl/sysuser/SysRoleController.java)
- [SysRoleEntRelaController.java](file://sys-manager/src/main/java/com/unipay/mgr/ctrl/sysuser/SysRoleEntRelaController.java)
- [SysRole.java](file://core/src/main/java/com/unipay/core/entity/SysRole.java)
- [SysEntitlement.java](file://core/src/main/java/com/unipay/core/entity/SysEntitlement.java)
- [SysRoleEntRela.java](file://core/src/main/java/com/unipay/core/entity/SysRoleEntRela.java)
- [WebSecurityConfig.java](file://sys-manager/src/main/java/com/unipay/mgr/secruity/WebSecurityConfig.java)
- [JeeUserDetailsServiceImpl.java](file://sys-manager/src/main/java/com/unipay/mgr/secruity/JeeUserDetailsServiceImpl.java)
- [SysRoleService.java](file://service/src/main/java/com/unipay/service/impl/SysRoleService.java)
- [SysRoleEntRelaService.java](file://service/src/main/java/com/unipay/service/impl/SysRoleEntRelaService.java)
</cite>

## 目录
1. [引言](#引言)
2. [核心实体模型](#核心实体模型)
3. [角色管理控制器](#角色管理控制器)
4. [权限关联管理控制器](#权限关联管理控制器)
5. [权限体系与安全拦截](#权限体系与安全拦截)
6. [角色与权限配置流程](#角色与权限配置流程)
7. [权限验证与调试](#权限验证与调试)
8. [总结](#总结)

## 引言
本文档详细阐述了运营平台中角色与权限管理的实现机制。系统通过`SysRoleController`和`SysRoleEntRelaController`两个控制器协同工作，实现了角色的创建、修改、删除以及权限分配等核心功能。`SysRole`实体用于表示角色的基本信息，`SysEntitlement`实体定义了系统的权限点，而`SysRoleEntRela`实体则建立了角色与权限之间的关联关系。权限体系通过`WebSecurityConfig`进行安全拦截，并在用户登录时由`JeeUserDetailsServiceImpl`加载其权限信息。本文将深入解析这些组件的交互流程，并提供完整的操作示例。

## 核心实体模型

### SysRole (系统角色)
`SysRole`实体代表系统中的一个角色，是权限分配的基本单位。

**属性说明**
- **roleId**: 角色ID，以"ROLE_"开头，作为主键。
- **roleName**: 角色名称，用于在界面上显示。
- **sysType**: 所属系统，区分"运营平台(MGR)"和"商户中心(MCH)"。
- **belongInfoId**: 所属商户ID，对于平台角色，此值为0。
- **updatedAt**: 更新时间，用于排序。

```mermaid
classDiagram
class SysRole {
+String roleId
+String roleName
+String sysType
+String belongInfoId
+Date updatedAt
}
```

**Diagram sources**
- [SysRole.java](file://core/src/main/java/com/unipay/core/entity/SysRole.java#L22-L68)

### SysEntitlement (系统权限)
`SysEntitlement`实体定义了系统中所有可被控制的权限点。

**属性说明**
- **entId**: 权限ID，命名规范为`ENT_功能模块_子模块_操作`，例如`ENT_UR_ROLE_ADD`。
- **entName**: 权限名称，用于显示。
- **entType**: 权限类型，如`ML`(菜单)、`PB`(按钮)。
- **state**: 状态，`1`表示启用，`0`表示停用。
- **sysType**: 所属系统，与`SysRole`的`sysType`对应。

```mermaid
classDiagram
class SysEntitlement {
+String entId
+String entName
+String menuIcon
+String menuUri
+String entType
+Byte quickJump
+Byte state
+String pid
+Integer entSort
+String sysType
+Date createdAt
+Date updatedAt
}
```

**Diagram sources**
- [SysEntitlement.java](file://core/src/main/java/com/unipay/core/entity/SysEntitlement.java#L22-L116)

### SysRoleEntRela (角色权限关联)
`SysRoleEntRela`实体是一个关联表，用于建立`SysRole`和`SysEntitlement`之间的多对多关系。

**属性说明**
- **roleId**: 角色ID，与`SysRole.roleId`关联。
- **entId**: 权限ID，与`SysEntitlement.entId`关联。

```mermaid
classDiagram
class SysRoleEntRela {
+String roleId
+String entId
}
SysRole "1" -- "0..*" SysRoleEntRela : 包含
SysEntitlement "1" -- "0..*" SysRoleEntRela : 被分配
```

**Diagram sources**
- [SysRoleEntRela.java](file://core/src/main/java/com/unipay/core/entity/SysRoleEntRela.java#L21-L49)

## 角色管理控制器

`SysRoleController`负责处理角色的增删改查（CRUD）操作。

### 创建角色 (add)
创建角色的流程如下：
1.  生成以"ROLE_"开头的唯一`roleId`。
2.  设置`sysType`为`MGR`（运营平台）或`MCH`（商户中心）。
3.  保存`SysRole`记录。
4.  如果当前用户拥有`ENT_UR_ROLE_DIST`权限，并且提供了权限列表，则调用`SysRoleEntRelaService.resetRela()`方法建立权限关联。

```mermaid
sequenceDiagram
participant 前端
participant SysRoleController
participant SysRoleService
participant SysRoleEntRelaService
前端->>SysRoleController : POST /api/sysRoles
SysRoleController->>SysRoleService : 生成roleId, 保存角色
alt 用户有分配权限
SysRoleController->>SysRoleEntRelaService : resetRela(roleId, entIdList)
end
SysRoleController-->>前端 : 返回成功
```

**Section sources**
- [SysRoleController.java](file://sys-manager/src/main/java/com/unipay/mgr/ctrl/sysuser/SysRoleController.java#L107-L135)
- [SysRoleService.java](file://service/src/main/java/com/unipay/service/impl/SysRoleService.java#L24-L60)

### 更新角色 (update)
更新角色的流程如下：
1.  更新`SysRole`表中的角色基本信息（如名称）。
2.  如果提供了新的权限列表，则调用`SysRoleEntRelaService.resetRela()`方法更新权限关联。
3.  查询所有拥有该角色的用户，并调用`authService.refAuthentication()`刷新其在Redis中的认证信息，确保权限变更立即生效。

```mermaid
sequenceDiagram
participant 前端
participant SysRoleController
participant SysRoleService
participant SysRoleEntRelaService
participant SysUserRoleRelaService
participant AuthService
前端->>SysRoleController : PUT /api/sysRoles/{recordId}
SysRoleController->>SysRoleService : updateById()
alt 用户有分配权限
SysRoleController->>SysRoleEntRelaService : resetRela(recordId, entIdList)
SysRoleController->>SysUserRoleRelaService : list(角色关联用户)
SysRoleController->>AuthService : refAuthentication(sysUserIdList)
end
SysRoleController-->>前端 : 返回成功
```

**Section sources**
- [SysRoleController.java](file://sys-manager/src/main/java/com/unipay/mgr/ctrl/sysuser/SysRoleController.java#L138-L171)
- [AuthService.java](file://sys-manager/src/main/java/com/unipay/mgr/service/AuthService.java#L102-L148)

### 删除角色 (del)
删除角色的流程如下：
1.  检查该角色是否已被分配给任何用户（通过`SysUserRoleRela`表）。
2.  如果已被分配，则抛出业务异常，禁止删除。
3.  调用`SysRoleService.removeRole()`方法，该方法会同时删除`SysRole`记录和`SysRoleEntRela`中的关联记录。

```mermaid
flowchart TD
A[收到删除请求] --> B{角色是否已分配给用户?}
B --> |是| C[抛出异常，禁止删除]
B --> |否| D[删除SysRole记录]
D --> E[删除SysRoleEntRela关联记录]
E --> F[返回成功]
```

**Section sources**
- [SysRoleController.java](file://sys-manager/src/main/java/com/unipay/mgr/ctrl/sysuser/SysRoleController.java#L174-L189)
- [SysRoleService.java](file://service/src/main/java/com/unipay/service/impl/SysRoleService.java#L43-L56)

## 权限关联管理控制器

`SysRoleEntRelaController`提供了专门用于管理角色权限关联的接口。

### 重置角色权限 (relas)
该接口用于为指定角色重新分配权限。
1.  验证角色ID的有效性。
2.  接收一个JSON格式的权限ID列表`entIdListStr`。
3.  调用`SysRoleEntRelaService.resetRela()`方法，该方法会先删除该角色原有的所有权限关联，再插入新的关联。
4.  与`update`操作类似，会刷新所有拥有该角色用户的Redis缓存。

```mermaid
sequenceDiagram
participant 前端
participant SysRoleEntRelaController
participant SysRoleService
participant SysRoleEntRelaService
participant AuthService
前端->>SysRoleEntRelaController : POST /api/sysRoleEntRelas/relas/{roleId}
SysRoleEntRelaController->>SysRoleService : getOne(验证角色)
SysRoleEntRelaController->>SysRoleEntRelaService : resetRela(roleId, entIdList)
SysRoleEntRelaController->>AuthService : refAuthentication(sysUserIdList)
SysRoleEntRelaController-->>前端 : 返回成功
```

**Section sources**
- [SysRoleEntRelaController.java](file://sys-merchant/src/main/java/com/unipay/mch/ctrl/sysuser/SysRoleEntRelaController.java#L76-L96)
- [SysRoleEntRelaService.java](file://service/src/main/java/com/unipay/service/impl/SysRoleEntRelaService.java#L48-L61)

## 权限体系与安全拦截

### 安全配置 (WebSecurityConfig)
`WebSecurityConfig`定义了系统的安全规则，核心是`securityFilterChain`方法。
- **认证方式**: 基于JWT Token，无状态（STATELESS）。
- **访问控制**:
  - `/api/**` 开头的接口需要认证。
  - 静态资源、Swagger文档、前端路由等路径允许匿名访问。
- **过滤器**: 添加了`JeeAuthenticationTokenFilter`，用于解析Token并进行认证。

```mermaid
graph TB
    A[HTTP请求] --> B{请求路径}
    B -->|/api/**| C[需要认证]
    B -->|/webjars/**, /doc.html| D[允许匿名访问]
    B -->|/main/**, /