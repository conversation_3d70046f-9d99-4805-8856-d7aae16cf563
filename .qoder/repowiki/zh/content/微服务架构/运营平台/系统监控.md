
# 系统监控

<cite>
**本文档引用文件**  
- [MainChartController.java](file://sys-manager/src/main/java/com/unipay/mgr/ctrl/config/MainChartController.java)
- [MainChartController.java](file://sys-merchant/src/main/java/com/unipay/mch/ctrl/merchant/MainChartController.java)
- [MainChartController.java](file://sys-agent/src/main/java/com/unipay/agent/ctrl/merchant/MainChartController.java)
- [SysLog.java](file://core/src/main/java/com/unipay/core/entity/SysLog.java)
- [MethodLogAop.java](file://sys-manager/src/main/java/com/unipay/mgr/aop/MethodLogAop.java)
- [MethodLogAop.java](file://sys-merchant/src/main/java/com/unipay/mch/aop/MethodLogAop.java)
- [MethodLogAop.java](file://sys-agent/src/main/java/com/unipay/agent/aop/MethodLogAop.java)
- [SysLogService.java](file://service/src/main/java/com/unipay/service/impl/SysLogService.java)
- [PayOrderService.java](file://service/src/main/java/com/unipay/service/impl/PayOrderService.java)
- [application.yml](file://conf/devCommons/config/application.yml)
- [application.yml](file://sys-manager/src/main/resources/application.yml)
- [application.yml](file://sys-merchant/src/main/resources/application.yml)
- [application.yml](file://sys-agent/src/main/resources/application.yml)
</cite>

## 目录
1. [系统监控概述](#系统监控概述)
2. [数据聚合与可视化](#数据聚合与可视化)
3. [操作日志记录机制](#操作日志记录机制)
4. [日志配置管理](#日志配置管理)
5. [监控最佳实践](#监控最佳实践)

## 系统监控概述

本系统监控模块为运营平台提供全面的数据可视化和操作审计功能。通过MainChartController实现关键业务指标的聚合展示，为运营人员提供直观的数据看板。同时，系统通过AOP切面技术自动记录所有关键操作日志，确保系统操作的可追溯性和安全性。

**Section sources**
- [MainChartController.java](file://sys-manager/src/main/java/com/unipay/mgr/ctrl/config/MainChartController.java#L29-L114)
- [SysLog.java](file://core/src/main/java/com/unipay/core/entity/SysLog.java#L23-L104)

## 数据聚合与可视化

### MainChartController指标聚合

MainChartController负责聚合交易数据、订单状态等关键运营指标，为不同角色提供定制化的数据看板。系统通过多个API端点提供不同的统计维度：

```mermaid
flowchart TD
A[MainChartController] --> B[周交易总金额]
A --> C[商户/代理商数量统计]
A --> D[交易趋势统计]
A --> E[支付方式分布]
B --> F[payAmountWeek]
C --> G[numCount]
D --> H[payCount]
E --> I[payTypeCount]
```

**Diagram sources**
- [MainChartController.java](file://sys-manager/src/main/java/com/unipay/mgr/ctrl/config/MainChartController.java#L29-L114)

**Section sources**
- [MainChartController.java](file://sys-manager/src/main/java/com/unipay/mgr/ctrl/config/MainChartController.java#L29-L114)
- [PayOrderService.java](file://service/src/main/java/com/unipay/service/impl/PayOrderService.java#L36-L640)

### 多角色数据聚合策略

系统为不同角色（运营平台、商户、代理商）提供了差异化的数据聚合策略：

#### 运营平台数据聚合
运营平台的MainChartController直接聚合全系统数据，提供全局视角的统计信息。

#### 商户数据聚合
商户中心的MainChartController通过getCurrentMchNo()获取当前商户号，仅聚合该商户的数据，确保数据隔离。

#### 代理商数据聚合
代理商系统的MainChartController首先通过agentMchRelationService获取关联的商户号列表，然后批量聚合这些商户的数据，实现多商户的汇总统计。

```mermaid
classDiagram
class MainChartController {
+payAmountWeek()
+numCount()
+payCount()
+payWayCount()
}
class PayOrderService {
+mainPageWeekCount()
+mainPageNumCount()
+mainPagePayCount()
+mainPagePayTypeCount()
+mainPageWeekCountByMchNos()
+mainPageNumCountByMchNos()
+mainPagePayCountByMchNos()
+mainPagePayTypeCountByMchNos()
}
MainChartController --> PayOrderService : "调用"
```

**Diagram sources**
- [MainChartController.java](file://sys-agent/src/main/java/com/unipay/agent/ctrl/merchant/MainChartController.java#L39-L219)
- [PayOrderService.java](file://service/src/main/java/com/unipay/service/impl/PayOrderService.java#L36-L640)

**Section sources**
- [MainChartController.java](file://sys-agent/src/main/java/com/unipay/agent/ctrl/merchant/MainChartController.java#L39-L219)
- [PayOrderService.java](file://service/src/main/java/com/unipay/service/impl/PayOrderService.java#L36-L640)

## 操作日志记录机制

### SysLog实体类设计

SysLog实体类定义了系统操作日志的结构，包含以下关键字段：

```mermaid
erDiagram
SYS_LOG {
integer sysLogId PK
long userId
string userName
string userIp
string sysType
string methodName
string methodRemark
string reqUrl
string optReqParam
string optResInfo
datetime createdAt
}
```

**Diagram sources**
- [SysLog.java](file://core/src/main/java/com/unipay/core/entity/SysLog.java#L23-L104)

**Section sources**
- [SysLog.java](file://core/src/main/java/com/unipay/core/entity/SysLog.java#L23-L104)

### AOP日志切面实现

系统通过MethodLogAop切面自动记录关键操作日志，实现原理如下：

```mermaid
sequenceDiagram
participant Controller
participant AOP as MethodLogAop
participant Service as SysLogService
participant DB as 数据库
Controller->>AOP : 执行带@MethodLog注解的方法
AOP->>Controller : proceed() 执行原方法
Controller-->>AOP : 返回结果
AOP->>AOP : setBaseLogInfo() 设置基础日志信息
AOP->>AOP : 异步执行日志保存
AOP->>Service : save(sysLog)
Service->>DB : 持久化日志
```

**Diagram sources**
- [MethodLogAop.java](file://sys-manager/src/main/java/com/unipay/mgr/aop/MethodLogAop.java#L39-L151)
- [SysLogService.java](file://service/src/main/java/com/unipay/service/impl/SysLogService.java#L16-L19)

**Section sources**
- [MethodLogAop.java](file://sys-manager/src/main/java/com/unipay/mgr/aop/MethodLogAop.java#L39-L151)
- [SysLogService.java](file://service/src/main/java/com/unipay/service/impl/SysLogService.java#L16-L19)

### 日志记录流程

1. **切点定义**：通过@Pointcut("@annotation(com.unipay.core.aop.MethodLog)")定义切点，匹配所有带有MethodLog注解的方法。
2. **基础信息收集**：setBaseLogInfo()方法收集用户信息、IP地址、请求URL、方法名等基础信息。
3. **异步持久化**：使用ScheduledExecutorService异步执行日志保存，避免影响主业务流程性能。
4. **异常处理**：通过@AfterThrowing注解捕获异常情况下的操作日志。

## 日志配置管理

### application.yml监控配置

系统通过application.yml文件配置监控相关参数，主要配置项包括：

```yaml
# 系统业务参数
isys:
  # 是否允许跨域请求
  allow-cors: true
  # 是否内存缓存配置信息
  cache-config: false
  mq:
    vender: activeMQ  # MQ厂商配置
```

**Section sources**
- [application.yml](file://conf/devCommons/config/application.yml#L1-L126)

### 多环境配置管理

系统采用分层配置策略，不同环境的配置文件优先级如下：

1. 当前目录下的config文件夹
2. 当前目录
3. classpath下的config目录
4. classpath根目录

各子系统（sys-manager、sys-merchant、sys-agent）可拥有独立的application.yml配置文件，实现差异化配置。

```mermaid
graph TD
A[配置文件优先级] --> B[./config/]
A --> C[./]
A --> D[classpath:/config/]
A --> E[classpath:/]
```

**Diagram sources**
- [application.yml](file://sys-manager/src/main/resources/application.yml#L1-L44)

**Section sources**
- [application.yml](file://sys-manager/src/main/resources/application.yml#L1-L44)
- [application.yml](file://sys-merchant/src/main/resources/application.yml#L1-L41)
- [application.yml](file://sys-agent/src/main/resources/application.yml#L1-L34)

## 监控最佳实践

