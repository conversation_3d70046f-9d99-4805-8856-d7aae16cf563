# 系统配置管理

<cite>
**本文档引用文件**  
- [SysConfigController.java](file://sys-manager/src/main/java/com/unipay/mgr/ctrl/config/SysConfigController.java)
- [SysConfig.java](file://core/src/main/java/com/unipay/core/entity/SysConfig.java)
- [ResetAppConfigMQ.java](file://components/components-mq/src/main/java/com/unipay/components/mq/model/ResetAppConfigMQ.java)
- [IMQSender.java](file://components/components-mq/src/main/java/com/unipay/components/mq/vender/IMQSender.java)
- [ActiveMQSender.java](file://components/components-mq/src/main/java/com/unipay/components/mq/vender/activemq/ActiveMQSender.java)
- [SysConfigService.java](file://service/src/main/java/com/unipay/service/impl/SysConfigService.java)
- [ResetAppConfigMQReceiver.java](file://sys-manager/src/main/java/com/unipay/mgr/mq/ResetAppConfigMQReceiver.java)
- [DBApplicationConfig.java](file://core/src/main/java/com/unipay/core/model/DBApplicationConfig.java)
- [系统配置说明.md](file://z-docs/md_records/系统配置说明.md)
</cite>

## 目录
1. [简介](#简介)
2. [核心组件](#核心组件)
3. [系统架构](#系统架构)
4. [详细组件分析](#详细组件分析)
5. [依赖分析](#依赖分析)
6. [性能考虑](#性能考虑)
7. [故障排除指南](#故障排除指南)
8. [结论](#结论)

## 简介
本系统配置管理文档详细阐述了UniPay平台的系统级参数管理机制。系统通过`SysConfigController`提供配置项的增删改查API，所有配置数据存储在`SysConfig`实体类对应的`t_sys_config`数据库表中。当管理员修改配置后，系统通过`ResetAppConfigMQ`消息通知所有微服务实例进行配置刷新，确保配置变更的实时性和一致性。该机制涉及消息发送接口`IMQSender`及其具体实现类（如`ActiveMQSender`），实现了跨服务的配置同步。

## 核心组件

`SysConfigController`是系统配置管理的核心控制器，提供了查询和修改系统配置的REST API。`SysConfig`实体类定义了系统配置的数据结构和存储方式。`ResetAppConfigMQ`作为消息模型，承载配置更新通知。`IMQSender`接口及其实现类负责将配置变更消息发布到消息队列。各微服务通过`ResetAppConfigMQReceiver`监听并处理配置更新消息。

**Section sources**
- [SysConfigController.java](file://sys-manager/src/main/java/com/unipay/mgr/ctrl/config/SysConfigController.java#L38-L109)
- [SysConfig.java](file://core/src/main/java/com/unipay/core/entity/SysConfig.java#L24-L94)
- [ResetAppConfigMQ.java](file://components/components-mq/src/main/java/com/unipay/components/mq/model/ResetAppConfigMQ.java#L16-L67)
- [IMQSender.java](file://components/components-mq/src/main/java/com/unipay/components/mq/vender/IMQSender.java#L9-L17)

## 系统架构

```mermaid
graph TB
subgraph "管理后台"
SysConfigController["SysConfigController<br/>配置管理API"]
end
subgraph "数据存储"
SysConfig["SysConfig<br/>实体类"]
Database[(t_sys_config<br/>数据库表)]
end
subgraph "消息系统"
IMQSender["IMQSender<br/>消息发送接口"]
ActiveMQSender["ActiveMQSender<br/>具体实现"]
MQ[(消息队列)]
end
subgraph "微服务集群"
Receiver1["ResetAppConfigMQReceiver<br/>服务实例1"]
Receiver2["ResetAppConfigMQReceiver<br/>服务实例2"]
ReceiverN["ResetAppConfigMQReceiver<br/>服务实例N"]
end
SysConfigController --> SysConfig
SysConfig --> Database
SysConfigController --> IMQSender
IMQSender --> ActiveMQSender
ActiveMQSender --> MQ
MQ --> Receiver1
MQ --> Receiver2
MQ --> ReceiverN
Receiver1 --> SysConfig
Receiver2 --> SysConfig
ReceiverN --> SysConfig
style SysConfigController fill:#e1f5fe,stroke:#039be5
style SysConfig fill:#e8f5e8,stroke:#43a047
style Database fill:#f3e5f5,stroke:#8e24aa
style IMQSender fill:#fff3e0,stroke:#fb8c00
style ActiveMQSender fill:#ffecb3,stroke:#f57c00
style MQ fill:#e0f7fa,stroke:#00acc1
style Receiver1 fill:#f1f8e9,stroke:#689f38
style Receiver2 fill:#f1f8e9,stroke:#689f38
style ReceiverN fill:#f1f8e9,stroke:#689f38
```

**Diagram sources**
- [SysConfigController.java](file://sys-manager/src/main/java/com/unipay/mgr/ctrl/config/SysConfigController.java#L38-L109)
- [SysConfig.java](file://core/src/main/java/com/unipay/core/entity/SysConfig.java#L24-L94)
- [ResetAppConfigMQ.java](file://components/components-mq/src/main/java/com/unipay/components/mq/model/ResetAppConfigMQ.java#L16-L67)
- [IMQSender.java](file://components/components-mq/src/main/java/com/unipay/components/mq/vender/IMQSender.java#L9-L17)
- [ActiveMQSender.java](file://components/components-mq/src/main/java/com/unipay/components/mq/vender/activemq/ActiveMQSender.java#L18-L44)

## 详细组件分析

### 配置管理控制器分析

`SysConfigController`提供了两个核心API：`getConfigs`用于查询指定分组下的所有配置项，`update`用于修改配置。当配置被修改时，系统会异步发送`ResetAppConfigMQ`消息到消息队列。

```mermaid
sequenceDiagram
participant Admin as "管理员"
participant Controller as "SysConfigController"
participant Service as "SysConfigService"
participant MQSender as "IMQSender"
participant MQ as "消息队列"
Admin->>Controller : PUT /api/sysConfigs/{groupKey}
Controller->>Controller : getReqParamJSON()
Controller->>Service : updateByConfigKey(updateMap)
Service-->>Controller : 返回更新数量
Controller->>Controller : 判断更新结果
Controller->>Controller : updateSysConfigMQ(groupKey)
Controller->>MQSender : send(ResetAppConfigMQ.build(groupKey))
MQSender->>MQ : 发送广播消息
MQ-->>Controller : 消息发送成功
Controller-->>Admin : 返回ApiRes.ok()
```

**Diagram sources**
- [SysConfigController.java](file://sys-manager/src/main/java/com/unipay/mgr/ctrl/config/SysConfigController.java#L76-L101)
- [SysConfigService.java](file://service/src/main/java/com/unipay/service/impl/SysConfigService.java#L78-L91)

### 配置实体类分析

`SysConfig`实体类定义了系统配置的各项属性，包括配置键、配置值、分组键等。该类通过MyBatis Plus注解映射到`t_sys_config`数据库表，实现了配置数据的持久化存储。

```mermaid
classDiagram
class SysConfig {
+String configKey
+String configName
+String configDesc
+String groupKey
+String groupName
+String configVal
+String type
+Long sortNum
+Date updatedAt
+static LambdaQueryWrapper gw()
}
class BaseModel {
+String createdBy
+Date createdAt
+String updatedBy
+Date updatedAt
}
SysConfig --|> BaseModel : 继承
```

**Diagram sources**
- [SysConfig.java](file://core/src/main/java/com/unipay/core/entity/SysConfig.java#L24-L94)

### 配置更新消息分析

`ResetAppConfigMQ`类定义了配置更新消息的结构和行为。该消息采用广播模式发送，确保所有微服务实例都能接收到配置变更通知。

```mermaid
classDiagram
class ResetAppConfigMQ {
+String MQ_NAME
+MsgPayload payload
+getMQName() String
+getMQType() MQSendTypeEnum
+toMessage() String
+build(groupKey) ResetAppConfigMQ
+parse(msg) MsgPayload
}
class MsgPayload {
+String groupKey
}
class AbstractMQ {
<<abstract>>
+getMQName() String
+getMQType() MQSendTypeEnum
+toMessage() String
}
ResetAppConfigMQ --|> AbstractMQ : 继承
ResetAppConfigMQ *-- MsgPayload : 包含
ResetAppConfigMQ : IMQReceiver
```

**Diagram sources**
- [ResetAppConfigMQ.java](file://components/components-mq/src/main/java/com/unipay/components/mq/model/ResetAppConfigMQ.java#L16-L67)

### 消息发送机制分析

`IMQSender`接口定义了消息发送的契约，`ActiveMQSender`是其具体实现。该机制通过JMS模板将配置更新消息发送到ActiveMQ消息队列。

```mermaid
flowchart TD
Start([开始]) --> CheckCondition["@ConditionalOnProperty<br/>检查MQ配置"]
CheckCondition --> |条件满足| CreateSender["创建ActiveMQSender实例"]
CreateSender --> InjectConfig["注入ActiveMQConfig"]
InjectConfig --> InjectTemplate["注入JmsTemplate"]
InjectTemplate --> ImplementSend["实现send()方法"]
ImplementSend --> SendNormal["send(AbstractMQ mqModel)"]
ImplementSend --> SendDelay["send(AbstractMQ mqModel, int delay)"]
SendNormal --> UseTemplate["jmsTemplate.convertAndSend()"]
SendDelay --> UseTemplateDelay["jmsTemplate.send() with delay properties"]
UseTemplate --> End([消息发送完成])
UseTemplateDelay --> End
```

**Diagram sources**
- [IMQSender.java](file://components/components-mq/src/main/java/com/unipay/components/mq/vender/IMQSender.java#L9-L17)
- [ActiveMQSender.java](file://components/components-mq/src/main/java/com/unipay/components/mq/vender/activemq/ActiveMQSender.java#L18-L44)

### 配置服务分析

`SysConfigService`提供了配置数据的业务逻辑处理，包括从数据库查询配置、更新配置以及初始化配置缓存。

```mermaid
sequenceDiagram
participant Controller as "SysConfigController"
participant Service as "SysConfigService"
participant DB as "数据库"
Controller->>Service : updateByConfigKey(updateMap)
Service->>Service : 遍历updateMap
loop 每个配置项
Service->>Service : 创建SysConfig对象
Service->>Service : 设置configKey和configVal
Service->>Service : saveOrUpdate()
Service->>DB : 执行数据库操作
DB-->>Service : 返回操作结果
end
Service-->>Controller : 返回更新数量
```

**Diagram sources**
- [SysConfigService.java](file://service/src/main/java/com/unipay/service/impl/SysConfigService.java#L78-L91)

## 依赖分析

```mermaid
graph LR
SysConfigController --> SysConfigService
SysConfigController --> IMQSender
SysConfigService --> SysConfigMapper
SysConfigService --> SysConfig
IMQSender --> ActiveMQSender
ActiveMQSender --> ActiveMQConfig
ActiveMQSender --> JmsTemplate
ResetAppConfigMQReceiver --> SysConfigService
SysConfig --> BaseModel
ResetAppConfigMQ --> AbstractMQ
ActiveMQSender -.-> MQVenderCS
style SysConfigController fill:#e1f5fe,stroke:#039be5
style SysConfigService fill:#e8f5e8,stroke:#43a047
style SysConfigMapper fill:#f3e5f5,stroke:#8e24aa
style SysConfig fill:#e8f5e8,stroke:#43a047
style IMQSender fill:#fff3e0,stroke:#fb8c00
style ActiveMQSender fill:#ffecb3,stroke:#f57c00
style ActiveMQConfig fill:#fff3e0,stroke:#fb8c00
style JmsTemplate fill:#fff3e0,stroke:#fb8c00
style ResetAppConfigMQReceiver fill:#f1f8e9,stroke:#689f38
style BaseModel fill:#e8f5e8,stroke:#43a047
style AbstractMQ fill:#e8f5e8,stroke:#43a047
style MQVenderCS fill:#f3e5f5,stroke:#8e24aa
```

**Diagram sources**
- [SysConfigController.java](file://sys-manager/src/main/java/com/unipay/mgr/ctrl/config/SysConfigController.java#L44-L44)
- [SysConfigService.java](file://service/src/main/java/com/unipay/service/impl/SysConfigService.java#L33-L34)
- [ActiveMQSender.java](file://components/components-mq/src/main/java/com/unipay/components/mq/vender/activemq/ActiveMQSender.java#L22-L23)

## 性能考虑

系统配置管理机制在设计时考虑了性能优化。通过`IS_USE_CACHE`标志位控制是否启用缓存，当启用缓存时，系统会将配置数据存储在静态变量中，避免频繁的数据库查询。配置更新采用异步消息通知机制，避免了同步调用带来的性能瓶颈。消息发送使用ActiveMQ的广播模式，确保消息能够高效地传递给所有微服务实例。

## 故障排除指南

当配置更新未能生效时，应检查以下方面：首先确认`SysConfigController`的更新API是否成功返回；其次检查消息队列服务是否正常运行；然后验证`ResetAppConfigMQReceiver`是否成功接收到消息；最后确认`SysConfigService`的`initDBConfig`方法是否被正确调用。日志中应包含"成功接收更新系统配置的订阅通知"和"系统配置静态属性已重置"等关键信息。

**Section sources**
- [ResetAppConfigMQReceiver.java](file://sys-manager/src/main/java/com/unipay/mgr/mq/ResetAppConfigMQReceiver.java#L22-L28)
- [SysConfigService.java](file://service/src/main/java/com/unipay/service/impl/SysConfigService.java#L39-L49)

## 结论

UniPay系统的配置管理机制通过REST API、数据库持久化、消息队列和缓存技术的有机结合，实现了高效、可靠的系统级参数管理。该机制不仅提供了友好的配置管理界面，还通过消息广播确保了配置变更的实时同步，为系统的稳定运行提供了有力保障。