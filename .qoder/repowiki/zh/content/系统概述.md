
# 系统概述

<cite>
**本文档引用的文件**   
- [README.md](file://README.md)
- [项目总览.md](file://unipay-portal/项目总览.md)
- [PayOrder.java](file://core/src/main/java/com/unipay/core/entity/PayOrder.java)
- [MchInfo.java](file://core/src/main/java/com/unipay/core/entity/MchInfo.java)
- [SysUser.java](file://core/src/main/java/com/unipay/core/entity/SysUser.java)
- [AgentInfo.java](file://core/src/main/java/com/unipay/core/entity/AgentInfo.java)
- [PayInterfaceConfig.java](file://core/src/main/java/com/unipay/core/entity/PayInterfaceConfig.java)
- [PayOrderService.java](file://service/src/main/java/com/unipay/service/impl/PayOrderService.java)
- [MchInfoService.java](file://service/src/main/java/com/unipay/service/impl/MchInfoService.java)
- [SysUserService.java](file://service/src/main/java/com/unipay/service/impl/SysUserService.java)
- [PayInterfaceConfigService.java](file://service/src/main/java/com/unipay/service/impl/PayInterfaceConfigService.java)
- [AgentInfoService.java](file://service/src/main/java/com/unipay/service/impl/AgentInfoService.java)
- [PayOrderMchNotifyMQ.java](file://components/components-mq/src/main/java/com/unipay/components/mq/model/PayOrderMchNotifyMQ.java)
- [ResetAppConfigMQ.java](file://components/components-mq/src/main/java/com/unipay/components/mq/model/ResetAppConfigMQ.java)
- [AgentApplication.java](file://sys-agent/src/main/java/com/unipay/agent/bootstrap/AgentApplication.java)
- [ManagerApplication.java](file://sys-manager/src/main/java/com/unipay/mgr/bootstrap/ManagerApplication.java)
- [MerchantApplication.java](file://sys-merchant/src/main/java/com/unipay/mch/bootstrap/MerchantApplication.java)
- [PaymentApplication.java](file://sys-payment/src/main/java/com/unipay/pay/bootstrap/PaymentApplication.java)
- [application.yml](file://sys-agent/src/main/resources/application.yml)
</cite>

## 目录
1. [引言](#引言)
2. [系统架构概览](#系统架构概览)
3. [核心子系统职责](#核心子系统职责)
4. [数据模型与核心实体](#数据模型与核心实体)
5. [异步消息通信机制](#异步消息通信机制)
6. [系统配置与热更新](#系统配置与热更新)
7. [总结](#总结)

## 引言

uni-pay统一支付平台是一个功能完备的全栈微服务架构系统，旨在为商户、代理商和运营方提供一个高效、稳定、可扩展的支付解决方案。该平台的核心价值在于其强大的集成能力，能够支持多种支付渠道（如微信、支付宝、云闪付等），并实现对多角色（商户、代理商、运营）的精细化管理。通过采用异步消息通信机制，系统实现了各模块间的松耦合，确保了高并发场景下的稳定性和可靠性。本系统概述将为初学者提供一个高层次的概念性理解，同时为经验丰富的开发者深入探讨其技术细节，如Spring Boot微服务间的通信、Vue.js前端与后端API的集成，以及通过消息队列实现配置热更新等关键流程。

## 系统架构概览

uni-pay平台采用现代化的微服务架构，将复杂的支付业务分解为多个独立、可独立部署和扩展的服务。整个系统由前端UI、后端微服务、公共组件和数据库四大核心部分构成，它们之间通过清晰定义的API和消息队列进行交互。

```mermaid
graph TD
subgraph "前端UI"
A[运营平台 UI] --> |HTTP/HTTPS| B[API网关]
C[商户平台 UI] --> |HTTP/HTTPS| B
D[代理商平台 UI] --> |HTTP/HTTPS| B
E[收银台] --> |HTTP/HTTPS| B
end
subgraph "后端微服务"
B[API网关] --> F[运营平台 (sys-manager)]
B --> G[商户平台 (sys-merchant)]
B --> H[代理商平台 (sys-agent)]
B --> I[支付网关 (sys-payment)]
end
subgraph "公共组件"
J[消息队列 (ActiveMQ/RabbitMQ)] --> F
J --> G
J --> H
J --> I
K[Redis] --> F
K --> G
K --> H
K --> I
L[OSS对象存储] --> F
L --> G
L --> H
L --> I
end
subgraph "数据库"
M[(MySQL)]
M < --> F
M < --> G
M < --> H
M < --> I
end
style A fill:#f9f,stroke:#333
style C fill:#bbf,stroke:#333
style D fill:#fbb,stroke:#333
style E fill:#bfb,stroke:#333
style F fill:#f96,stroke:#333
style G fill:#6f9,stroke:#333
style H fill:#96f,stroke:#333
style I fill:#66f,stroke:#333
style J fill:#ff6,stroke:#333
style K fill:#6ff,stroke:#333
style L fill:#f66,stroke:#333
style M fill:#ccc,stroke:#333
```

**图 1：uni-pay系统上下文图**

如图1所示，系统的数据流和组件边界清晰。前端UI通过API网关与后端微服务进行通信。四个核心微服务（运营、商户、代理、支付）各自负责特定的业务领域，并通过公共组件（消息队列、Redis、OSS）实现数据共享和事件驱动。所有服务最终都与同一个MySQL数据库集群交互，保证了数据的一致性。这种架构设计使得系统具备了高内聚、低耦合的特性，任何一个服务的升级或故障都不会直接影响到其他服务。

**系统架构核心特点**:
- **全栈微服务**: 前后端分离，后端由多个Spring Boot应用构成。
- **多角色支持**: 通过`sys-manager`、`sys-merchant`、`sys-agent`三个独立服务分别管理运营、商户和代理商。
- **核心支付引擎**: `sys-payment`作为支付网关，处理所有与第三方支付渠道的交互。
- **异步解耦**: 消息队列是各服务间通信的桥梁，确保了系统的稳定性和可扩展性。

**Diagram sources**
- [README.md](file://README.md)
- [项目总览.md](file://unipay-portal/项目总览.md)

## 核心子系统职责

uni-pay平台由四个核心微服务构成，每个服务都有明确的职责边界，共同支撑起整个支付生态。

### 运营平台 (sys-manager)
运营平台是整个系统的“大脑”和管理中心，主要面向平台运营人员。其核心职责包括：
*   **全局配置管理**: 统一管理系统的全局参数、支付接口定义（`PayInterfaceDefine`）以及服务商（IsvInfo）信息。
*   **用户与权限管理**: 管理所有系统用户（`SysUser`），分配角色（`SysRole`），并控制其访问权限（`SysEntitlement`）。
*   **商户与代理商管理**: 提供对商户（`MchInfo`）和代理商（`AgentInfo`）的创建、修改、查询和删除功能。
*   **数据监控与审计**: 提供系统日志（`SysLog`）查看、数据统计和业务监控功能。

**Section sources**
- [ManagerApplication.java](file://sys-manager/src/main/java/com/unipay/mgr/bootstrap/ManagerApplication.java)

### 商户平台 (sys-merchant)
商户平台是商户进行日常业务操作的中心，主要面向商户管理员。其核心职责包括：
*   **商户应用管理**: 创建和管理商户的应用（`MchApp`），每个应用都有独立的AppId和AppSecret。
*   **支付通道配置**: 配置商户应用所支持的支付方式（`MchPayPassage`），并设置具体的支付接口参数（`PayInterfaceConfig`）。
*   **订单与交易管理**: 查询和管理商户自身的支付订单（`PayOrder`）、退款订单（`RefundOrder`）和转账订单（`TransferOrder`）。
*   **分账管理**: 配置分账规则，管理分账接收方（`MchDivisionReceiver`）和分账记录（`PayOrderDivisionRecord`）。

**Section sources**
- [MerchantApplication.java](file://sys-merchant/src/main/java/com/unipay/mch/bootstrap/MerchantApplication.java)

### 代理商平台 (sys-agent)
代理商平台是为代理商设计的管理工具，主要面向代理商管理员。其核心职责包括：
*   **多级代理管理**: 支持建立多级代理商体系，管理下级代理商（`AgentInfo`）。
*   **商户发展与管理**: 代理商可以发展和管理其下级商户（`MchInfo`）。
*   **分润结算**: 管理代理商的分润比例（`AgentInfo.profitRate`），并生成和查看分润记录（`AgentProfitRecord`）。
*   **资金结算**: 处理与代理商相关的转账和结算业务。

**Section sources**
- [AgentApplication.java](file://sys-agent/src/main/java/com/unipay/agent/bootstrap/AgentApplication.java)

### 支付网关 (sys-payment)
支付网关是整个平台的核心执行引擎，直接与微信、支付宝等第三方支付机构进行交互。其核心职责包括：
*   **统一下单**: 接收来自商户的支付请求，根据支付方式（wayCode）路由到具体的支付服务（如`AlipayPaymentService`、`WxpayPaymentService`）。
*   **支付处理**: 调用第三方支付API完成支付，生成支付链接或二维码。
*   **异步通知处理**: 接收第三方支付平台的异步回调，更新订单状态，并通过消息队列通知其他服务。
*   **退款与转账**: 处理商户发起的退款和转账请求。
*   **分账处理**: 执行自动分账任务，将资金分配给指定的接收方。

**Section sources**
- [PaymentApplication.java](file://sys-payment/src/main/java/com/unipay/pay/bootstrap/PaymentApplication.java)

## 数据模型与核心实体

系统的数据模型是其业务逻辑的基石。以下是几个最核心的实体类。

### 支付订单 (PayOrder)
`PayOrder`实体是整个支付流程的核心，记录了一次支付请求的所有信息。

```java
// [PayOrder.java](file://core/src/main/java/com/unipay/core/entity/PayOrder.java#L24-L278)
@Schema(description = "支付订单表")
public class PayOrder extends BaseModel implements Serializable {
    public static final byte STATE_INIT = 0; //订单生成
    public static final byte STATE_ING = 1; //支付中
    public static final byte STATE_SUCCESS = 2; //支付成功
    public static final byte STATE_FAIL = 3; //支付失败

    @TableId
    private String payOrderId; // 支付订单号
    private String mchNo; // 商户号
    private String appId; // 应用ID
    private String wayCode; // 支付方式代码
    private Long amount; // 支付金额,单位分
    private Byte state; // 支付状态
    private String notifyUrl; // 异步通知地址
    // ... 其他字段
}
```

**关键字段说明**:
- `payOrderId`: 平台生成的唯一支付订单号。
- `mchNo`: 发起支付的商户号。
- `wayCode`: 支付方式代码，如`WX_JSAPI`、`ALI_BAR`等。
- `amount`: 支付金额，以分为单位。
- `state`: 订单状态，是业务流程流转的关键。

**Section sources**
- [PayOrder.java](file://core/src/main/java/com/unipay/core/entity/PayOrder.java#L24-L278)

### 商户信息 (MchInfo)
`MchInfo`实体存储了商户的基本信息和状态。

```java
// [MchInfo.java](file://core/src/main/java/com/unipay/core/entity/MchInfo.java#L24-L140)
@Schema(description = "商户信息表")
public class MchInfo extends BaseModel implements Serializable {
    public static final byte TYPE_NORMAL = 1; //普通商户
    public static final byte TYPE_ISVSUB = 2; //特约商户

    @TableId(value = "mch_no", type = IdType.INPUT)
    private String mchNo; // 商户号
    private String mchName; // 商户名称
    private Byte type; // 商户类型
    private String isvNo; // 服务商号
    private String agentNo; // 所属代理商号
    private Byte state; // 商户状态: 0-停用, 1-正常
    // ... 其他字段
}
```

**关键字段说明**:
- `mchNo`: 商户的唯一标识。
- `type`: 区分普通商户和特约商户（服务商模式）。
- `agentNo`: 关联的代理商号，用于分润结算。

**Section sources**
- [MchInfo.java](file://core/src/main/java/com/unipay/core/entity/MchInfo.java#L24-L140)

### 系统用户 (SysUser)
`SysUser`实体代表了所有可以登录系统的用户。

```java
// [SysUser.java](file://core/src/main/java/com/unipay/core/entity/SysUser.java#L23-L117)
@Schema(description = "系统用户表")
public class SysUser extends BaseModel {
    @TableId(value = "sys_user_id", type = IdType.AUTO)
    private Long sysUserId; // 系统用户ID
    private String loginUsername; // 登录用户名
    private String realname; // 真实姓名
    private String telphone; // 手机号
    private Byte isAdmin; // 是否超管 0-否 1-是
    private String sysType; // 所属系统： MGR-运营平台, MCH-商户中心
    private String belongInfoId; // 所属商户ID / 0(平台)
    // ... 其他字段
}
```

**关键字段说明**:
- `sysType`: 标识用户属于哪个系统（MGR, MCH, AGENT）。
- `belongInfoId`: 标识用户所属的商户或代理商。

**Section sources**
- [SysUser.java](file://core/src/main/java/com/unipay/core/entity/SysUser.java#L23-L117)

### 代理商信息 (AgentInfo)
`AgentInfo`实体用于管理多级代理商体系。

```java
// [AgentInfo.java](file://core/src/main/java/com/unipay/core/entity/AgentInfo.java#L25-L201)
@Schema(description = "代理商信息表")
public class AgentInfo extends BaseModel implements Serializable {
    public static final byte STATE_NORMAL = 1; //正常
    public static final byte TYPE_LEVEL_1 = 1; //一级代理商

    @TableId
    private String agentNo; // 代理商号
    private String agentName; // 代理商名称
    private Byte agentType; // 代理商类型
    private String parentAgentNo; // 上级代理商号
    private Byte agentLevel; // 代理商层级
    private String agentPath; // 代理商层级路径
    private BigDecimal profitRate; // 代理商分润比例
    private Byte state; // 代理商状态
    // ... 其他字段
}
```

**关键字段说明**:
- `agentPath`: 通过路径（如`/A001/A002`）可以快速查询所有下级代理商，是实现多级代理的关键。

**Section sources**
- [AgentInfo.java](file://core/src/main/java/com/unipay/core/entity/AgentInfo.java#L25-L201)

## 异步消息通信机制

异步消息通信是uni-pay平台实现高可用和解耦的核心。系统通过消息队列（MQ）在不同服务间传递事件，避免了服务间的直接依赖。

### 支付订单通知 (PayOrderMchNotifyMQ)
当一笔支付订单状态发生变更（如支付成功）时，支付网关会发布一条`PayOrderMchNotifyMQ`消息。该消息采用点对点（QUEUE）模式，确保只有目标商户系统能接收到。

```java
// [PayOrderMchNotifyMQ.java](file://components/components-mq/src/main/java/com/unipay/components/mq/model/PayOrderMchNotifyMQ.java#L14-L67)
@Data
public class PayOrderMchNotifyMQ extends AbstractMQ {
    public static final String MQ_NAME = "QUEUE_PAY_ORDER_MCH_NOTIFY";

    @Override
    public MQSendTypeEnum getMQType(){
        return MQSendTypeEnum.QUEUE; // 点对点
    }

    public static class MsgPayload {
        private Long notifyId; // 通知单号
    }

    public interface IMQReceiver{
        void receive(MsgPayload payload);
    }
}
```

**消息流程**:
1.  支付成功后，`sys-payment`服务发送`PayOrderMchNotifyMQ`消息。
2.  `sys-merchant`服务中的`PayOrderMchNotifyMQReceiver`监听到消息。
3.  `sys-merchant`服务执行业务逻辑，如更新本地订单状态、发送通知等。

**Section sources**
- [PayOrderMchNotifyMQ.java](file://components/components-mq/src/main/java/com/unipay/components/mq/model/PayOrderMchNotifyMQ.java#L14-L67)

##