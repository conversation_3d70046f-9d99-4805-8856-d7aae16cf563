
# 数据库设计

<cite>
**本文档引用的文件**   
- [PayOrder.java](file://core/src/main/java/com/unipay/core/entity/PayOrder.java)
- [RefundOrder.java](file://core/src/main/java/com/unipay/core/entity/RefundOrder.java)
- [TransferOrder.java](file://core/src/main/java/com/unipay/core/entity/TransferOrder.java)
- [MchInfo.java](file://core/src/main/java/com/unipay/core/entity/MchInfo.java)
- [AgentInfo.java](file://core/src/main/java/com/unipay/core/entity/AgentInfo.java)
- [init.sql](file://z-docs/sql/init.sql)
- [PayOrderService.java](file://service/src/main/java/com/unipay/service/impl/PayOrderService.java)
- [RefundOrderService.java](file://service/src/main/java/com/unipay/service/impl/RefundOrderService.java)
- [TransferOrderService.java](file://service/src/main/java/com/unipay/service/impl/TransferOrderService.java)
- [AgentInfoService.java](file://service/src/main/java/com/unipay/service/impl/AgentInfoService.java)
- [MchInfoService.java](file://service/src/main/java/com/unipay/service/impl/MchInfoService.java)
</cite>

## 目录
1. [引言](#引言)
2. [核心数据模型](#核心数据模型)
3. [实体关系与ER图](#实体关系与er图)
4. [数据库范式与反范式化设计](#数据库范式与反范式化设计)
5. [数据访问模式与查询优化](#数据访问模式与查询优化)
6. [事务管理建议](#事务管理建议)
7. [结论](#结论)

## 引言
本文档旨在全面介绍统一支付系统（uni-pay）的核心数据库设计。系统围绕支付订单、退款订单、转账订单、商户信息和代理商信息等核心实体构建，支持复杂的支付业务场景。文档将详细阐述各数据表的字段定义、数据类型、主外键关系、索引策略，并解释为平衡数据一致性与查询性能而采用的范式与反范式化设计。同时，为数据库管理员（DBA）和开发者提供数据访问模式、查询优化和事务管理的最佳实践建议。

## 核心数据模型

### 支付订单 (PayOrder)
`PayOrder` 表是系统的核心，记录每一笔支付交易的完整信息。

**字段定义与说明**:
| 字段名 | 数据类型 | 是否主键 | 是否可为空 | 默认值 | 约束 | 说明 |
| :--- | :--- | :--- | :--- | :--- | :--- | :--- |
| `payOrderId` | VARCHAR(30) | 是 | 否 | 无 | PRIMARY KEY | 支付订单号，系统生成的唯一标识 |
| `mchNo` | VARCHAR(64) | 否 | 否 | 无 | FOREIGN KEY | 关联的商户号 |
| `isvNo` | VARCHAR(64) | 否 | 是 | NULL | FOREIGN KEY | 关联的服务商号 |
| `appId` | VARCHAR(64) | 否 | 否 | 无 | FOREIGN KEY | 关联的商户应用ID |
| `mchName` | VARCHAR(30) | 否 | 否 | 无 | 无 | 商户名称（冗余字段，用于反范式化） |
| `mchType` | TINYINT(6) | 否 | 否 | 无 | 无 | 商户类型：1-普通商户，2-特约商户 |
| `mchOrderNo` | VARCHAR(64) | 否 | 否 | 无 | UNIQUE KEY (mchNo, mchOrderNo) | 商户系统内的订单号，与商户号组合唯一 |
| `ifCode` | VARCHAR(20) | 否 | 是 | NULL | FOREIGN KEY | 支付接口代码（如 alipay, wxpay） |
| `wayCode` | VARCHAR(20) | 否 | 否 | 无 | 无 | 支付方式代码（如 ALI_JSAPI, WX_NATIVE） |
| `amount` | BIGINT(20) | 否 | 否 | 无 | 无 | 支付金额，单位为分 |
| `mchFeeRate` | DECIMAL(20,6) | 否 | 否 | 无 | 无 | 商户手续费费率快照 |
| `mchFeeAmount` | BIGINT(20) | 否 | 否 | 无 | 无 | 商户手续费，单位为分 |
| `currency` | VARCHAR(3) | 否 | 否 | 'cny' | 无 | 三位货币代码，默认为人民币(cny) |
| `state` | TINYINT(6) | 否 | 否 | 0 | 无 | 支付状态：0-订单生成, 1-支付中, 2-支付成功, 3-支付失败, 4-已撤销, 5-已退款, 6-订单关闭 |
| `notifyState` | TINYINT(6) | 否 | 否 | 0 | 无 | 向下游回调状态：0-未发送, 1-已发送 |
| `clientIp` | VARCHAR(32) | 否 | 是 | NULL | 无 | 客户端IP地址 |
| `subject` | VARCHAR(64) | 否 | 否 | 无 | 无 | 商品标题 |
| `body` | VARCHAR(256) | 否 | 否 | 无 | 无 | 商品描述信息 |
| `channelExtra` | VARCHAR(512) | 否 | 是 | NULL | 无 | 特定渠道发起的额外参数 |
| `channelUser` | VARCHAR(64) | 否 | 是 | NULL | 无 | 渠道用户标识（如微信openId） |
| `channelOrderNo` | VARCHAR(64) | 否 | 是 | NULL | 无 | 渠道订单号 |
| `refundState` | TINYINT(6) | 否 | 否 | 0 | 无 | 退款状态：0-未发生实际退款, 1-部分退款, 2-全额退款 |
| `refundTimes` | INT | 否 | 否 | 0 | 无 | 退款次数 |
| `refundAmount` | BIGINT(20) | 否 | 否 | 0 | 无 | 退款总金额，单位为分 |
| `divisionMode` | TINYINT(6) | 否 | 是 | 0 | 无 | 订单分账模式：0-不允许分账, 1-自动分账, 2-手动分账 |
| `divisionState` | TINYINT(6) | 否 | 是 | 0 | 无 | 订单分账状态：0-未发生, 1-等待任务, 2-处理中, 3-已结束 |
| `divisionLastTime` | DATETIME | 否 | 是 | NULL | 无 | 最新分账时间 |
| `errCode` | VARCHAR(128) | 否 | 是 | NULL | 无 | 渠道支付错误码 |
| `errMsg` | VARCHAR(256) | 否 | 是 | NULL | 无 | 渠道支付错误描述 |
| `extParam` | VARCHAR(128) | 否 | 是 | NULL | 无 | 商户扩展参数 |
| `notifyUrl` | VARCHAR(128) | 否 | 否 | '' | 无 | 异步通知地址 |
| `returnUrl` | VARCHAR(128) | 否 | 是 | '' | 无 | 页面跳转地址 |
| `expiredTime` | DATETIME | 否 | 是 | NULL | 无 | 订单失效时间 |
| `successTime` | DATETIME | 否 | 是 | NULL | 无 | 订单支付成功时间 |
| `createdAt` | TIMESTAMP(3) | 否 | 否 | CURRENT_TIMESTAMP(3) | 无 | 创建时间 |
| `updatedAt` | TIMESTAMP(3) | 否 | 否 | CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) | 无 | 更新时间 |

**索引策略**:
- 主键索引：`PRIMARY KEY (payOrderId)`
- 唯一索引：`UNIQUE KEY Uni_MchNo_MchOrderNo (mchNo, mchOrderNo)`，确保商户订单号在商户维度下的唯一性。
- 普通索引：`INDEX(created_at)`，用于按创建时间范围查询订单，支持分页和统计。

**Section sources**
- [PayOrder.java](file://core/src/main/java/com/unipay/core/entity/PayOrder.java#L24-L278)
- [init.sql](file://z-docs/sql/init.sql#L405-L450)

### 退款订单 (RefundOrder)
`RefundOrder` 表记录每一笔退款交易的详细信息，与 `PayOrder` 表存在一对多的关系。

**字段定义与说明**:
| 字段名 | 数据类型 | 是否主键 | 是否可为空 | 默认值 | 约束 | 说明 |
| :--- | :--- | :--- | :--- | :--- | :--- | :--- |
| `refundOrderId` | VARCHAR(30) | 是 | 否 | 无 | PRIMARY KEY | 退款订单号，系统生成的唯一标识 |
| `payOrderId` | VARCHAR(30) | 否 | 否 | 无 | FOREIGN KEY | 关联的支付订单号 |
| `channelPayOrderNo` | VARCHAR(64) | 否 | 是 | NULL | 无 | 渠道支付单号（来自支付订单） |
| `mchNo` | VARCHAR(64) | 否 | 否 | 无 | FOREIGN KEY | 关联的商户号 |
| `isvNo` | VARCHAR(64) | 否 | 是 | NULL | FOREIGN KEY | 关联的服务商号 |
| `appId` | VARCHAR(64) | 否 | 否 | 无 | FOREIGN KEY | 关联的商户应用ID |
| `mchName` | VARCHAR(30) | 否 | 否 | 无 | 无 | 商户名称（冗余字段，用于反范式化） |
| `mchType` | TINYINT(6) | 否 | 否 | 无 | 无 | 商户类型：1-普通商户，2-特约商户 |
| `mchRefundNo` | VARCHAR(64) | 否 | 否 | 无 | UNIQUE KEY (mchNo, mchRefundNo) | 商户系统内的退款单号，与商户号组合唯一 |
| `wayCode` | VARCHAR(20) | 否 | 否 | 无 | 无 | 支付方式代码 |
| `ifCode` | VARCHAR(20) | 否 | 否 | 无 | FOREIGN KEY | 支付接口代码 |
| `payAmount` | BIGINT(20) | 否 | 否 | 无 | 无 | 原支付金额，单位为分 |
| `refundAmount` | BIGINT(20) | 否 | 否 | 无 | 无 | 本次退款金额，单位为分 |
| `currency` | VARCHAR(3) | 否 | 否 | 'cny' | 无 | 三位货币代码，默认为人民币(cny) |
| `state` | TINYINT(6) | 否 | 否 | 0 | 无 | 退款状态：0-订单生成, 1-退款中, 2-退款成功, 3-退款失败, 4-退款任务关闭 |
| `clientIp` | VARCHAR(32) | 否 | 是 | NULL | 无 | 客户端IP地址 |
| `refundReason` | VARCHAR(256) | 否 | 否 | 无 | 无 | 退款原因 |
| `channelOrderNo` | VARCHAR(32) | 否 | 是 | NULL | 无 | 渠道订单号 |
| `errCode` | VARCHAR(128) | 否 | 是 | NULL | 无 | 渠道错误码 |
| `errMsg` | VARCHAR(2048) | 否 | 是 | NULL | 无 | 渠道错误描述 |
| `channelExtra` | VARCHAR(512) | 否 | 是 | NULL | 无 | 特定渠道发起时的额外参数 |
| `notifyUrl` | VARCHAR(128) | 否 | 是 | NULL | 无 | 通知地址 |
| `extParam` | VARCHAR(64) | 否 | 是 | NULL | 无 | 扩展参数 |
| `successTime` | DATETIME | 否 | 是 | NULL | 无 | 订单退款成功时间 |
| `expiredTime` | DATETIME | 否 | 是 | NULL | 无 | 退款失效时间 |
| `createdAt` | TIMESTAMP(3) | 否 | 否 | CURRENT_TIMESTAMP(3) | 无 | 创建时间 |
| `updatedAt` | TIMESTAMP(3) | 否 | 否 | CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) | 无 | 更新时间 |

**索引策略**:
- 主键索引：`PRIMARY KEY (refundOrderId)`
- 唯一索引：`UNIQUE KEY Uni_MchNo_MchRefundNo (mchNo, mchRefundNo)`，确保商户退款单号在商户维度下的唯一性。
- 外键索引：`payOrderId` 字段上的索引用于高效关联查询支付订单。

**Section sources**
- [RefundOrder.java](file://core/src/main/java/com/unipay/core/entity/RefundOrder.java#L22-L204)
- [init.sql](file://z-docs/sql/init.sql#L552-L595)

### 转账订单 (TransferOrder)
`TransferOrder` 表记录每一笔转账交易的详细信息。

**字段定义与说明**:
| 字段名 | 数据类型 | 是否主键 | 是否可为空 | 默认值 | 约束 | 说明 |
| :--- | :--- | :--- | :--- | :--- | :--- | :--- |
| `transferId` | VARCHAR(32) | 是 | 否 | 无 | PRIMARY KEY | 转账订单号，系统生成的唯一标识 |
| `mchNo` | VARCHAR(64) | 否 | 否 | 无 | FOREIGN KEY | 关联的商户号 |
| `isvNo` | VARCHAR(64) | 否 | 是 | NULL | FOREIGN KEY | 关联的服务商号 |
| `appId` | VARCHAR(64) | 否 | 否 | 无 | FOREIGN KEY | 关联的商户应用ID |
| `mchName` | VARCHAR(30) | 否 | 否 | 无 | 无 | 商户名称（冗余字段，用于反范式化） |
| `mchType` | TINYINT(6) | 否 | 否 | 无 | 无 | 商户类型：1-普通商户，2-特约商户 |
| `mchOrderNo` | VARCHAR(64) | 否 | 否 | 无 | UNIQUE KEY (mchNo, mchOrderNo) | 商户系统内的订单号，与商户号组合唯一 |
| `ifCode` | VARCHAR(20) | 否 | 否 | 无 | FOREIGN KEY | 支付接口代码 |
| `entryType` | VARCHAR(20) | 否 | 否 | 无 | 无 | 入账方式：WX_CASH-微信零钱, ALIPAY_CASH-支付宝转账, BANK_CARD-银行卡 |
| `amount` | BIGINT(20) | 否 | 否 | 无 | 无 | 转账金额，单位为分 |
| `currency` | VARCHAR(3) | 否 | 否 | 'cny' | 无 | 三位货币代码，默认为人民币(cny) |
| `accountNo` | VARCHAR(64) | 否 | 否 | 无 | 无 | 收款账号 |
| `accountName` | VARCHAR(64) | 否 | 是 | NULL | 无 | 收款人姓名 |
| `bankName` | VARCHAR(32) | 否 | 是 | NULL | 无 | 收款人开户行名称 |
| `transferDesc` | VARCHAR(128) | 否 | 否 | '' | 无 | 转账备注信息 |
| `clientIp` | VARCHAR(32) | 否 | 是 | NULL | 无 | 客户端IP |
| `state` | TINYINT(6) | 否 | 否 | 0 | 无 | 支付状态：0-订单生成, 1-转账中, 2-转账成功, 3-转账失败, 4-订单关闭 |
| `channelExtra` | VARCHAR(512) | 否 | 是 | NULL | 无 | 特定渠道发起额外参数 |
| `channelOrderNo` | VARCHAR(64) | 否 | 是 | NULL | 无 | 渠道订单号 |
| `channelResData` | TEXT | 否 | 是 | NULL | 无 | 渠道响应数据（如微信确认数据包） |
| `errCode` | VARCHAR(128) | 否 | 是 | NULL | 无 | 渠道支付错误码 |
| `errMsg` | VARCHAR(256) | 否 | 是 | NULL | 无 | 渠道支付错误描述 |
| `extParam` | VARCHAR(128) | 否 | 是 | NULL | 无 | 商户扩展参数 |
| `notifyUrl` | VARCHAR(128) | 否 | 否 | '' | 无 | 异步通知地址 |
| `successTime` | DATETIME | 否 | 是 | NULL | 无 | 转账成功时间 |
| `createdAt` | TIMESTAMP(3) | 否 | 否 | CURRENT_TIMESTAMP(3) | 无 | 创建时间 |
| `updatedAt` | TIMESTAMP(3) | 否 | 否 | CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) | 无 | 更新时间 |

**索引策略**:
- 主键索引：`PRIMARY KEY (transferId)`
- 唯一索引：`UNIQUE KEY Uni_MchNo_MchOrderNo (mchNo, mchOrderNo)`，确保商户订单号在商户维度下的唯一性。
- 普通索引：`INDEX(created_at)`，用于按创建时间范围查询订单。

**Section sources**
- [TransferOrder.java](file://core/src/main/java/com/unipay/core/entity/TransferOrder.java#L22-L211)
- [init.sql](file://z-docs/sql/init.sql#L608-L658)

### 商户信息 (MchInfo)
`MchInfo` 表存储商户的基本信息。

**字段定义与说明**:
| 字段名 | 数据类型 | 是否主键 | 是否可为空 | 默认值 | 约束 | 说明 |
| :--- | :--- | :--- | :--- | :--- | :--- | :--- |
| `mchNo` | VARCHAR(64) | 是 | 否 | 无 | PRIMARY KEY | 商户号，商户的唯一标识 |
| `mchName` | VARCHAR(64) | 否 | 否 | 无 | 无 | 商户名称 |
| `mchShortName` | VARCHAR(32) | 否 | 否 | 无 | 无 | 商户简称 |
| `type` | TINYINT(6) | 否 | 否 | 1 | 无 | 类型：1-普通商户, 2-特约商户(服务商模式) |
| `isvNo` | VARCHAR(64) | 否 | 是 | NULL | FOREIGN KEY | 关联的服务商号 |
| `agentNo` | VARCHAR(64) | 否 | 是 | NULL | FOREIGN KEY | 所属代理商号 |
| `contactName` | VARCHAR(32) | 否 | 是 | NULL | 无 | 联系人姓名 |
| `contactTel` | VARCHAR(32) | 否 | 是 | NULL | 无 | 联系人手机号 |
| `contactEmail` | VARCHAR(32) | 否 | 是 | NULL | 无 | 联系人邮箱 |
| `state` | TINYINT(6) | 否 | 否 | 1 | 无 | 商户状态：0-停用, 1-正常 |
| `remark` | VARCHAR(128) | 否 | 是 | NULL | 无 | 商户备注 |
| `initUserId` | BIGINT(20) | 否 | 是 | NULL | FOREIGN KEY | 初始用户ID（创建商户时允许登录的用户） |
| `createdUid` | BIGINT(20) | 否 | 是 | NULL | FOREIGN KEY | 创建者用户ID |
| `createdBy` | VARCHAR(64) | 否 | 是 | NULL | 无 | 创建者姓名 |
| `createdAt` | TIMESTAMP(3) | 否 | 否 | CURRENT_TIMESTAMP(3) | 无 | 创建时间 |
| `updatedAt` | TIMESTAMP(3) | 否 | 否 | CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) | 无 | 更新时间 |

**索引策略**:
- 主键索引：`PRIMARY KEY (mchNo)`
- 普通索引：`INDEX(agent_no)`，用于根据代理商号快速查询其下属商户。

**Section sources**
- [MchInfo.java](file://core/src/main/java/com/unipay/core/entity/MchInfo.java#L24-L140)
- [init.sql](file://z-docs/sql/init.sql#L137-L178)

### 代理商信息 (AgentInfo)
`AgentInfo` 表存储代理商的信息，支持多级代理结构。

**字段定义与说明**:
| 字段名 | 数据类型 | 是否主键 | 是否可为空 | 默认值 | 约束 | 说明 |
| :--- | :--- | :--- | :--- | :--- | :--- | :--- |
| `agentNo` | VARCHAR(64) | 是 | 否 | 无 | PRIMARY KEY | 代理商号，代理商的唯一标识 |
| `agentName` | VARCHAR(64) | 否 | 否 | 无 | 无 | 代理商名称 |
| `agentShortName` | VARCHAR(32) | 否 | 否 | 无 | 无 | 代理商简称 |
| `loginUsername` | VARCHAR(32) | 否 | 是 | NULL | 无 | 登录用户名 |
| `agentType` | TINYINT(6) | 否 | 否 | 1 | 无 | 代理商类型：1-一级代理商, 2-二级代理商, 3-三级代理商等 |
| `parentAgentNo` | VARCHAR(64) | 否 | 是 | NULL | FOREIGN KEY | 上级代理商号 |
| `agentLevel` | TINYINT(6) | 否 | 否 | 1 | 无 | 代理商层级：1-一级, 2-二级, 3-三级等 |
| `agentPath` | VARCHAR(512) | 否 | 是 | NULL | 无 | 代理商层级路径，如: /A001/A002/A003 |
| `contactName` | VARCHAR(32) | 否 | 是 | NULL | 无 | 联系人姓名 |
| `contactTel` | VARCHAR(32) | 否 | 是 | NULL | 无 | 联系人手机号 |
| `contactEmail` | VARCHAR(32) | 否 | 是 | NULL | 无 | 联系人邮箱 |
| `province` | VARCHAR(32) | 否 | 是 | NULL | 无 | 省份 |
| `city` | VARCHAR(32) | 否 | 是 | NULL | 无 | 城市 |
| `district` | VARCHAR(32) | 否 | 是 | NULL | 无 | 区县 |
| `address` | VARCHAR(128) | 否 | 是 | NULL | 无 | 详细地址 |
| `profitRate` | DECIMAL(20,6) | 否 | 是 | 0.000000 | 无 | 代理商分润比例 |
| `canDevelopAgent` | TINYINT(1) | 否 | 否 | 1 | 无 | 是否允许发展下级代理商：0-否, 1-是