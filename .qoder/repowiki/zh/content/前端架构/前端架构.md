# 前端架构

<cite>
**本文档引用的文件**  
- [unipay-ui-agent/package.json](file://unipay-web-ui/unipay-ui-agent/package.json)
- [unipay-ui-cashier/package.json](file://unipay-web-ui/unipay-ui-cashier/package.json)
- [unipay-ui-manager/package.json](file://unipay-web-ui/unipay-ui-manager/package.json)
- [unipay-ui-merchant/package.json](file://unipay-web-ui/unipay-ui-merchant/package.json)
- [unipay-ui-agent/vite.config.ts](file://unipay-web-ui/unipay-ui-agent/vite.config.ts)
- [unipay-ui-agent/src/main.ts](file://unipay-web-ui/unipay-ui-agent/src/main.ts)
- [unipay-ui-agent/src/router/index.js](file://unipay-web-ui/unipay-ui-agent/src/router/index.js)
- [unipay-ui-agent/src/store/modules/user.ts](file://unipay-web-ui/unipay-ui-agent/src/store/modules/user.ts)
- [unipay-ui-agent/src/http/request.js](file://unipay-web-ui/unipay-ui-agent/src/http/request.js)
- [unipay-ui-agent/src/components/JeepayTable/JeepayTable.vue](file://unipay-web-ui/unipay-ui-agent/src/components/JeepayTable/JeepayTable.vue)
- [unipay-ui-agent/src/components/JeepayLayout/JeepayLayout.vue](file://unipay-web-ui/unipay-ui-agent/src/components/JeepayLayout/JeepayLayout.vue)
</cite>

## 目录
1. [简介](#简介)
2. [项目结构](#项目结构)
3. [核心技术栈](#核心技术栈)
4. [多前端应用架构](#多前端应用架构)
5. [组件化开发模式](#组件化开发模式)
6. [路由管理](#路由管理)
7. [状态管理](#状态管理)
8. [API请求封装](#api请求封装)
9. [主题定制与扩展](#主题定制与扩展)
10. [性能优化建议](#性能优化建议)

## 简介
本项目采用现代化前端架构设计，基于Vue.js构建单页应用（SPA），通过Vite提供高效的开发体验。系统包含四个独立的前端应用：代理商、收银台、运营和商户，分别服务于不同的用户角色和业务场景。文档将详细介绍前端架构设计、核心组件、状态管理、路由机制和API封装等关键方面，为开发者提供全面的技术指导。

## 项目结构
项目采用模块化设计，前端代码集中于`unipay-web-ui`目录下，包含四个独立的Vue应用：

```mermaid
graph TB
subgraph "前端应用"
A[unipay-ui-agent<br>代理商前端]
B[unipay-ui-cashier<br>收银台前端]
C[unipay-ui-manager<br>运营前端]
D[unipay-ui-merchant<br>商户前端]
end
subgraph "共享资源"
E[components<br>可复用UI组件]
F[http<br>HTTP请求封装]
G[utils<br>工具函数]
H[config<br>配置文件]
end
A --> E
B --> E
C --> E
D --> E
A --> F
B --> F
C --> F
D --> F
A --> G
B --> G
C --> G
D --> G
A --> H
B --> H
C --> H
D --> H
```

**图示来源**  
- [unipay-web-ui](file://unipay-web-ui)
- [unipay-web-ui/unipay-ui-agent](file://unipay-web-ui/unipay-ui-agent)
- [unipay-web-ui/unipay-ui-cashier](file://unipay-web-ui/unipay-ui-cashier)
- [unipay-web-ui/unipay-ui-manager](file://unipay-web-ui/unipay-ui-manager)
- [unipay-web-ui/unipay-ui-merchant](file://unipay-web-ui/unipay-ui-merchant)

**本节来源**  
- [unipay-web-ui](file://unipay-web-ui)

## 核心技术栈
系统采用现代化前端技术栈，结合Vue 3和Vite构建高性能应用。

### Vue.js框架
- **代理商前端**：使用Vue 3.2.21，基于Composition API设计
- **运营前端**：使用Vue 3.2.21，支持TypeScript
- **商户前端**：使用Vue 3.2.21，完整TypeScript支持
- **收银台前端**：使用Vue 2.6.11，面向移动端优化

### Vite构建工具
除收银台外，所有前端应用均采用Vite作为构建工具，提供以下优势：
- 快速冷启动：基于ES模块的原生支持
- 热模块替换（HMR）：毫秒级热更新
- 按需编译：只编译当前请求的文件
- 内置优化：自动代码分割和懒加载

```mermaid
graph LR
A[源代码] --> B[Vite开发服务器]
B --> C[ES模块解析]
C --> D[按需编译]
D --> E[浏览器]
F[生产构建] --> G[Vite Rollup打包]
G --> H[优化的静态资源]
```

**图示来源**  
- [unipay-ui-agent/vite.config.ts](file://unipay-web-ui/unipay-ui-agent/vite.config.ts)
- [unipay-ui-agent/package.json](file://unipay-web-ui/unipay-ui-agent/package.json)

**本节来源**  
- [unipay-ui-agent/package.json](file://unipay-web-ui/unipay-ui-agent/package.json)
- [unipay-ui-cashier/package.json](file://unipay-web-ui/unipay-ui-cashier/package.json)
- [unipay-ui-manager/package.json](file://unipay-web-ui/unipay-ui-manager/package.json)
- [unipay-ui-merchant/package.json](file://unipay-web-ui/unipay-ui-merchant/package.json)
- [unipay-ui-agent/vite.config.ts](file://unipay-web-ui/unipay-ui-agent/vite.config.ts)

## 多前端应用架构
系统设计了四个独立的前端应用，分别服务于不同的用户角色和业务需求。

### 用户角色与功能差异

| 应用名称 | 用户角色 | 主要功能 | 技术特点 |
|--------|--------|--------|--------|
| 代理商前端 | 代理商 | 代理商管理、商户管理、利润分润、订单管理 | Vue 3 + TypeScript + Vite |
| 收银台前端 | 终端用户 | 支付收银、扫码支付、支付方式选择 | Vue 2 + 移动端适配 |
| 运营前端 | 平台运营 | 系统配置、权限管理、日志监控、ISV管理 | Vue 3 + TypeScript + Vite |
| 商户前端 | 商户 | 商户配置、支付测试、分账管理、转账管理 | Vue 3 + TypeScript + Vite |

### 应用架构对比

```mermaid
graph TD
subgraph "代理商前端"
A1[Vue 3]
A2[TypeScript]
A3[Vite]
A4[Pinia]
A5[Ant Design Vue]
end
subgraph "收银台前端"
B1[Vue 2]
B2[JavaScript]
B3[Vue CLI]
B4[移动端适配]
B5[Flex布局]
end
subgraph "运营前端"
C1[Vue 3]
C2[TypeScript]
C3[Vite]
C4[Pinia]
C5[Ant Design Vue]
end
subgraph "商户前端"
D1[Vue 3]
D2[TypeScript]
D3[Vite]
D4[Pinia]
D5[Ant Design Vue]
end
A1 --> A2
A2 --> A3
A3 --> A4
A4 --> A5
B1 --> B2
B2 --> B3
B3 --> B4
B4 --> B5
C1 --> C2
C2 --> C3
C3 --> C4
C4 --> C5
D1 --> D2
D2 --> D3
D3 --> D4
D4 --> D5
```

**图示来源**  
- [unipay-ui-agent/package.json](file://unipay-web-ui/unipay-ui-agent/package.json)
- [unipay-ui-cashier/package.json](file://unipay-web-ui/unipay-ui-cashier/package.json)
- [unipay-ui-manager/package.json](file://unipay-web-ui/unipay-ui-manager/package.json)
- [unipay-ui-merchant/package.json](file://unipay-web-ui/unipay-ui-merchant/package.json)

**本节来源**  
- [unipay-web-ui/unipay-ui-agent](file://unipay-web-ui/unipay-ui-agent)
- [unipay-web-ui/unipay-ui-cashier](file://unipay-web-ui/unipay-ui-cashier)
- [unipay-web-ui/unipay-ui-manager](file://unipay-web-ui/unipay-ui-manager)
- [unipay-web-ui/unipay-ui-merchant](file://unipay-web-ui/unipay-ui-merchant)

## 组件化开发模式
项目采用组件化开发模式，设计了一系列可复用的UI组件，提高开发效率和代码一致性。

### 核心可复用组件

#### JeepayTable组件
通用表格组件，提供数据展示、分页、排序和筛选功能。

```mermaid
classDiagram
class JeepayTable {
+data : Array
+columns : Array
+pagination : Object
+loading : Boolean
+fetchData()
+handlePageChange()
+handleSizeChange()
+renderCell()
}
class JeepayTableColumns {
+title : String
+dataIndex : String
+width : Number
+fixed : String
+render()
}
class JeepayTableColState {
+visible : Boolean
+width : Number
+order : Number
}
JeepayTable --> JeepayTableColumns : "包含"
JeepayTable --> JeepayTableColState : "管理"
JeepayTable --> JeepayDrChildren : "支持树形数据"
```

**图示来源**  
- [unipay-ui-agent/src/components/JeepayTable/JeepayTable.vue](file://unipay-web-ui/unipay-ui-agent/src/components/JeepayTable/JeepayTable.vue)
- [unipay-ui-agent/src/components/JeepayTable/JeepayTableColumns.vue](file://unipay-web-ui/unipay-ui-agent/src/components/JeepayTable/JeepayTableColumns.vue)
- [unipay-ui-agent/src/components/JeepayTable/JeepayTableColState.vue](file://unipay-web-ui/unipay-ui-agent/src/components/JeepayTable/JeepayTableColState.vue)

#### JeepayLayout组件
通用布局组件，提供侧边栏导航、顶部导航和内容区域的布局管理。

```mermaid
classDiagram
class JeepayLayout {
+collapsed : Boolean
+menuData : Array
+headerStyle : Object
+sidebarStyle : Object
+toggleCollapsed()
+renderMenu()
+renderHeader()
}
class SubMenu {
+key : String
+title : String
+children : Array
+icon : String
}
class GlobalHeader {
+user : Object
+notifications : Array
+renderUserDropdown()
+renderRightContent()
}
class GlobalFooter {
+copyright : String
+links : Array
}
JeepayLayout --> SubMenu : "包含"
JeepayLayout --> GlobalHeader : "包含"
JeepayLayout --> GlobalFooter : "包含"
```

**图示来源**  
- [unipay-ui-agent/src/components/JeepayLayout/JeepayLayout.vue](file://unipay-web-ui/unipay-ui-agent/src/components/JeepayLayout/JeepayLayout.vue)
- [unipay-ui-agent/src/components/JeepayLayout/SubMenu.vue](file://unipay-web-ui/unipay-ui-agent/src/components/JeepayLayout/SubMenu.vue)
- [unipay-ui-agent/src/components/GlobalHeader/RightContent.vue](file://unipay-web-ui/unipay-ui-agent/src/components/GlobalHeader/RightContent.vue)
- [unipay-ui-agent/src/components/GlobalFooter/index.vue](file://unipay-web-ui/unipay-ui-agent/src/components/GlobalFooter/index.vue)

#### 其他通用组件
- **JeepayCard**：卡片容器组件，用于信息展示
- **JeepayUpload**：文件上传组件，支持多种文件类型
- **JeepayTextUp**：文本上浮标签组件
- **GlobalLoad**：全局加载指示器
- **ChannelUser**：渠道用户选择组件

**本节来源**  
- [unipay-web-ui/unipay-ui-agent/src/components](file://unipay-web-ui/unipay-ui-agent/src/components)
- [unipay-web-ui/unipay-ui-manager/src/components](file://unipay-web-ui/unipay-ui-manager/src/components)
- [unipay-web-ui/unipay-ui-merchant/src/components](file://unipay-web-ui/unipay-ui-merchant/src/components)

## 路由管理
系统采用Vue Router进行路由管理，实现单页应用的页面导航和状态管理。

### 路由配置
路由配置分为静态路由和动态路由两部分：

```mermaid
flowchart TD
A[应用启动] --> B[加载静态路由]
B --> C[用户登录]
C --> D[获取用户权限]
D --> E[生成动态路由]
E --> F[添加到路由表]
F --> G[路由守卫验证]
G --> H[页面渲染]
subgraph "动态路由生成"
E1[权限数据]
E2[菜单配置]
E3[路由映射]
E1 --> E3
E2 --> E3
E3 --> E
end
```

### 路由结构
```mermaid
graph TD
A[根路由] --> B[用户布局]
A --> C[基础布局]
A --> D[空白布局]
B --> E[登录页]
B --> F[注册页]
C --> G[仪表盘]
C --> H[代理商管理]
C --> I[商户管理]
C --> J[订单管理]
C --> K[配置管理]
C --> L[系统管理]
D --> M[异常页]
D --> N[支付页]
```

**图示来源**  
- [unipay-ui-agent/src/router/index.js](file://unipay-web-ui/unipay-ui-agent/src/router/index.js)
- [unipay-ui-agent/src/router/generator-routers.js](file://unipay-web-ui/unipay-ui-agent/src/router/generator-routers.js)
- [unipay-ui-agent/src/layouts](file://unipay-web-ui/unipay-ui-agent/src/layouts)

**本节来源**  
- [unipay-web-ui/unipay-ui-agent/src/router](file://unipay-web-ui/unipay-ui-agent/src/router)
- [unipay-web-ui/unipay-ui-agent/src/layouts](file://unipay-web-ui/unipay-ui-agent/src/layouts)

## 状态管理
系统采用Pinia作为状态管理方案，替代传统的Vuex，提供更简洁的API和更好的TypeScript支持。

### Pinia状态管理架构
```mermaid
classDiagram
class Pinia {
+state : Object
+actions : Object
+getters : Object
+useStore()
+defineStore()
}
class UserStore {
+userInfo : Object
+token : String
+permissions : Array
+login(credentials)
+logout()
+updateUserInfo(data)
+hasPermission(permission)
}
class AppStore {
+sidebarCollapsed : Boolean
+language : String
+theme : String
+toggleSidebar()
+setLanguage()
+setTheme()
}
Pinia <|-- UserStore
Pinia <|-- AppStore
UserStore --> AppStore : "依赖"
```

### 状态管理实现
- **模块化设计**：每个功能模块有独立的store
- **类型安全**：完整TypeScript支持，提供类型推断
- **持久化**：通过插件实现状态持久化存储
- **开发工具**：集成Vue Devtools，支持时间旅行调试

**图示来源**  
- [unipay-ui-agent/src/store/modules/user.ts](file://unipay-web-ui/unipay-ui-agent/src/store/modules/user.ts)
- [unipay-ui-agent/src/main.ts](file://unipay-web-ui/unipay-ui-agent/src/main.ts)

**本节来源**  
- [unipay-web-ui/unipay-ui-agent/src/store](file://unipay-web-ui/unipay-ui-agent/src/store)
- [unipay-ui-agent/src/main.ts](file://unipay-web-ui/unipay-ui-agent/src/main.ts)

## API请求封装
系统对API请求进行了统一封装，提供一致的接口调用方式和错误处理机制。

### 请求封装架构
```mermaid
sequenceDiagram
participant Component as "组件"
participant Service as "API服务"
participant Request as "请求封装"
participant Interceptor as "拦截器"
participant Server as "后端服务"
Component->>Service : 调用API方法
Service->>Request : 发送HTTP请求
Request->>Interceptor : 请求拦截
Interceptor->>Interceptor : 添加认证头
Interceptor->>Interceptor : 显示加载状态
Interceptor->>Server : 发送请求
Server-->>Interceptor : 返回响应
Interceptor-->>Interceptor : 隐藏加载状态
Interceptor-->>Interceptor : 错误处理
Interceptor-->>Request : 返回处理结果
Request-->>Service : 返回数据
Service-->>Component : 返回业务数据
```

### 请求封装特性
- **统一配置**：基础URL、超时时间、请求头等统一配置
- **拦截器**：请求和响应拦截，自动处理认证、加载状态
- **错误处理**：统一错误处理，区分网络错误、业务错误
- **类型安全**：TypeScript接口定义，提供类型检查
- **缓存机制**：支持请求缓存，提高性能

**图示来源**  
- [unipay-ui-agent/src/http/request.js](file://unipay-web-ui/unipay-ui-agent/src/http/request.js)
- [unipay-ui-agent/src/http/HttpRequest.js](file://unipay-web-ui/unipay-ui-agent/src/http/HttpRequest.js)
- [unipay-ui-agent/src/api](file://unipay-web-ui/unipay-ui-agent/src/api)

**本节来源**  
- [unipay-web-ui/unipay-ui-agent/src/http](file://unipay-web-ui/unipay-ui-agent/src/http)
- [unipay-web-ui/unipay-ui-agent/src/api](file://unipay-web-ui/unipay-ui-agent/src/api)

## 主题定制与扩展
系统提供灵活的主题定制和组件扩展机制，满足不同场景的个性化需求。

### 主题定制
通过Vite配置和Less变量实现主题定制：

```typescript
// vite.config.ts 主题配置
css: {
  preprocessorOptions: {
    less: {
      additionalData: `@import "@/less/color.less";`,
      modifyVars: {
        '@primary-color': '#1890ff',
        '@link-color': '#1890ff',
        '@success-color': '#52c41a',
        '@warning-color': '#faad14',
        '@error-color': '#f5222d',
        '@font-size-base': '14px',
        '@border-radius-base': '4px'
      },
      javascriptEnabled: true
    }
  }
}
```

### 组件扩展
提供组件扩展机制，支持自定义组件开发：

```mermaid
flowchart TD
A[基础组件] --> B[扩展组件]
B --> C[业务组件]
subgraph "扩展方式"
D[继承]
E[组合]
F[插槽]
G[事件]
end
D --> B
E --> B
F --> B
G --> B
C --> H[代理商管理]
C --> I[商户配置]
C --> J[支付测试]
```

**图示来源**  
- [unipay-ui-agent/vite.config.ts](file://unipay-web-ui/unipay-ui-agent/vite.config.ts)
- [unipay-ui-agent/src/components](file://unipay-web-ui/unipay-ui-agent/src/components)

**本节来源**  
- [unipay-web-ui/unipay-ui-agent/vite.config.ts](file://unipay-web-ui/unipay-ui-agent/vite.config.ts)
- [unipay-web-ui/unipay-ui-agent/src/components](file://unipay-web-ui/unipay-ui-agent/src/components)

## 性能优化建议
为提升前端应用性能，建议采取以下优化措施：

### 构建优化
- **代码分割**：利用Vite的自动代码分割功能
- **懒加载**：路由和组件的懒加载
- **Tree Shaking**：移除未使用的代码
- **压缩优化**：启用Gzip/Brotli压缩

### 运行时优化
- **虚拟滚动**：大数据量列表使用虚拟滚动
- **防抖节流**：高频事件使用防抖和节流
- **缓存策略**：合理使用浏览器缓存和内存缓存
- **图片优化**：使用WebP格式，懒加载图片

### 监控与分析
- **性能监控**：集成性能监控工具
- **错误追踪**：捕获和上报前端错误
- **用户体验**：监控关键用户体验指标

```mermaid
flowchart LR
A[性能优化] --> B[构建优化]
A --> C[运行时优化]
A --> D[监控分析]
B --> B1[代码分割]
B --> B2[懒加载]
B --> B3[Tree Shaking]
B --> B4[资源压缩]
C --> C1[虚拟滚动]
C --> C2[防抖节流]
C --> C3[缓存策略]
C --> C4[图片优化]
D --> D1[性能监控]
D --> D2[错误追踪]
D --> D3[用户体验]
```

**图示来源**  
- [unipay-ui-agent/vite.config.ts](file://unipay-web-ui/unipay-ui-agent/vite.config.ts)
- [unipay-ui-agent/src/utils](file://unipay-web-ui/unipay-ui-agent/src/utils)

**本节来源**  
- [unipay-web-ui/unipay-ui-agent/vite.config.ts](file://unipay-web-ui/unipay-ui-agent/vite.config.ts)
- [unipay-web-ui/unipay-ui-agent/src/utils](file://unipay-web-ui/unipay-ui-agent/src/utils)