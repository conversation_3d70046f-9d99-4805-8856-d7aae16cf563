
# 运营平台前端

<cite>
**本文档引用的文件**   
- [AlipayPayConfig.vue](file://unipay-web-ui/unipay-ui-manager/src/views/isv/custom/AlipayPayConfig.vue)
- [WxpayPayConfig.vue](file://unipay-web-ui/unipay-ui-manager/src/views/isv/custom/WxpayPayConfig.vue)
- [SysConfig.vue](file://unipay-web-ui/unipay-ui-manager/src/views/sys/config/SysConfig.vue)
- [EntPage.vue](file://unipay-web-ui/unipay-ui-manager/src/views/ent/EntPage.vue)
- [SysLog.vue](file://unipay-web-ui/unipay-ui-manager/src/views/sys/log/SysLog.vue)
- [SysConfig.java](file://core/src/main/java/com/unipay/core/entity/SysConfig.java)
- [SysLog.java](file://core/src/main/java/com/unipay/core/entity/SysLog.java)
- [SysEntitlement.java](file://core/src/main/java/com/unipay/core/entity/SysEntitlement.java)
- [IsvInfo.java](file://core/src/main/java/com/unipay/core/entity/IsvInfo.java)
- [MchInfo.java](file://core/src/main/java/com/unipay/core/entity/MchInfo.java)
- [PayInterfaceConfig.java](file://core/src/main/java/com/unipay/core/entity/PayInterfaceConfig.java)
- [SysConfigService.java](file://service/src/main/java/com/unipay/service/impl/SysConfigService.java)
- [SysLogService.java](file://service/src/main/java/com/unipay/service/impl/SysLogService.java)
- [SysEntitlementService.java](file://service/src/main/java/com/unipay/service/impl/SysEntitlementService.java)
- [ISysConfigService.java](file://core/src/main/java/com/unipay/core/service/ISysConfigService.java)
</cite>

## 目录
1. [介绍](#介绍)
2. [项目结构](#项目结构)
3. [核心组件](#核心组件)
4. [架构概述](#架构概述)
5. [详细组件分析](#详细组件分析)
6. [依赖分析](#依赖分析)
7. [性能考虑](#性能考虑)
8. [故障排除指南](#故障排除指南)
9. [结论](#结论)
10. [附录](#附录)（如有必要）

## 介绍
运营平台前端是为超级管理员设计的操作界面，提供对整个支付系统的高级管理功能。该系统允许管理员管理ISV（独立软件供应商）、配置系统参数、监控系统日志，并管理权限体系。作为最高权限的管理界面，它与代理商前端在UI架构上相似，但提供了更广泛的控制和配置能力。本文档详细介绍了运营平台前端的关键功能模块，包括ISV支付配置、系统参数配置、权限管理和系统日志等，为系统管理员和开发者提供配置管理和安全实践的指导。

## 项目结构
运营平台前端位于`unipay-web-ui/unipay-ui-manager`目录下，采用Vue 3和TypeScript构建，遵循模块化设计原则。前端应用通过Ant Design Vue组件库实现用户界面，与后端通过RESTful API进行通信。主要功能模块按目录组织，包括ISV管理、商户管理、订单管理、支付配置、权限管理（ent）、系统配置（sys/config）和系统日志（sys/log）等。`views`目录下的`isv/custom`子目录包含针对不同支付渠道（如支付宝、微信支付）的特定配置组件，而`sys`目录则包含系统级功能模块。

**文档来源**
- [AlipayPayConfig.vue](file://unipay-web-ui/unipay-ui-manager/src/views/isv/custom/AlipayPayConfig.vue)
- [WxpayPayConfig.vue](file://unipay-web-ui/unipay-ui-manager/src/views/isv/custom/WxpayPayConfig.vue)
- [SysConfig.vue](file://unipay-web-ui/unipay-ui-manager/src/views/sys/config/SysConfig.vue)
- [EntPage.vue](file://unipay-web-ui/unipay-ui-manager/src/views/ent/EntPage.vue)
- [SysLog.vue](file://unipay-web-ui/unipay-ui-manager/src/views/sys/log/SysLog.vue)

## 核心组件
运营平台前端的核心组件包括ISV支付配置、系统参数配置、权限管理和系统日志。ISV支付配置组件允许超级管理员为不同的ISV配置支付宝和微信支付的详细参数，包括密钥、证书和API版本等。系统参数配置组件提供了一个集中的界面来管理应用级别的配置，这些配置对整个系统有全局影响。权限管理组件用于定义和管理系统的资源权限，而系统日志组件则提供了对所有操作日志的查询和审计功能。

**文档来源**
- [AlipayPayConfig.vue](file://unipay-web-ui/unipay-ui-manager/src/views/isv/custom/AlipayPayConfig.vue)
- [WxpayPayConfig.vue](file://unipay-web-ui/unipay-ui-manager/src/views/isv/custom/WxpayPayConfig.vue)
- [SysConfig.vue](file://unipay-web-ui/unipay-ui-manager/src/views/sys/config/SysConfig.vue)
- [EntPage.vue](file://unipay-web-ui/unipay-ui-manager/src/views/ent/EntPage.vue)
- [SysLog.vue](file://unipay-web-ui/unipay-ui-manager/src/views/sys/log/SysLog.vue)

## 架构概述
运营平台前端采用前后端分离的架构，前端通过API与后端服务进行交互。前端应用使用Vue 3的组合式API和TypeScript，实现了响应式和类型安全的开发体验。UI组件基于Ant Design Vue，确保了界面的一致性和专业性。状态管理通过Vue的响应式系统实现，而路由和权限控制则通过Vue Router和自定义指令完成。后端服务提供RESTful API，处理前端的请求并返回JSON格式的数据。

```mermaid
graph TB
subgraph "前端"
UI[运营平台前端]
Router[Vue Router]
State[响应式状态]
end
subgraph "后端"
API[RESTful API]
Service[业务服务]
DB[(数据库)]
end
UI --> API
API --> Service
Service --> DB
```

**图表来源**
- [AlipayPayConfig.vue](file://unipay-web-ui/unipay-ui-manager/src/views/isv/custom/AlipayPayConfig.vue)
- [SysConfig.vue](file://unipay-web-ui/unipay-ui-manager/src/views/sys/config/SysConfig.vue)

## 详细组件分析
### ISV支付配置分析
ISV支付配置组件位于`isv/custom`目录下，包含`AlipayPayConfig.vue`和`WxpayPayConfig.vue`两个主要组件。这些组件为超级管理员提供了配置ISV支付参数的界面，包括支付接口费率、状态、备注以及特定于支付渠道的参数。

#### 支付宝支付配置
```mermaid
classDiagram
class AlipayPayConfig {
+vdata : any
+ifParamsRules : reactive
+show(isvNo, record)
+getIsvPayConfig()
+onSubmit()
+clearEmptyKey(key)
+uploadSuccess(value, name)
+onClose()
}
AlipayPayConfig --> JeepayUpload : "使用"
AlipayPayConfig --> req : "调用"
AlipayPayConfig --> getIsvPayConfigUnique : "调用"
AlipayPayConfig --> upload : "调用"
```

**图表来源**
- [AlipayPayConfig.vue](file://unipay-web-ui/unipay-ui-manager/src/views/isv/custom/AlipayPayConfig.vue)

#### 微信支付配置
```mermaid
classDiagram
class WxpayPayConfig {
+vdata : any
+ifParamsRules : reactive
+show(isvNo, record)
+getIsvPayConfig()
+onSubmit()
+clearEmptyKey(key)
+uploadSuccess(value, name)
+onClose()
}
WxpayPayConfig --> JeepayUpload : "使用"
WxpayPayConfig --> req : "调用"
WxpayPayConfig --> getIsvPayConfigUnique : "调用"
WxpayPayConfig --> upload : "调用"
```

**图表来源**
- [WxpayPayConfig.vue](file://unipay-web-ui/unipay-ui-manager/src/views/isv/custom/WxpayPayConfig.vue)

### 系统参数配置分析
系统参数配置组件`SysConfig.vue`提供了一个集中的界面来管理应用级别的配置。这些配置存储在数据库的`t_sys_config`表中，对整个系统有全局影响。

#### 系统配置
```mermaid
classDiagram
class SysConfig {
+vdata : any
+detail()
+selectTabs(key)
+confirm(e)
}
SysConfig --> req : "调用"
SysConfig --> getConfigs : "调用"
SysConfig --> API_URL_SYS_CONFIG : "使用"
```

**图表来源**
- [SysConfig.vue](file://unipay-web-ui/unipay-ui-manager/src/views/sys/config/SysConfig.vue)
- [SysConfig.java](file://core/src/main/java/com/unipay/core/entity/SysConfig.java)
- [SysConfigService.java](file://service/src/main/java/com/unipay/service/impl/SysConfigService.java)

### 权限管理分析
权限管理组件`EntPage.vue`用于定义和管理系统的资源权限。权限信息存储在`t_sys_entitlement`表中，支持不同系统（运营平台、商户系统）的权限管理。

#### 权限管理
```mermaid
classDiagram
class EntPage {
+vdata : any
+refTable()
+reqTableDataFunc()
+updateState(recordId, state)
+editFunc(recordId)
}
EntPage --> JeepayTable : "使用"
EntPage --> InfoAddOrEdit : "使用"
EntPage --> getEntTree : "调用"
EntPage --> reqLoad : "调用"
EntPage --> API_URL_ENT_LIST : "使用"
```

**图表来源**
- [EntPage.vue](file://unipay-web-ui/unipay-ui-manager/src/views/ent/EntPage.vue)
- [SysEntitlement.java](file://core/src/main/java/com/unipay/core/entity/SysEntitlement.java)
- [SysEntitlementService.java](file://service/src/main/java/com/unipay/service/impl/SysEntitlementService.java)

### 系统日志分析
系统日志组件`SysLog.vue`提供了对所有操作日志的查询和审计功能。日志信息存储在`t_sys_log`表中，支持按时间、用户ID、用户名和所属系统进行筛选。

#### 系统日志
```mermaid
classDiagram
class SysLog {
+vdata : any
+reqTableDataFunc(params)
+delFunc()
+searchFunc()
+onSelectChange(selectedRowKeys)
+detailFunc(recordId)
+onChange(date, dateString)
+disabledDate(current)
+onClose()
+queryhFunc()
}
SysLog --> JeepayTable : "使用"
SysLog --> req : "调用"
SysLog --> API_URL_SYS_LOG : "使用"
```

**图表来源**
- [SysLog.vue](file://unipay-web-ui/unipay-ui-manager/src/views/sys/log/SysLog.vue)
- [SysLog.java](file://core/src/main/java/com/unipay/core/entity/SysLog.java)
- [SysLogService.java](file://service/src/main/java/com/unipay/service/impl/SysLogService.java)

## 依赖分析
运营平台前端依赖于后端提供的RESTful API来获取和更新数据。前端组件通过`req`模块调用API，与后端服务进行通信。后端服务依赖于MyBatis-Plus进行数据库操作，并使用Redis进行缓存。`SysConfigService`中的`IS_USE_CACHE`标志控制是否启用内存缓存，以提高系统性能。

```mermaid
graph TD
    A[运营平台前端]