# 设计原则

<cite>
**本文档引用的文件**
- [IMQSender.java](file://components/components-mq/src/main/java/com/unipay/components/mq/vender/IMQSender.java)
- [IMQMsgReceiver.java](file://components/components-mq/src/main/java/com/unipay/components/mq/vender/IMQMsgReceiver.java)
- [AliYunRocketMQFactory.java](file://components/components-mq/src/main/java/com/unipay/components/mq/vender/aliyunrocketmq/AliYunRocketMQFactory.java)
- [MqThreadExecutor.java](file://components/components-mq/src/main/java/com/unipay/components/mq/executor/MqThreadExecutor.java)
- [MQVenderCS.java](file://components/components-mq/src/main/java/com/unipay/components/mq/constant/MQVenderCS.java)
- [MQSendTypeEnum.java](file://components/components-mq/src/main/java/com/unipay/components/mq/constant/MQSendTypeEnum.java)
- [AbstractMQ.java](file://components/components-mq/src/main/java/com/unipay/components/mq/model/AbstractMQ.java)
- [ActiveMQConfig.java](file://components/components-mq/src/main/java/com/unipay/components/mq/vender/activemq/ActiveMQConfig.java)
- [RabbitMQSender.java](file://components/components-mq/src/main/java/com/unipay/components/mq/vender/rabbitmq/RabbitMQSender.java)
- [RocketMQSender.java](file://components/components-mq/src/main/java/com/unipay/components/mq/vender/rocketmq/RocketMQSender.java)
- [AliYunRocketMQSender.java](file://components/components-mq/src/main/java/com/unipay/components/mq/vender/aliyunrocketmq/AliYunRocketMQSender.java)
- [ActiveMQSender.java](file://components/components-mq/src/main/java/com/unipay/components/mq/vender/activemq/ActiveMQSender.java)
- [RabbitMQConfig.java](file://components/components-mq/src/main/java/com/unipay/components/mq/vender/rabbitmq/RabbitMQConfig.java)
- [RabbitMQBeanProcessor.java](file://components/components-mq/src/main/java/com/unipay/components/mq/vender/rabbitmq/RabbitMQBeanProcessor.java)
- [AbstractAliYunRocketMQReceiver.java](file://components/components-mq/src/main/java/com/unipay/components/mq/vender/aliyunrocketmq/AbstractAliYunRocketMQReceiver.java)
- [PayOrderMchNotifyActiveMQReceiver.java](file://components/components-mq/src/main/java/com/unipay/components/mq/vender/activemq/receive/PayOrderMchNotifyActiveMQReceiver.java)
</cite>

## 目录
1. [引言](#引言)
2. [消息队列核心目的](#消息队列核心目的)
3. [多MQ支持架构设计](#多mq支持架构设计)
4. [消息发送与线程池管理](#消息发送与线程池管理)
5. [消息序列化机制](#消息序列化机制)
6. [MQ厂商配置指南](#mq厂商配置指南)
7. [消息类型定义](#消息类型定义)
8. [总结](#总结)

## 引言
本设计文档详细阐述了uni-pay系统中消息队列（Message Queue, MQ）的设计原则与实现机制。系统通过引入消息队列实现了服务解耦、异步处理和流量削峰三大核心目标。文档将深入解析多MQ支持的抽象架构，包括接口定义、工厂模式实现、线程池管理以及序列化机制，为开发者提供全面的技术指导。

**Section sources**
- [IMQSender.java](file://components/components-mq/src/main/java/com/unipay/components/mq/vender/IMQSender.java)
- [IMQMsgReceiver.java](file://components/components-mq/src/main/java/com/unipay/components/mq/vender/IMQMsgReceiver.java)

## 消息队列核心目的
消息队列在uni-pay系统中扮演着至关重要的角色，其主要目的包括：

1. **服务解耦**：通过消息队列，生产者和消费者之间无需直接通信，降低了系统各模块间的耦合度，使得系统更易于维护和扩展。
2. **异步处理**：对于耗时操作（如商户通知、订单分账等），系统可以先将消息发送到队列，然后立即返回响应，后续由消费者异步处理，从而提升用户体验和系统响应速度。
3. **流量削峰**：在高并发场景下，消息队列可以作为缓冲区，平滑处理突发的流量高峰，防止后端服务因瞬时压力过大而崩溃。

这些设计原则确保了系统的稳定性、可扩展性和高性能。

## 多MQ支持架构设计
为了实现对多种消息队列中间件的无缝支持，系统采用了高度抽象的架构设计。

### 抽象接口定义
系统定义了两个核心接口：`IMQSender` 和 `IMQMsgReceiver`，分别用于消息的发送和接收。

- `IMQSender` 接口提供了 `send(AbstractMQ mqModel)` 和 `send(AbstractMQ mqModel, int delay)` 两个方法，支持实时和延迟消息的发送。
- `IMQMsgReceiver` 接口定义了 `receiveMsg(String msg)` 方法，用于接收和处理消息。

```mermaid
classDiagram
class IMQSender {
<<interface>>
+send(mqModel AbstractMQ)
+send(mqModel AbstractMQ, delay int)
}
class IMQMsgReceiver {
<<interface>>
+receiveMsg(msg String)
}
class AbstractMQ {
<<abstract>>
+getMQName() String
+getMQType() MQSendTypeEnum
+toMessage() String
}
IMQSender <|-- ActiveMQSender
IMQSender <|-- RabbitMQSender
IMQSender <|-- RocketMQSender
IMQSender <|-- AliYunRocketMQSender
IMQMsgReceiver <|-- AbstractAliYunRocketMQReceiver
IMQMsgReceiver <|-- PayOrderMchNotifyActiveMQReceiver
AbstractMQ <|-- PayOrderMchNotifyMQ
AbstractMQ <|-- PayOrderDivisionMQ
```

**Diagram sources**
- [IMQSender.java](file://components/components-mq/src/main/java/com/unipay/components/mq/vender/IMQSender.java)
- [IMQMsgReceiver.java](file://components/components-mq/src/main/java/com/unipay/components/mq/vender/IMQMsgReceiver.java)
- [AbstractMQ.java](file://components/components-mq/src/main/java/com/unipay/components/mq/model/AbstractMQ.java)

### 工厂模式实现
系统通过工厂模式实现了对不同MQ厂商的动态切换。以 `AliYunRocketMQFactory` 为例，该类通过Spring的`@ConditionalOnProperty`注解，根据配置文件中的`isys.mq.vender`属性值来决定是否实例化。

```mermaid
sequenceDiagram
participant Config as 配置文件
participant Factory as AliYunRocketMQFactory
participant Producer as producerClient
participant Consumer as aliyunRocketMQConsumer
Config->>Factory : isys.mq.vender=aliYunRocketMQ
Factory->>Factory : afterPropertiesSet()
Factory->>Producer : 创建生产者实例
Factory->>Consumer : 创建消费者实例
Producer-->>Factory : 返回生产者
Consumer-->>Factory : 返回消费者
```

**Diagram sources**
- [AliYunRocketMQFactory.java](file://components/components-mq/src/main/java/com/unipay/components/mq/vender/aliyunrocketmq/AliYunRocketMQFactory.java)

**Section sources**
- [AliYunRocketMQFactory.java](file://components/components-mq/src/main/java/com/unipay/components/mq/vender/aliyunrocketmq/AliYunRocketMQFactory.java)

## 消息发送与线程池管理
### 消息发送实现
系统为每种MQ厂商提供了具体的发送器实现：
- `ActiveMQSender`：基于JmsTemplate实现ActiveMQ消息发送。
- `RabbitMQSender`：利用RabbitTemplate实现RabbitMQ消息发送。
- `RocketMQSender`：通过RocketMQTemplate实现RocketMQ消息发送。
- `AliYunRocketMQSender`：使用阿里云ONS SDK实现消息发送。

所有发送器均实现了`IMQSender`接口，确保了API的一致性。

### 线程池管理
系统通过`MqThreadExecutor`类配置了专用的线程池，用于处理高并发场景下的消息发送任务。

```mermaid
flowchart TD
Start([线程池初始化]) --> CorePool["核心线程数: 20"]
CorePool --> MaxPool["最大线程数: 300"]
MaxPool --> Queue["队列容量: 10"]
Queue --> KeepAlive["空闲时间: 60秒"]
KeepAlive --> Policy["拒绝策略: CallerRunsPolicy"]
Policy --> End([线程池就绪])
```

该线程池专门用于处理支付订单商户通知（`payOrderMchNotifyExecutor`），确保在高并发下仍能稳定处理大量通知请求。

**Section sources**
- [MqThreadExecutor.java](file://components/components-mq/src/main/java/com/unipay/components/mq/executor/MqThreadExecutor.java)

## 消息序列化机制
系统中的消息体通过`AbstractMQ`类的`toMessage()`方法进行序列化。具体的消息模型（如`PayOrderMchNotifyMQ`）需要实现该方法，将对象转换为字符串格式（通常是JSON），以便在网络中传输。接收方则通过对应的`parse()`方法将字符串反序列化为对象。

**Section sources**
- [AbstractMQ.java](file://components/components-mq/src/main/java/com/unipay/components/mq/model/AbstractMQ.java)
- [PayOrderMchNotifyActiveMQReceiver.java](file://components/components-mq/src/main/java/com/unipay/components/mq/vender/activemq/receive/PayOrderMchNotifyActiveMQReceiver.java)

## MQ厂商配置指南
开发者可以通过配置文件中的`isys.mq.vender`属性来选择使用的MQ厂商。

```mermaid
graph TD
A[配置 isys.mq.vender] --> B{值为?}
B --> |activeMQ| C[使用ActiveMQ]
B --> |rabbitMQ| D[使用RabbitMQ]
B --> |rocketMQ| E[使用RocketMQ]
B --> |aliYunRocketMQ| F[使用阿里云RocketMQ]
```

**Section sources**
- [MQVenderCS.java](file://components/components-mq/src/main/java/com/unipay/components/mq/constant/MQVenderCS.java)

## 消息类型定义
`MQSendTypeEnum`枚举定义了两种消息类型：
- `QUEUE`：点对点模式，消息仅被一个消费者消费。
- `BROADCAST`：广播模式，所有订阅者都能接收到消息。

该枚举确保了不同MQ厂商在消息模式上的统一抽象。

**Section sources**
- [MQSendTypeEnum.java](file://components/components-mq/src/main/java/com/unipay/components/mq/constant/MQSendTypeEnum.java)

## 总结
uni-pay系统通过精心设计的消息队列架构，实现了服务的高效解耦、异步处理和流量削峰。通过`IMQSender`和`IMQMsgReceiver`接口的抽象，结合工厂模式，系统能够灵活支持多种MQ厂商。专用的线程池和统一的序列化机制进一步保障了系统的性能和稳定性。开发者可以根据实际需求，通过简单的配置即可完成MQ厂商的切换。