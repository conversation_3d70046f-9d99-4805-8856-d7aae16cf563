# 用户管理

<cite>
**本文档引用的文件**   
- [SysUser.java](file://core\src\main\java\com\unipay\core\entity\SysUser.java)
- [SysRole.java](file://core\src\main\java\com\unipay\core\entity\SysRole.java)
- [SysEntitlement.java](file://core\src\main\java\com\unipay\core\entity\SysEntitlement.java)
- [SysUserController.java](file://sys-manager\src\main\java\com\unipay\mgr\ctrl\sysuser\SysUserController.java)
- [WebSecurityConfig.java](file://sys-manager\src\main\java\com\unipay\mgr\secruity\WebSecurityConfig.java)
- [JeeUserDetailsServiceImpl.java](file://sys-manager\src\main\java\com\unipay\mgr\secruity\JeeUserDetailsServiceImpl.java)
- [JeeAuthenticationTokenFilter.java](file://sys-manager\src\main\java\com\unipay\mgr\secruity\JeeAuthenticationTokenFilter.java)
- [SysUserAuthService.java](file://service\src\main\java\com\unipay\service\impl\SysUserAuthService.java)
- [AuthService.java](file://sys-manager\src\main\java\com\unipay\mgr\service\AuthService.java)
- [JeeUserDetails.java](file://core\src\main\java\com\unipay\core\model\security\JeeUserDetails.java)
</cite>

## 目录
1. [核心实体与权限模型](#核心实体与权限模型)
2. [用户生命周期管理API](#用户生命周期管理api)
3. [安全配置与JWT认证](#安全配置与jwt认证)
4. [角色权限分配流程](#角色权限分配流程)
5. [扩展与集成](#扩展与集成)

## 核心实体与权限模型

系统采用基于角色的访问控制（RBAC）模型，核心由三个实体构成：`SysUser`（系统用户）、`SysRole`（系统角色）和`SysEntitlement`（系统权限）。它们通过关联表建立多对多关系，实现灵活的权限管理。

```mermaid
erDiagram
SysUser ||--o{ SysUserRoleRela : "拥有"
SysRole ||--o{ SysUserRoleRela : "被分配"
SysRole ||--o{ SysRoleEntRela : "包含"
SysEntitlement ||--o{ SysRoleEntRela : "被包含"
SysUser {
Long sysUserId PK
String loginUsername
String realname
String telphone
Byte sex
String avatarUrl
String userNo
Byte isAdmin
Byte state
String sysType
String belongInfoId
Date createdAt
Date updatedAt
}
SysRole {
String roleId PK
String roleName
String sysType
String belongInfoId
Date updatedAt
}
SysEntitlement {
String entId PK
String entName
String menuIcon
String menuUri
String componentName
String entType
Byte quickJump
Byte state
String pid
Integer entSort
String sysType
Date createdAt
Date updatedAt
}
SysUserRoleRela {
Long userId PK, FK
String roleId PK, FK
}
SysRoleEntRela {
String roleId PK, FK
String entId PK, FK
}
```

**图源**
- [SysUser.java](file://core\src\main\java\com\unipay\core\entity\SysUser.java)
- [SysRole.java](file://core\src\main\java\com\unipay\core\entity\SysRole.java)
- [SysEntitlement.java](file://core\src\main\java\com\unipay\core\entity\SysEntitlement.java)

### SysUser（系统用户）
`SysUser`实体代表系统中的操作员，是权限分配的最终对象。

**字段说明：**
- **sysUserId**: 系统用户ID，主键。
- **loginUsername**: 登录用户名，用于身份认证。
- **realname**: 用户真实姓名。
- **telphone**: 手机号，可用于手机号登录。
- **sex**: 性别，0-未知，1-男，2-女。
- **avatarUrl**: 头像地址。
- **userNo**: 员工编号，具有唯一性。
- **isAdmin**: 是否为超管，1-是，拥有系统全部权限。
- **state**: 用户状态，0-停用，1-启用。
- **sysType**: 所属系统，MGR-运营平台，MCH-商户中心。
- **belongInfoId**: 所属商户ID，平台用户为"0"。

**节源**
- [SysUser.java](file://core\src\main\java\com\unipay\core\entity\SysUser.java#L23-L117)

### SysRole（系统角色）
`SysRole`实体代表一组权限的集合，用于简化权限分配。

**字段说明：**
- **roleId**: 角色ID，主键，通常以"ROLE_"开头。
- **roleName**: 角色名称。
- **sysType**: 所属系统。
- **belongInfoId**: 所属商户ID。

**节源**
- [SysRole.java](file://core\src\main\java\com\unipay\core\entity\SysRole.java#L22-L68)

### SysEntitlement（系统权限）
`SysEntitlement`实体代表系统中最细粒度的权限单元，可以是菜单、按钮或API。

**字段说明：**
- **entId**: 权限ID，主键，命名规范为`ENT_功能模块_子模块_操作`。
- **entName**: 权限名称。
- **menuIcon**: 菜单图标。
- **menuUri**: 菜单或路由地址。
- **componentName**: 前端组件名称。
- **entType**: 权限类型，ML-左侧菜单，MO-其他菜单，PB-页面/按钮。
- **quickJump**: 是否为快速开始菜单。
- **state**: 权限状态，0-停用，1-启用。
- **pid**: 父ID，用于构建菜单树。
- **entSort**: 排序字段。

**节源**
- [SysEntitlement.java](file://core\src\main\java\com\unipay\core\entity\SysEntitlement.java#L22-L116)

## 用户生命周期管理API

`SysUserController`提供了对`SysUser`进行增删改查的完整API接口，所有接口均基于JWT进行认证和权限校验。

```mermaid
sequenceDiagram
participant Client as 客户端
participant Controller as SysUserController
participant Service as SysUserService
participant Mapper as SysUserMapper
Client->>Controller : GET /api/sysUsers (列表)
Controller->>Service : page()
Service->>Mapper : selectPage()
Mapper-->>Service : IPage<SysUser>
Service-->>Controller : IPage<SysUser>
Controller-->>Client : ApiPageRes
Client->>Controller : POST /api/sysUsers (添加)
Controller->>Service : addSysUser()
Service->>Mapper : insert()
Service->>SysUserAuthService : addUserAuthDefault()
Service-->>Controller : void
Controller-->>Client : ApiRes.ok()
Client->>Controller : PUT /api/sysUsers/{id} (修改)
Controller->>Service : updateSysUser()
Service->>Mapper : updateById()
Service-->>Controller : void
Controller-->>Client : ApiRes.ok()
Client->>Controller : DELETE /api/sysUsers/{id} (删除)
Controller->>Service : removeUser()
Service->>Mapper : deleteById()
Service-->>Controller : void
Controller-->>Client : ApiRes.ok()
```

**图源**
- [SysUserController.java](file://sys-manager\src\main\java\com\unipay\mgr\ctrl\sysuser\SysUserController.java)
- [SysUserService.java](file://service\src\main\java\com\unipay\service\impl\SysUserService.java)

### API接口详情

| HTTP方法 | 路径 | 操作 | 权限要求 | 说明 |
| :--- | :--- | :--- | :--- | :--- |
| GET | /api/sysUsers | 查询操作员列表 | `ENT_UR_USER_LIST` | 支持分页、按姓名和ID查询 |
| GET | /api/sysUsers/{recordId} | 查询操作员详情 | `ENT_UR_USER_EDIT` | 返回指定ID的用户信息 |
| POST | /api/sysUsers | 添加操作员 | `ENT_UR_USER_ADD` | 创建新用户，自动初始化认证信息 |
| PUT | /api/sysUsers/{recordId} | 修改操作员信息 | `ENT_UR_USER_EDIT` | 更新用户信息，可选择重置密码 |
| DELETE | /api/sysUsers/{recordId} | 删除操作员 | `ENT_UR_USER_DELETE` | 删除用户及其所有关联信息 |

**节源**
- [SysUserController.java](file://sys-manager\src\main\java\com\unipay\mgr\ctrl\sysuser\SysUserController.java#L35-L213)

## 安全配置与JWT认证

系统的安全配置由`WebSecurityConfig`类定义，采用基于JWT的无状态认证机制，结合Spring Security实现细粒度的权限控制。

```mermaid
sequenceDiagram
participant Client as 客户端
participant Filter as JeeAuthenticationTokenFilter
participant Security as Spring Security Context
participant Redis as Redis缓存
participant Service as JeeUserDetailsServiceImpl
Note over Client,Filter : 用户登录
Client->>Service : POST /auth/login
Service->>Service : authenticate()
Service->>Service : loadUserByUsername()
Service-->>Client : JWT Token
Note over Client,Filter : 后续请求
Client->>Filter : GET /api/users iToken : JWT
Filter->>Filter : commonFilter()
Filter->>Redis : getObject(cacheKey)
Redis-->>Filter : JeeUserDetails
Filter->>Security : setAuthentication()
Filter->>Filter : expire(cacheKey)
Filter->>Client : 继续处理请求
```

**图源**
- [WebSecurityConfig.java](file://sys-manager\src\main\java\com\unipay\mgr\secruity\WebSecurityConfig.java)
- [JeeAuthenticationTokenFilter.java](file://sys-manager\src\main\java\com\unipay\mgr\secruity\JeeAuthenticationTokenFilter.java)
- [JeeUserDetailsServiceImpl.java](file://sys-manager\src\main\java\com\unipay\mgr\secruity\JeeUserDetailsServiceImpl.java)

### JWT认证流程

1.  **用户登录**: 用户通过`/auth/login`接口提交用户名和密码。
2.  **身份验证**: `AuthService`调用Spring Security的`authenticationManager.authenticate()`方法。
3.  **加载用户详情**: `JeeUserDetailsServiceImpl`的`loadUserByUsername`方法被调用，根据用户名查询`SysUserAuth`和`SysUser`实体。
4.  **生成JWT**: 认证成功后，`AuthService`生成一个包含`cacheKey`的JWT令牌，并将其存入Redis。
5.  **请求认证**: 客户端在后续请求的Header中携带JWT令牌。
6.  **令牌解析**: `JeeAuthenticationTokenFilter`拦截请求，解析JWT，获取`cacheKey`。
7.  **加载用户信息**: 通过`cacheKey`从Redis中加载`JeeUserDetails`对象。
8.  **设置上下文**: 将`JeeUserDetails`放入Spring Security上下文中，完成认证。

### 权限校验机制

权限校验在`WebSecurityConfig`中通过`@PreAuthorize`注解和`authorizeHttpRequests`配置实现。

-   **接口级校验**: 在`SysUserController`的方法上使用`@PreAuthorize("hasAuthority('ENT_XXX')")`，确保调用者拥有特定权限ID。
-   **路径级校验**: 在`WebSecurityConfig`中，通过`requestMatchers("/api/**").authenticated()`确保所有API接口都需要认证。

### 接口访问控制

`WebSecurityConfig`定义了详细的访问控制规则：

-   **匿名访问**: 静态资源、Swagger文档、前端路由等路径允许匿名访问。
-   **认证访问**: 所有`/api/**`路径下的接口必须经过认证。
-   **跨域支持**: 通过`CorsFilter`配置，支持前端应用的跨域请求。

**节源**
- [WebSecurityConfig.java](file://sys-manager\src\main\java\com\unipay\mgr\secruity\WebSecurityConfig.java#L33-L162)
- [JeeAuthenticationTokenFilter.java](file://sys-manager\src\main\java\com\unipay\mgr\secruity\JeeAuthenticationTokenFilter.java#L32-L83)
- [JeeUserDetailsServiceImpl.java](file://sys-manager\src\main\java\com\unipay\mgr\secruity\JeeUserDetailsServiceImpl.java#L21-L69)

## 角色权限分配流程

角色权限的分配和管理主要通过后端数据持久化和前端UI操作协同完成。

### UI操作流程

1.  管理员在前端界面进入“角色管理”模块。
2.  选择一个角色，进入“权限分配”页面。
3.  前端通过API获取所有可用的权限节点（`SysEntitlement`），并以树形结构展示。
4.  管理员勾选需要分配给该角色的权限。
5.  提交操作，前端将角色ID和选中的权限ID列表发送至后端。

### 后端数据持久化

后端接收到权限分配请求后，执行以下操作：
1.  **删除旧关联**: 删除`SysRoleEntRela`表中该角色ID的所有旧记录。
2.  **插入新关联**: 将新的角色ID与权限ID组合，批量插入`SysRoleEntRela`表。
3.  **更新缓存**: 如果用户已登录且其角色权限被修改，系统会调用`AuthService.refAuthentication()`方法，更新Redis中该用户的`JeeUserDetails`对象，确保新权限立即生效。

**节源**
- [SysRoleEntRela.java](file://core\src\main\java\com\unipay\core\entity\SysRoleEntRela.java)
- [SysRoleService.java](file://service\src\main\java\com\unipay\service\impl\SysRoleService.java)
- [AuthService.java](file://sys-manager\src\main\java\com\unipay\mgr\service\AuthService.java)

## 扩展与集成

### 自定义权限策略

当前系统使用`hasAuthority()`进行权限校验。如需更复杂的策略（如基于资源的访问控制），可扩展Spring Security的`AccessDecisionManager`或`PermissionEvaluator`。

### 与外部身份提供商集成

系统可通过扩展`JeeUserDetailsServiceImpl`来集成外部身份提供商（如LDAP、OAuth2）。
1.  修改`loadUserByUsername`方法，在查询本地数据库前，先尝试通过外部协议进行认证。
2.  认证成功后，可选择在本地创建一个映射用户，或直接返回一个包含外部信息的`JeeUserDetails`对象。
3.  权限信息仍可从本地数据库加载，实现身份与权限的分离。

**节源**
- [JeeUserDetailsServiceImpl.java](file://sys-manager\src\main\java\com\unipay\mgr\secruity\JeeUserDetailsServiceImpl.java)
- [JeeUserDetails.java](file://core\src\main\java\com\unipay\core\model\security\JeeUserDetails.java)