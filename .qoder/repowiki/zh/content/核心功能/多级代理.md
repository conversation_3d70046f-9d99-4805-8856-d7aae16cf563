
# 多级代理

<cite>
**本文档引用文件**   
- [AgentInfo.java](file://core/src/main/java/com/unipay/core/entity/AgentInfo.java)
- [AgentInfoService.java](file://service/src/main/java/com/unipay/service/impl/AgentInfoService.java)
- [AgentProfitRecord.java](file://core/src/main/java/com/unipay/core/entity/AgentProfitRecord.java)
- [AgentProfitRecordService.java](file://service/src/main/java/com/unipay/service/impl/AgentProfitRecordService.java)
- [AgentMchRelation.java](file://core/src/main/java/com/unipay/core/entity/AgentMchRelation.java)
- [AgentMchRelationService.java](file://service/src/main/java/com/unipay/service/impl/AgentMchRelationService.java)
- [AgentInfoMapper.java](file://service/src/main/java/com/unipay/service/mapper/AgentInfoMapper.java)
- [AgentProfitRecordMapper.java](file://service/src/main/java/com/unipay/service/mapper/AgentProfitRecordMapper.java)
- [AgentMchRelationMapper.java](file://service/src/main/java/com/unipay/service/mapper/AgentMchRelationMapper.java)
- [AgentInfoController.java](file://sys-agent/src/main/java/com/unipay/agent/ctrl/agent/AgentInfoController.java)
- [ProfitRecordController.java](file://sys-agent/src/main/java/com/unipay/agent/ctrl/profit/ProfitRecordController.java)
- [MainController.java](file://sys-agent/src/main/java/com/unipay/agent/ctrl/MainController.java)
</cite>

## 目录
1. [代理商管理](#代理商管理)
2. [层级关系维护](#层级关系维护)
3. [分润计算](#分润计算)
4. [API接口说明](#api接口说明)
5. [权限控制与数据隔离](#权限控制与数据隔离)
6. [报表生成](#报表生成)

## 代理商管理

### AgentInfo实体字段含义

`AgentInfo`实体类定义了代理商的核心信息，其主要字段含义如下：

- **agentNo**: 代理商号，唯一标识符，作为主键
- **agentName**: 代理商名称
- **agentShortName**: 代理商简称
- **loginUsername**: 登录用户名，用于系统登录
- **agentType**: 代理商类型，1-一级代理商，2-二级代理商，3-三级代理商等
- **parentAgentNo**: 上级代理商号，用于构建层级关系
- **agentLevel**: 代理商层级，1-一级，2-二级，3-三级等
- **agentPath**: 代理商层级路径，格式为`/A001/A002/A003`，用于快速查询所有下级代理商
- **contactName**: 联系人姓名
- **contactTel**: 联系人手机号
- **contactEmail**: 联系人邮箱
- **province**: 省份
- **city**: 城市
- **district**: 区县
- **address**: 详细地址
- **profitRate**: 代理商分润比例
- **canDevelopAgent**: 是否允许发展下级代理商: 0-否, 1-是
- **canDevelopMch**: 是否允许发展下级商户: 0-否, 1-是
- **state**: 代理商状态: 0-停用, 1-正常
- **remark**: 代理商备注
- **initUserId**: 初始用户ID（创建代理商时，允许代理商登录的用户）
- **createdUid**: 创建者用户ID
- **createdBy**: 创建者姓名
- **createdAt**: 创建时间
- **updatedAt**: 更新时间

**Section sources**
- [AgentInfo.java](file://core/src/main/java/com/unipay/core/entity/AgentInfo.java#L25-L201)

### AgentInfoService核心功能

`AgentInfoService`提供了代理商的增删改查和上下级关系绑定功能：

- **创建代理商**: `createAgentWithUser`方法创建代理商并同时创建关联的系统用户。如果未指定代理商号，则自动生成。系统会根据上级代理商号自动构建`agentPath`和`agentLevel`。
- **查询代理商**: `selectPage`方法支持分页查询，可指定当前代理商号来查询其所有下级代理商（包括孙级代理商）。
- **更新代理商**: `updateAgent`方法更新代理商信息。
- **删除代理商**: `deleteAgentWithUser`方法删除代理商及其关联的用户数据，删除前会检查是否存在下级代理商。
- **层级路径构建**: `buildAgentPath`方法根据上级代理商的`agentPath`构建当前代理商的完整路径。
- **下级代理商查询**: `getAllSubAgentNos`方法获取指定代理商的所有下级代理商号列表（包括自己）。

```mermaid
classDiagram
class AgentInfoService {
+getByAgentNo(agentNo) AgentInfo
+createAgentWithUser(agentInfo, loginPassword) boolean
+selectPage(page, agentInfo, currentAgentNo) IPage~AgentInfo~
+updateAgent(agentInfo) boolean
+createAgent(agentInfo) boolean
+canDeleteAgent(agentNo) boolean
+deleteAgentWithUser(agentNo) boolean
+buildAgentPath(parentAgentNo, agentNo) String
+getAllSubAgentNos(agentNo) String[]
+countAllSubAgents(agentNo) long
}
class AgentInfoMapper {
+selectSubAgentsByPath(agentPath) AgentInfo[]
+selectDirectSubAgents(parentAgentNo) AgentInfo[]
+updateAgentPath(agentNo, agentPath) int
}
AgentInfoService --> AgentInfoMapper : "使用"
AgentInfoService --> SysUserService : "依赖"
AgentInfoService --> SysUserAuthService : "依赖"
AgentInfoService --> SysUserRoleRelaService : "依赖"
```

**Diagram sources **
- [AgentInfoService.java](file://service/src/main/java/com/unipay/service/impl/AgentInfoService.java#L38-L389)
- [AgentInfoMapper.java](file://service/src/main/java/com/unipay/service/mapper/AgentInfoMapper.java#L17-L41)

**Section sources**
- [AgentInfoService.java](file://service/src/main/java/com/unipay/service/impl/AgentInfoService.java#L38-L389)

## 层级关系维护

### 层级编码规则

代理商层级通过`agentPath`字段进行编码，采用斜杠分隔的路径格式，如`/A001/A002/A003`。这种编码方式具有以下优势：
- **快速查询**: 可以通过`LIKE`操作符快速查询某个代理商的所有下级代理商
- **层级清晰**: 路径中的层级数即为代理商的层级
- **排序友好**: 路径可以按字典序排序，保持层级结构

当创建新代理商时，系统会自动构建其`agentPath`：
1. 如果没有上级代理商，则`agentPath`为`/` + 代理商号
2. 如果有上级代理商，则`agentPath`为上级代理商的`agentPath` + `/` + 当前代理商号

同时，`agentLevel`字段会根据上级代理商的层级自动计算，根代理商为1级，每增加一级层级加1。

```mermaid
flowchart TD
Start([创建代理商]) --> CheckParent{"是否有上级代理商?"}
CheckParent --> |否| SetLevel1["设置agentLevel = 1<br/>设置agentPath = '/' + agentNo"]
CheckParent --> |是| GetParent["获取上级代理商信息"]
GetParent --> CalcLevel["计算agentLevel = parentLevel + 1"]
CalcLevel --> BuildPath["构建agentPath = parentPath + '/' + agentNo"]
SetLevel1 --> Save
BuildPath --> Save["保存代理商信息"]
Save --> End([完成])
```

**Diagram sources **
- [AgentInfoService.java](file://service/src/main/java/com/unipay/service/impl/AgentInfoService.java#L241-L274)

**Section sources**
- [AgentInfoService.java](file://service/src/main/java/com/unipay/service/impl/AgentInfoService.java#L241-L274)

### 上下级关系绑定

代理商与商户的关系通过`AgentMchRelation`实体进行管理，支持两种关系类型：
- **RELATION_TYPE_DIRECT (1)**: 直属商户，即由该代理商直接发展的商户
- **RELATION_TYPE_INDIRECT (2)**: 下级代理商的商户，即由该代理商的下级代理商发展的商户

`AgentMchRelationService`提供了以下核心功能：
- **创建关系**: `createHierarchicalRelations`方法在创建直属商户关系的同时，为所有上级代理商创建间接关系
- **查询关系**: `getMchNosByAgentNo`方法获取指定代理商关联的所有商户号（包括下级代理商的商户）
- **修复数据**: `fixExistingHierarchicalRelations`方法用于修复现有商户的层级关系数据，为现有的直接关系商户补充创建上级代理商的间接关系

```mermaid
sequenceDiagram
participant Agent as "代理商"
participant Service as "AgentMchRelationService"
participant DB as "数据库"
Agent->>Service : createHierarchicalRelations(agentNo, mchNo, profitRate)
Service->>Service : createRelation(直接关系)
Service->>Service : 获取上级代理商
loop 每个上级代理商
Service->>Service : checkExist(间接关系)
Service->>Service : createRelation(间接关系)
Service->>Service : 获取上上级代理商
end
Service->>DB : 批量保存所有关系
DB-->>Service : 保存结果
Service-->>Agent : 返回结果
```

**Diagram sources **
- [AgentMchRelationService.java](file://service/src/main/java/com/unipay/service/impl/AgentMchRelationService.java#L25-L311)

**Section sources**
- [AgentMchRelationService.java](file://service/src/main/java/com/unipay/service/impl/AgentMchRelationService.java#L25-L311)

## 分润计算

### 利润分配规则

分润计算基于商户手续费进行，计算公式为：
```
分润金额 = 商户手续费 × 分润比例
```

分润记录通过`AgentProfitRecord`实体进行管理，其核心字段包括：
- **agentNo**: 代理商号
- **mchNo**: 商户号
- **payOrderId**: 支付订单号
- **orderAmount**: 订单金额（单位分）
- **mchFeeAmount**: 商户手续费（单位分）
- **profitRate**: 分润比例
- **profitAmount**: 分润金额（单位分）
- **profitDate**: 分润日期
- **state**: 分润状态: 0-待结算, 1-已结算, 2-已取消

### 结算周期

分润记录的结算状态管理如下：
- **待结算 (STATE_WAIT_SETTLE)**: 分润记录刚创建时的状态
- **已结算 (STATE_SETTLED)**: 分润已结算，结算时间记录在`settleTime`字段
- **已取消 (STATE_CANCELLED)**: 分润记录被取消

系统支持批量结算和批量取消分润记录，通过`AgentProfitRecordService`的`batchSettleProfitRecords`和`batchCancelProfitRecords`方法实现。

### AgentProfitRecord生成过程

分润记录的生成过程如下：
1. 当支付订单成功时，系统检查该订单对应的商户是否有关联的代理商
2. 如果有关联代理商，则根据商户给该代理商的分润比例创建分润记录
3. 分润金额基于商户手续费计算
4. 分润记录状态初始化为"待结算"

```mermaid
flowchart TD
Start([支付订单成功]) --> CheckAgent{"商户是否有代理商?"}
CheckAgent --> |否| End1([结束])
CheckAgent --> |是| GetProfitRate["获取商户给代理商的分润比例"]
GetProfitRate --> CalcProfit["计算分润金额 = 手续费 × 分润比例"]
CalcProfit --> CreateRecord["创建AgentProfitRecord"]
CreateRecord --> SetState["设置状态为待结算"]
SetState --> SaveRecord["保存分润记录"]
SaveRecord --> End2([完成])
```

**Diagram sources **
- [AgentProfitRecordService.java](file://service/src/main/java/com/unipay/service/impl/AgentProfitRecordService.java#L43-L83)

**Section sources**
- [AgentProfitRecord.java](file://core/src/main/java/com/unipay/core/entity/AgentProfitRecord.java#L25-L128)
- [AgentProfitRecordService.java](file://service/src/main/java/com/unipay/service/impl/AgentProfitRecordService.java#L43-L83)

## API接口说明

### 代理商管理API

| 接口 | HTTP方法 | 路径 | 权限要求 | 功能描述 |
| --- | --- | --- | --- | --- |
| 查询代理商列表 | GET | /api/agentInfo | ENT_AGENT_LIST | 分页查询代理商列表，支持按代理商号、名称、状态等条件过滤 |
| 代理商详情 | GET | /api/agentInfo/{agentNo} | ENT_AGENT_INFO_VIEW | 获取指定代理商的详细信息 |
| 新增代理商 | POST | /api/agentInfo | ENT_AGENT_INFO_ADD | 创建新的代理商，同时创建关联的系统用户 |
| 更新代理商 | PUT | /api/agentInfo/{agentNo} | ENT_AGENT_INFO_EDIT | 更新指定代理商的信息 |
| 删除代理商 | DELETE | /api/agentInfo/{agentNo} | ENT_AGENT_INFO_DELETE | 删除指定代理商及其关联的用户数据 |

### 分润管理API

| 接口 | HTTP方法 | 路径 | 权限要求 | 功能描述 |
| --- | --- | --- | --- | --- |
| 查询分润记录 | GET | /api/profitRecord | ENT_PROFIT_RECORD_LIST | 分页查询分润记录，支持按商户号、状态、日期范围等条件过滤 |
| 分润记录详情 | GET | /api/profitRecord/{recordId} | ENT_PROFIT_RECORD_VIEW | 获取指定分润记录的详细信息 |
| 分润统计 | GET | /api/profitRecord/statistics | ENT_PROFIT_RECORD_LIST | 统计分润金额，支持按状态、日期范围进行统计 |

### 使用示例

**创建代理商示例：**
```json
POST /api/agentInfo
Headers: iToken: your_token

{
  "agentNo": "A001",
  "agentName": "一级代理商",
  "agentShortName": "一级",
  "contactName": "张三",
  "contactTel": "13800138000",
  "profitRate": 0.05,
  "loginUsername": "agent001",
  "loginPassword": "password123"
}
```

**查询分润记录示例：**
```json
GET /api/pro