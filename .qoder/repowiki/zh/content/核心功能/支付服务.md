# 支付服务

<cite>
**本文档引用的文件**  
- [UnifiedOrderController.java](file://sys-payment/src/main/java/com/unipay/pay/ctrl/payorder/UnifiedOrderController.java)
- [PayOrderProcessService.java](file://sys-payment/src/main/java/com/unipay/pay/service/PayOrderProcessService.java)
- [PayOrder.java](file://core/src/main/java/com/unipay/core/entity/PayOrder.java)
- [PayOrderService.java](file://service/src/main/java/com/unipay/service/impl/PayOrderService.java)
- [PayMchNotifyService.java](file://sys-payment/src/main/java/com/unipay/pay/service/PayMchNotifyService.java)
- [PayOrderDivisionProcessService.java](file://sys-payment/src/main/java/com/unipay/pay/service/PayOrderDivisionProcessService.java)
- [PayOrderDivisionMQ.java](file://components/components-mq/src/main/java/com/unipay/components/mq/model/PayOrderDivisionMQ.java)
- [PayOrderMchNotifyMQ.java](file://components/components-mq/src/main/java/com/unipay/components/mq/model/PayOrderMchNotifyMQ.java)
- [PayOrderDivisionRecord.java](file://core/src/main/java/com/unipay/core/entity/PayOrderDivisionRecord.java)
- [MchDivisionReceiver.java](file://core/src/main/java/com/unipay/core/entity/MchDivisionReceiver.java)
- [MchDivisionReceiverGroup.java](file://core/src/main/java/com/unipay/core/entity/MchDivisionReceiverGroup.java)
- [ChannelOrderReissueService.java](file://sys-payment/src/main/java/com/unipay/pay/service/ChannelOrderReissueService.java)
- [PayOrderExpiredTask.java](file://sys-payment/src/main/java/com/unipay/pay/task/PayOrderExpiredTask.java)
- [UnifiedOrderRQ.java](file://sys-payment/src/main/java/com/unipay/pay/rqrs/payorder/UnifiedOrderRQ.java)
- [UnifiedOrderRS.java](file://sys-payment/src/main/java/com/unipay/pay/rqrs/payorder/UnifiedOrderRS.java)
- [ChannelRetMsg.java](file://sys-payment/src/main/java/com/unipay/pay/rqrs/msg/ChannelRetMsg.java)
</cite>

## 目录
1. [统一下单功能](#统一下单功能)
2. [订单查询与退款](#订单查询与退款)
3. [转账功能](#转账功能)
4. [UnifiedOrderController请求处理流程](#unifiedordercontroller请求处理流程)
5. [PayOrderProcessService订单生命周期管理](#payorderprocessservice订单生命周期管理)
6. [API支付请求示例](#api支付请求示例)
7. [完整支付流程序列图](#完整支付流程序列图)
8. [常见错误码解释](#常见错误码解释)
9. [签名验证机制](#签名验证机制)
10. [安全最佳实践](#安全最佳实践)

## 统一下单功能

统一下单功能是支付服务的核心入口，通过`UnifiedOrderController`的`/api/pay/unifiedOrder`接口实现。该接口接收商户发起的支付请求，经过参数验证、商户配置加载、支付通道选择和支付请求转发等步骤，最终返回支付参数。

统一下单支持多种支付方式，包括支付宝条码支付（ali_bar）、微信JSAPI支付（wxpay_jsapi）等。根据`wayCode`参数的不同，系统会自动选择相应的支付通道进行处理。对于聚合支付场景（QR_CASHIER），系统会返回收银台页面供用户选择支付方式。

订单创建时，系统会生成唯一的支付订单号（payOrderId），并记录商户订单号（mchOrderNo）、支付金额（amount）、商品信息等关键数据。支付金额以分为单位，确保精度。系统还支持分账功能，可通过`divisionMode`参数设置分账模式。

**Section sources**
- [UnifiedOrderController.java](file://sys-payment/src/main/java/com/unipay/pay/ctrl/payorder/UnifiedOrderController.java#L24-L91)
- [UnifiedOrderRQ.java](file://sys-payment/src/main/java/com/unipay/pay/rqrs/payorder/UnifiedOrderRQ.java#L23-L159)
- [PayOrder.java](file://core/src/main/java/com/unipay/core/entity/PayOrder.java#L24-L278)

## 订单查询与退款

订单查询功能允许商户通过支付订单号或商户订单号查询订单状态。系统提供`queryMchOrder`方法，支持按商户号、支付订单号或商户订单号进行精确查询。查询结果包含订单的完整信息，包括支付状态、金额、时间戳等。

退款功能通过`RefundOrderProcessService`实现，支持全额退款和部分退款。退款请求需要提供原支付订单号、退款金额和退款原因。系统会验证退款金额不超过原订单金额，并检查订单状态是否允许退款。退款成功后，系统会更新支付订单的退款状态和退款金额。

系统还实现了退款补单机制，通过`RefundOrderReissueTask`定时任务，对长时间未收到上游渠道响应的退款订单进行状态查询和结果更新，确保退款状态的最终一致性。

**Section sources**
- [PayOrderService.java](file://service/src/main/java/com/unipay/service/impl/PayOrderService.java#L36-L640)
- [RefundOrderProcessService.java](file://sys-payment/src/main/java/com/unipay/pay/service/RefundOrderProcessService.java)
- [ChannelOrderReissueService.java](file://sys-payment/src/main/java/com/unipay/pay/service/ChannelOrderReissueService.java#L20-L119)

## 转账功能

转账功能允许商户向指定账户进行资金划转。系统通过`TransferOrderService`管理转账订单的生命周期，包括创建、查询、状态更新等操作。转账请求需要提供收款方信息、转账金额、转账原因等必要参数。

系统支持异步通知机制，当转账状态发生变化时，会通过`transferOrderNotify`方法向商户配置的回调地址发送通知。通知内容包含转账订单的最新状态和相关信息，并使用商户密钥进行签名验证，确保通知的完整性和真实性。

转账功能还实现了失败重试机制，对于因网络异常等原因导致的转账失败，系统会自动进行重试，提高转账成功率。

**Section sources**
- [TransferOrderService.java](file://service/src/main/java/com/unipay/service/impl/TransferOrderService.java)
- [PayMchNotifyService.java](file://sys-payment/src/main/java/com/unipay/pay/service/PayMchNotifyService.java#L25-L260)

## UnifiedOrderController请求处理流程

`UnifiedOrderController`的请求处理流程始于`unifiedOrder`方法，该方法处理`/api/pay/unifiedOrder`接口的POST请求。流程包括以下几个关键步骤：

1. **参数获取与验签**：通过`getRQByWithMchSign`方法获取请求参数并验证签名，确保请求的合法性和完整性。
2. **业务参数构建**：调用`buildBizRQ`方法构建具体的业务请求对象，根据`wayCode`参数选择相应的支付方式处理器。
3. **支付通道选择**：验证`wayCode`是否支持，通过`payWayService`检查支付方式是否存在。
4. **支付请求转发**：调用`unifiedOrder`方法将请求转发给具体的支付通道处理器。
5. **响应构建与签名**：将业务响应数据复制到统一响应对象中，根据订单状态决定是否返回支付参数，并使用商户密钥对响应进行签名。

对于自动条码支付（AUTO_BAR），系统会根据用户提供的授权码自动识别支付方式，提高了用户体验。

```mermaid
sequenceDiagram
participant Client as "客户端"
participant Controller as "UnifiedOrderController"
participant Service as "PayOrderProcessService"
participant DB as "数据库"
Client->>Controller : 发起统一下单请求
Controller->>Controller : 获取参数并验签
Controller->>Controller : 构建业务请求对象
Controller->>Controller : 验证支付方式
Controller->>Service : 转发支付请求
Service->>DB : 创建支付订单
DB-->>Service : 返回订单信息
Service->>Controller : 返回支付响应
Controller->>Controller : 构建响应并签名
Controller-->>Client : 返回支付参数
```

**Diagram sources**
- [UnifiedOrderController.java](file://sys-payment/src/main/java/com/unipay/pay/ctrl/payorder/UnifiedOrderController.java#L34-L61)
- [PayOrderProcessService.java](file://sys-payment/src/main/java/com/unipay/pay/service/PayOrderProcessService.java#L20-L97)

## PayOrderProcessService订单生命周期管理

`PayOrderProcessService`负责协调支付订单的完整生命周期管理，包括状态转换、异步通知和超时处理等核心功能。

### 状态转换

支付订单的状态机包含多个关键状态：订单生成（STATE_INIT）、支付中（STATE_ING）、支付成功（STATE_SUCCESS）、支付失败（STATE_FAIL）和订单关闭（STATE_CLOSED）。状态转换通过`updateIngAndSuccessOrFailByCreatebyOrder`方法实现，该方法在事务中更新订单状态，确保数据一致性。

当支付成功时，`confirmSuccess`方法会被调用，执行以下操作：
1. 更新订单状态为支付成功
2. 处理自动分账逻辑
3. 发送商户通知

```mermaid
stateDiagram-v2
[*] --> STATE_INIT
STATE_INIT --> STATE_ING : 支付中
STATE_ING --> STATE_SUCCESS : 支付成功
STATE_ING --> STATE_FAIL : 支付失败
STATE_ING --> STATE_CLOSED : 订单关闭
STATE_SUCCESS --> [*]
STATE_FAIL --> [*]
STATE_CLOSED --> [*]
```

**Diagram sources**
- [PayOrderProcessService.java](file://sys-payment/src/main/java/com/unipay/pay/service/PayOrderProcessService.java#L30-L44)
- [PayOrder.java](file://core/src/main/java/com/unipay/core/entity/PayOrder.java#L38-L41)

### 异步通知

系统通过消息队列实现异步通知机制。当订单状态变为终态（成功或失败）时，`PayMchNotifyService`会创建通知记录并推送到`PayOrderMchNotifyMQ`消息队列。`PayOrderMchNotifyMQReceiver`消费者从队列中获取通知任务，向商户配置的回调地址发送HTTP请求。

通知机制支持重试策略，初始通知失败后会按30秒、60秒、90秒等递增间隔进行重试，最多重试6次。通知结果会记录在`mch_notify_record`表中，便于后续查询和问题排查。

### 超时处理

系统通过`PayOrderExpiredTask`定时任务处理超时订单。该任务每分钟执行一次，查询创建时间超过设定有效期的未完成订单（状态为STATE_INIT或STATE_ING），并将其状态更新为STATE_CLOSED。

```mermaid
flowchart TD
A[开始] --> B{订单超时?}
B --> |是| C[更新订单状态为关闭]
B --> |否| D[保持原状态]
C --> E[结束]
D --> E
```

**Diagram sources**
- [PayOrderExpiredTask.java](file://sys-payment/src/main/java/com/unipay/pay/task/PayOrderExpiredTask.java#L13-L27)
- [PayOrderService.java](file://service/src/main/java/com/unipay/service/impl/PayOrderService.java#L47-L62)

## API支付请求示例

以下是一个通过API发起支付请求的示例：

```java
// 构建统一下单请求
UnifiedOrderRQ request = new UnifiedOrderRQ();
request.setMchNo("MCH123456"); // 商户号
request.setAppId("APP789012"); // 应用ID
request.setMchOrderNo("ORDER20231201001"); // 商户订单号
request.setWayCode("wxpay_jsapi"); // 支付方式
request.setAmount(100L); // 支付金额，单位：分
request.setCurrency("CNY"); // 货币代码
request.setSubject("商品标题"); // 商品标题
request.setBody("商品描述信息"); // 商品描述
request.setNotifyUrl("https://yourdomain.com/notify"); // 异步通知地址
request.setReturnUrl("https://yourdomain.com/return"); // 跳转通知地址
request.setExpiredTime(300); // 订单失效时间，单位：秒

// 添加签名
String sign = JeepayKit.getSign(JSONObject.toJSON(request), "your_app_secret");
request.setSign(sign);

// 发送HTTP请求
String response = HttpUtil.post("https://payment-gateway.com/api/pay/unifiedOrder", request);
```

响应数据结构如下：

```json
{
  "code": 0,
  "msg": "OK",
  "data": {
    "payOrderId": "PAY202312010001",
    "mchOrderNo": "ORDER20231201001",
    "orderState": 1,
    "payDataType": "JSAPI",
    "payData": "{\"appId\":\"wx123\",\"timeStamp\":\"123456789\",\"nonceStr\":\"abc\",\"package\":\"prepay_id=xyz\",\"signType\":\"MD5\",\"paySign\":\"sign\"}",
    "errCode": null,
    "errMsg": null
  },
  "sign": "response_signature"
}
```

**Section sources**
- [UnifiedOrderRQ.java](file://sys-payment/src/main/java/com/unipay/pay/rqrs/payorder/UnifiedOrderRQ.java#L23-L159)
- [UnifiedOrderRS.java](file://sys-payment/src/main/java/com/unipay/pay/rqrs/payorder/UnifiedOrderRS.java#L13-L52)

## 完整支付流程序列图

以下是用户发起支付到最终结果通知的完整流程：

```mermaid
sequenceDiagram
participant User as "用户"
participant Client as "客户端应用"
participant Gateway as "支付网关"
participant Channel as "支付渠道"
participant Merchant as "商户系统"
User->>Client : 发起支付请求
Client->>Gateway : 调用统一下单接口
Gateway->>Gateway : 参数验证与验签
Gateway->>Gateway : 创建支付订单
Gateway->>Channel : 转发支付请求
Channel-->>Gateway : 返回支付参数
Gateway-->>Client : 返回支付参数
Client->>User : 展示支付界面
User->>Channel : 完成支付操作
Channel->>Channel : 处理支付
Channel-->>Client : 支付结果回调
Client->>Gateway : 查询订单状态
Gateway->>Gateway : 更新订单状态
Gateway->>Merchant : 发送异步通知
Merchant-->>Gateway : 返回通知结果
Gateway->>Client : 返回最终支付结果
Client->>User : 展示支付结果
```

**Diagram sources**
- [UnifiedOrderController.java](file://sys-payment/src/main/java/com/unipay/pay/ctrl/payorder/UnifiedOrderController.java#L24-L91)
- [PayOrderProcessService.java](file://sys-payment/src/main/java/com/unipay/pay/service/PayOrderProcessService.java#L20-L97)
- [PayMchNotifyService.java](file://sys-payment/src/main/java/com/unipay/pay/service/PayMchNotifyService.java#L25-L260)

## 常见错误码解释

| 错误码 | 含义 | 解决方案 |
|-------|------|---------|
| 10001 | 参数不能为空 | 检查请求参数是否完整 |
| 10002 | 签名验证失败 | 检查签名算法和密钥是否正确 |
| 10003 | 商户号不存在 | 确认商户号是否正确注册 |
| 10004 | 应用ID不存在 | 确认应用ID是否正确配置 |
| 10005 | 支付方式不支持 | 检查支付方式代码是否正确 |
| 10006 | 订单已存在 | 使用不同的商户订单号 |
| 10007 | 余额不足 | 充值或选择其他支付方式 |
| 10008 | 订单已关闭 | 重新创建订单 |
| 10009 | 订单已支付 | 查询订单状态获取结果 |
| 10010 | 系统异常 | 联系技术支持 |

**Section sources**
- [ApiCodeEnum.java](file://core/src/main/java/com/unipay/core/constants/ApiCodeEnum.java)
- [BizException.java](file://core/src/main/java/com/unipay/core/exception/BizException.java)

## 签名验证机制

系统采用基于HMAC-SHA256的签名验证机制，确保API请求和响应的完整性和真实性。签名生成规则如下：

1. 将请求参数按参数名ASCII码从小到大排序（字典序）
2. 使用URL键值对的格式（即key1=value1&key2=value2…）拼接成字符串
3. 在字符串末尾拼接商户密钥（appSecret）
4. 使用HMAC-SHA256算法生成签名
5. 将签名转换为十六进制字符串

验证流程：
1. 接收方收到请求后，使用相同的算法重新计算签名
2. 将计算出的签名与请求中的签名进行比对
3. 如果签名一致，则验证通过；否则拒绝请求

对于异步通知，商户系统也需要按照相同的规则验证签名，防止伪造通知。

**Section sources**
- [JeepayKit.java](file://core/src/main/java/com/unipay/core/utils/JeepayKit.java)
- [UnifiedOrderController.java](file://sys-payment/src/main/java/com/unipay/pay/ctrl/payorder/UnifiedOrderController.java#L24-L91)

## 安全最佳实践

为确保支付系统的安全性，建议遵循以下最佳实践：

1. **密钥管理**：商户密钥应妥善保管，避免硬编码在代码中，建议使用环境变量或配置中心管理。
2. **HTTPS传输**：所有API调用必须使用HTTPS协议，防止数据在传输过程中被窃取或篡改。
3. **输入验证**：对所有用户输入进行严格验证，防止SQL注入、XSS等攻击。
4. **限流防护**：对接口进行限流控制，防止恶意用户发起大量请求导致系统瘫痪。
5. **日志审计**：记录关键操作日志，便于问题排查和安全审计。
6. **定期轮换**：定期更换商户密钥，降低密钥泄露风险。
7. **最小权限**：遵循最小权限原则，不同应用使用不同的应用ID和密钥。
8. **监控告警**：建立完善的监控告警系统，及时发现异常交易和安全事件。

**Section sources**
- [JWTUtils.java](file://core/src/main/java/com/unipay/core/jwt/JWTUtils.java)
- [WebSecurityConfig.java](file://sys-merchant/src/main/java/com/unipay/mch/secruity/WebSecurityConfig.java)