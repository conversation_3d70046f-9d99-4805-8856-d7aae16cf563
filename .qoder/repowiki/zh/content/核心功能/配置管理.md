# 配置管理

<cite>
**本文档引用的文件**
- [SysConfig.java](file://core/src/main/java/com/unipay/core/entity/SysConfig.java)
- [SysConfigController.java](file://sys-manager/src/main/java/com/unipay/mgr/ctrl/config/SysConfigController.java)
- [SysConfigService.java](file://service/src/main/java/com/unipay/service/impl/SysConfigService.java)
- [ResetAppConfigMQ.java](file://components/components-mq/src/main/java/com/unipay/components/mq/model/ResetAppConfigMQ.java)
- [ResetAppConfigMQReceiver.java](file://sys-manager/src/main/java/com/unipay/mgr/mq/ResetAppConfigMQReceiver.java)
- [ResetAppConfigMQReceiver.java](file://sys-merchant/src/main/java/com/unipay/mch/mq/ResetAppConfigMQReceiver.java)
- [ResetAppConfigMQReceiver.java](file://sys-payment/src/main/java/com/unipay/pay/mq/ResetAppConfigMQReceiver.java)
- [ResetAppConfigMQReceiver.java](file://sys-agent/src/main/java/com/unipay/agent/mq/ResetAppConfigMQReceiver.java)
- [DBApplicationConfig.java](file://core/src/main/java/com/unipay/core/model/DBApplicationConfig.java)
</cite>

## 目录
1. [引言](#引言)
2. [项目结构](#项目结构)
3. [核心组件](#核心组件)
4. [架构概述](#架构概述)
5. [详细组件分析](#详细组件分析)
6. [依赖分析](#依赖分析)
7. [性能考虑](#性能考虑)
8. [故障排除指南](#故障排除指南)
9. [结论](#结论)

## 引言
本文档详细描述了uni-pay系统的配置管理机制，重点阐述动态配置更新、系统参数管理、配置项分类、数据类型和作用域等核心功能。文档深入分析了SysConfig实体的设计，通过SysConfigController实现的配置增删改查API接口，以及基于消息队列的配置同步机制。同时，文档还涵盖了配置项的最佳实践、版本管理策略、灰度发布方案，以及敏感配置加密存储和访问控制的实现细节。

## 项目结构
配置管理功能分布在多个模块中，主要涉及核心数据模型、服务实现、控制器和消息队列组件。核心配置实体`SysConfig`位于`core`模块，服务实现位于`service`模块，管理接口位于`sys-manager`模块，而配置更新的消息接收器则分布在各个微服务中。

```mermaid
graph TB
subgraph "核心模块"
SysConfig[SysConfig.java]
DBApplicationConfig[DBApplicationConfig.java]
end
subgraph "服务模块"
SysConfigService[SysConfigService.java]
end
subgraph "管理平台"
SysConfigController[SysConfigController.java]
end
subgraph "消息队列"
ResetAppConfigMQ[ResetAppConfigMQ.java]
end
subgraph "微服务实例"
Manager[运营平台]
Merchant[商户系统]
Payment[支付网关]
Agent[代理商系统]
end
SysConfig --> SysConfigService
SysConfigService --> SysConfigController
SysConfigController --> ResetAppConfigMQ
ResetAppConfigMQ --> Manager
ResetAppConfigMQ --> Merchant
ResetAppConfigMQ --> Payment
ResetAppConfigMQ --> Agent
```

**图示来源**
- [SysConfig.java](file://core/src/main/java/com/unipay/core/entity/SysConfig.java)
- [SysConfigService.java](file://service/src/main/java/com/unipay/service/impl/SysConfigService.java)
- [SysConfigController.java](file://sys-manager/src/main/java/com/unipay/mgr/ctrl/config/SysConfigController.java)
- [ResetAppConfigMQ.java](file://components/components-mq/src/main/java/com/unipay/components/mq/model/ResetAppConfigMQ.java)

**本节来源**
- [SysConfig.java](file://core/src/main/java/com/unipay/core/entity/SysConfig.java)
- [SysConfigService.java](file://service/src/main/java/com/unipay/service/impl/SysConfigService.java)
- [SysConfigController.java](file://sys-manager/src/main/java/com/unipay/mgr/ctrl/config/SysConfigController.java)
- [ResetAppConfigMQ.java](file://components/components-mq/src/main/java/com/unipay/components/mq/model/ResetAppConfigMQ.java)

## 核心组件
配置管理的核心组件包括`SysConfig`实体、`SysConfigService`服务、`SysConfigController`控制器和`ResetAppConfigMQ`消息模型。`SysConfig`实体定义了配置项的数据结构，`SysConfigService`提供了配置的持久化和缓存管理，`SysConfigController`暴露了RESTful API供前端调用，而`ResetAppConfigMQ`则实现了配置变更的广播通知机制。

**本节来源**
- [SysConfig.java](file://core/src/main/java/com/unipay/core/entity/SysConfig.java)
- [SysConfigService.java](file://service/src/main/java/com/unipay/service/impl/SysConfigService.java)
- [SysConfigController.java](file://sys-manager/src/main/java/com/unipay/mgr/ctrl/config/SysConfigController.java)
- [ResetAppConfigMQ.java](file://components/components-mq/src/main/java/com/unipay/components/mq/model/ResetAppConfigMQ.java)

## 架构概述
系统采用基于消息队列的发布-订阅模式来实现配置的动态更新。当运营人员通过管理后台修改配置时，`SysConfigController`会将变更通过`ResetAppConfigMQ`消息发送到消息中间件。所有注册了`ResetAppConfigMQ.IMQReceiver`接口的微服务实例（如商户系统、支付网关、代理商系统）都会接收到该广播消息，并触发本地缓存的刷新。

```mermaid
sequenceDiagram
participant "运营平台" as Manager
participant "消息中间件" as MQ
participant "商户系统" as Merchant
participant "支付网关" as Payment
participant "代理商系统" as Agent
Manager->>MQ : 发送ResetAppConfigMQ消息
MQ-->>Merchant : 广播消息
MQ-->>Payment : 广播消息
MQ-->>Agent : 广播消息
Merchant->>Merchant : 调用initDBConfig()刷新缓存
Payment->>Payment : 调用initDBConfig()刷新缓存
Agent->>Agent : 调用initDBConfig()刷新缓存
Note over Merchant,Agent : 所有实例并行刷新本地缓存
```

**图示来源**
- [SysConfigController.java](file://sys-manager/src/main/java/com/unipay/mgr/ctrl/config/SysConfigController.java#L103-L107)
- [ResetAppConfigMQ.java](file://components/components-mq/src/main/java/com/unipay/components/mq/model/ResetAppConfigMQ.java#L16-L67)
- [ResetAppConfigMQReceiver.java](file://sys-manager/src/main/java/com/unipay/mgr/mq/ResetAppConfigMQReceiver.java#L22-L28)

## 详细组件分析

### SysConfig实体分析
`SysConfig`实体是配置管理的数据模型，它定义了配置项的所有属性，包括配置键、名称、描述、分组、类型和值等。

```mermaid
classDiagram
class SysConfig {
+String configKey
+String configName
+String configDesc
+String groupKey
+String groupName
+String configVal
+String type
+Long sortNum
+Date updatedAt
+static LambdaQueryWrapper<SysConfig> gw()
}
SysConfig : <<Entity>>
SysConfig : @TableName("t_sys_config")
```

**图示来源**
- [SysConfig.java](file://core/src/main/java/com/unipay/core/entity/SysConfig.java#L24-L94)

**本节来源**
- [SysConfig.java](file://core/src/main/java/com/unipay/core/entity/SysConfig.java#L24-L94)

### SysConfigController API分析
`SysConfigController`提供了两个核心API：`getConfigs`用于查询分组下的配置，`update`用于修改配置。

#### 查询配置API
```mermaid
sequenceDiagram
participant "前端" as Frontend
participant "控制器" as Controller
participant "服务层" as Service
participant "数据库" as DB
Frontend->>Controller : GET /api/sysConfigs/{groupKey}
Controller->>Service : sysConfigService.list(condition)
Service->>DB : 执行SQL查询
DB-->>Service : 返回配置列表
Service-->>Controller : 返回List<SysConfig>
Controller-->>Frontend : 返回ApiRes<List<SysConfig>>
```

**图示来源**
- [SysConfigController.java](file://sys-manager/src/main/java/com/unipay/mgr/ctrl/config/SysConfigController.java#L53-L69)
- [SysConfigService.java](file://service/src/main/java/com/unipay/service/impl/SysConfigService.java#L84-L88)

#### 更新配置API
```mermaid
flowchart TD
Start([接收到PUT请求]) --> ParseJSON["解析请求JSON参数"]
ParseJSON --> UpdateDB["调用sysConfigService.updateByConfigKey()"]
UpdateDB --> CheckResult{"更新成功?"}
CheckResult --> |否| ReturnFail["返回更新失败"]
CheckResult --> |是| SendMQ["异步发送ResetAppConfigMQ消息"]
SendMQ --> ReturnSuccess["返回更新成功"]
ReturnFail --> End([API响应])
ReturnSuccess --> End
```

**图示来源**
- [SysConfigController.java](file://sys-manager/src/main/java/com/unipay/mgr/ctrl/config/SysConfigController.java#L76-L101)
- [SysConfigService.java](file://service/src/main/java/com/unipay/service/impl/SysConfigService.java#L78-L91)

**本节来源**
- [SysConfigController.java](file://sys-manager/src/main/java/com/unipay/mgr/ctrl/config/SysConfigController.java#L53-L101)
- [SysConfigService.java](file://service/src/main/java/com/unipay/service/impl/SysConfigService.java#L78-L91)

### 配置同步机制分析
配置同步机制的核心是`ResetAppConfigMQ`消息模型和其接收器`ResetAppConfigMQReceiver`。

```mermaid
classDiagram
class ResetAppConfigMQ {
+String MQ_NAME
+MsgPayload payload
+getMQName() String
+getMQType() MQSendTypeEnum
+toMessage() String
+build(groupKey) ResetAppConfigMQ
+parse(msg) MsgPayload
}
class ResetAppConfigMQ$MsgPayload {
+String groupKey
}
class ResetAppConfigMQ$IMQReceiver {
+receive(payload) void
}
class ResetAppConfigMQReceiver {
-SysConfigService sysConfigService
+receive(payload) void
}
ResetAppConfigMQ --> ResetAppConfigMQ$MsgPayload : 包含
ResetAppConfigMQ <|-- ResetAppConfigMQ$IMQReceiver : 实现
ResetAppConfigMQ$IMQReceiver <|.. ResetAppConfigMQReceiver : 实现
ResetAppConfigMQReceiver --> SysConfigService : 依赖
```

**图示来源**
- [ResetAppConfigMQ.java](file://components/components-mq/src/main/java/com/unipay/components/mq/model/ResetAppConfigMQ.java#L16-L67)
- [ResetAppConfigMQReceiver.java](file://sys-manager/src/main/java/com/unipay/mgr/mq/ResetAppConfigMQReceiver.java#L15-L29)

**本节来源**
- [ResetAppConfigMQ.java](file://components/components-mq/src/main/java/com/unipay/components/mq/model/ResetAppConfigMQ.java#L16-L67)
- [ResetAppConfigMQReceiver.java](file://sys-manager/src/main/java/com/unipay/mgr/mq/ResetAppConfigMQReceiver.java#L15-L29)

## 依赖分析
配置管理模块依赖于核心模块（`core`）提供实体和模型，依赖于服务模块（`service`）提供数据访问能力，依赖于消息队列组件（`components-mq`）实现跨服务通信。各个微服务通过实现`ResetAppConfigMQ.IMQReceiver`接口来依赖配置更新消息。

```mermaid
graph TD
SysConfigController --> SysConfigService
SysConfigController --> IMQSender
SysConfigService --> SysConfigMapper
SysConfigService --> DBApplicationConfig
ResetAppConfigMQReceiver --> SysConfigService
SysConfig --> BaseModel
SysConfig --> Serializable
```

**图示来源**
- [SysConfigController.java](file://sys-manager/src/main/java/com/unipay/mgr/ctrl/config/SysConfigController.java#L29-L33)
- [SysConfigService.java](file://service/src/main/java/com/unipay/service/impl/SysConfigService.java#L24-L30)
- [ResetAppConfigMQReceiver.java](file://sys-manager/src/main/java/com/unipay/mgr/mq/ResetAppConfigMQReceiver.java#L19-L20)

**本节来源**
- [SysConfigController.java](file://sys-manager/src/main/java/com/unipay/mgr/ctrl/config/SysConfigController.java#L29-L33)
- [SysConfigService.java](file://service/src/main/java/com/unipay/service/impl/SysConfigService.java#L24-L30)
- [ResetAppConfigMQReceiver.java](file://sys-manager/src/main/java/com/unipay/mgr/mq/ResetAppConfigMQReceiver.java#L19-L20)

## 性能考虑
系统通过内存缓存（`IS_USE_CACHE`）来提升配置读取性能。当`IS_USE_CACHE`为`true`时，配置数据会被缓存在静态变量`APPLICATION_CONFIG`中，避免了频繁的数据库查询。配置更新通过消息队列广播，确保了所有实例的配置一致性，但需要注意消息队列的可靠性和性能。

## 故障排除指南
当配置更新后，部分实例未生效时，应检查以下几点：
1. 确认消息队列服务是否正常运行。
2. 检查`ResetAppConfigMQReceiver`是否正确注册为Spring Bean。
3. 验证`sysConfigService.initDBConfig()`方法是否被成功调用。
4. 确认`IS_USE_CACHE`配置项是否为`true`。

**本节来源**
- [ResetAppConfigMQReceiver.java](file://sys-manager/src/main/java/com/unipay/mgr/mq/ResetAppConfigMQReceiver.java#L22-L28)
- [SysConfigService.java](file://service/src/main/java/com/unipay/service/impl/SysConfigService.java#L39-L49)

## 结论
uni-pay系统的配置管理机制设计合理，通过`SysConfig`实体统一管理所有配置项，通过RESTful API提供灵活的配置操作接口，并通过消息队列实现配置的实时同步。该机制支持系统的动态扩展和高可用性，为系统的稳定运行提供了有力保障。