
# 核心功能

<cite>
**本文档引用的文件**   
- [PayOrderService.java](file://service/src/main/java/com/unipay/service/impl/PayOrderService.java)
- [RefundOrderService.java](file://service/src/main/java/com/unipay/service/impl/RefundOrderService.java)
- [PayOrderDivisionProcessService.java](file://sys-payment/src/main/java/com/unipay/pay/service/PayOrderDivisionProcessService.java)
- [AgentInfoService.java](file://service/src/main/java/com/unipay/service/impl/AgentInfoService.java)
- [SysUserService.java](file://service/src/main/java/com/unipay/service/impl/SysUserService.java)
- [SysRoleService.java](file://service/src/main/java/com/unipay/service/impl/SysRoleService.java)
- [SysConfigService.java](file://service/src/main/java/com/unipay/service/impl/SysConfigService.java)
- [PayOrder.java](file://core/src/main/java/com/unipay/core/entity/PayOrder.java)
- [RefundOrder.java](file://core/src/main/java/com/unipay/core/entity/RefundOrder.java)
- [MchDivisionReceiver.java](file://core/src/main/java/com/unipay/core/entity/MchDivisionReceiver.java)
- [AgentInfo.java](file://core/src/main/java/com/unipay/core/entity/AgentInfo.java)
- [SysUser.java](file://core/src/main/java/com/unipay/core/entity/SysUser.java)
- [SysRole.java](file://core/src/main/java/com/unipay/core/entity/SysRole.java)
- [SysConfig.java](file://core/src/main/java/com/unipay/core/entity/SysConfig.java)
- [PayOrderDivisionRecord.java](file://core/src/main/java/com/unipay/core/entity/PayOrderDivisionRecord.java)
- [AgentProfitRecord.java](file://core/src/main/java/com/unipay/core/entity/AgentProfitRecord.java)
- [PayOrderDivisionMQ.java](file://components/components-mq/src/main/java/com/unipay/components/mq/model/PayOrderDivisionMQ.java)
- [PayOrderDivisionMQReceiver.java](file://sys-payment/src/main/java/com/unipay/pay/mq/PayOrderDivisionMQReceiver.java)
- [PayOrderProcessService.java](file://sys-payment/src/main/java/com/unipay/pay/service/PayOrderProcessService.java)
- [RefundOrderProcessService.java](file://sys-payment/src/main/java/com/unipay/pay/service/RefundOrderProcessService.java)
- [ConfigContextService.java](file://sys-payment/src/main/java/com/unipay/pay/service/ConfigContextService.java)
</cite>

## 目录
1. [支付服务](#支付服务)
2. [分账功能](#分账功能)
3. [多级代理体系](#多级代理体系)
4. [配置管理](#配置管理)
5. [用户管理](#用户管理)

## 支付服务

支付服务是系统的核心功能，提供统一下单、订单查询和退款三大核心能力，支撑商户的日常交易需求。

### 统一下单

统一下单功能允许商户通过统一接口发起支付请求，系统根据商户配置自动路由到合适的支付渠道。该功能的业务价值在于简化商户接入流程，支持多种支付方式（如微信、支付宝等），并确保交易数据的一致性。

**关键服务类**：
- `PayOrderService`：处理支付订单的核心服务类，负责订单的创建、状态更新和查询。
- `PayOrderProcessService`：处理支付订单的业务流程，包括订单状态转换和自动分账触发。

```mermaid
flowchart TD
A[商户发起支付请求] --> B{验证请求参数}
B --> |验证失败| C[返回错误信息]
B --> |验证成功| D[创建支付订单]
D --> E[调用支付渠道接口]
E --> F{渠道返回结果}
F --> |支付成功| G[更新订单状态为成功]
F --> |支付失败| H[更新订单状态为失败]
G --> I[触发自动分账逻辑]
H --> J[结束]
I --> K[发送商户通知]
K --> L[结束]
```

**流程说明**：
1. 商户通过API发起支付请求，包含订单金额、商品信息等必要参数。
2. 系统验证请求参数的完整性和合法性。
3. 验证通过后，创建支付订单并持久化到数据库。
4. 根据商户配置的支付通道，调用相应的支付渠道接口。
5. 根据渠道返回的结果，更新订单状态。
6. 若支付成功，触发自动分账逻辑。
7. 向商户发送支付结果通知。

**入口点**：
- `PayOrderService` 类中的 `updateInit2Ing` 方法用于更新订单状态为"支付中"。
- `PayOrderProcessService` 类中的 `updateIngAndSuccessOrFailByCreatebyOrder` 方法处理支付中到成功或失败的状态转换。

**Section sources**
- [PayOrderService.java](file://service/src/main/java/com/unipay/service/impl/PayOrderService.java#L36-L640)
- [PayOrderProcessService.java](file://sys-payment/src/main/java/com/unipay/pay/service/PayOrderProcessService.java#L20-L97)

### 订单查询

订单查询功能允许商户根据订单号查询支付状态，确保交易的可追溯性。该功能的业务价值在于提供实时的订单状态反馈，帮助商户及时处理异常订单。

**关键服务类**：
- `PayOrderService`：提供订单查询接口，支持按商户订单号或系统订单号查询。

**入口点**：
- `PayOrderService` 类中的 `queryMchOrder` 方法用于查询商户订单。

**Section sources**
- [PayOrderService.java](file://service/src/main/java/com/unipay/service/impl/PayOrderService.java#L120-L135)

### 退款

退款功能允许商户对已支付的订单发起退款操作，支持部分退款和全额退款。该功能的业务价值在于满足商户的售后需求，提升用户体验。

**关键服务类**：
- `RefundOrderService`：处理退款订单的核心服务类，负责退款订单的创建、状态更新和查询。
- `RefundOrderProcessService`：处理退款订单的业务流程，包括调用渠道退款接口和更新订单状态。

```mermaid
sequenceDiagram
participant 商户 as 商户系统
participant 支付平台 as 支付平台
participant 支付渠道 as 支付渠道
商户->>支付平台 : 发起退款请求
支付平台->>支付平台 : 验证退款参数
支付平台->>支付渠道 : 调用退款接口
支付渠道-->>支付平台 : 返回退款结果
支付平台->>支付平台 : 更新退款订单状态
支付平台->>支付平台 : 更新支付订单退款信息
支付平台->>商户 : 返回退款结果
```

**流程说明**：
1. 商户发起退款请求，包含退款金额、原因等信息。
2. 系统验证退款参数的合法性。
3. 调用支付渠道的退款接口。
4. 根据渠道返回的结果，更新退款订单状态。
5. 更新原支付订单的退款信息（退款次数、退款金额等）。
6. 向商户返回退款结果。

**入口点**：
- `RefundOrderService` 类中的 `updateInit2Ing` 方法用于更新退款订单状态为"退款中"。
- `RefundOrderProcessService` 类中的 `handleRefundOrder4Channel` 方法处理渠道返回的退款结果。

**Section sources**
- [RefundOrderService.java](file://service/src/main/java/com/unipay/service/impl/RefundOrderService.java#L28-L177)
- [RefundOrderProcessService.java](file://sys-payment/src/main/java/com/unipay/pay/service/RefundOrderProcessService.java#L15-L47)

## 分账功能

分账功能支持自动分账和手动分账两种模式，满足不同商户的分账需求。该功能的业务价值在于实现资金的自动化分配，提高资金流转效率。

### 自动分账

自动分账在支付成功后自动触发，根据预设的分账规则将资金分配给指定的接收方。该功能适用于有固定分账比例的场景，如平台与商户的分成。

**关键服务类**：
- `PayOrderDivisionProcessService`：处理分账业务的核心服务类，负责分账逻辑的执行。
- `PayOrderDivisionRecordService`：处理分账记录的持久化和状态更新。

```mermaid
flowchart TD
A[支付成功] --> B{订单分账模式}
B --> |自动分账| C[触发分账MQ]
C --> D[分账服务处理]
D --> E[查询分账接收方]
E --> F[计算分账金额]
F --> G[调用渠道分账接口]
G --> H{分账结果}
H --> |成功| I[更新分账记录为成功]
H --> |失败| J[更新分账记录为失败]
I --> K[结束]
J --> K
```

**流程说明**：
1. 支付成功后，检查订单的分账模式。
2. 若为自动分账模式，发送分账消息到消息队列。
3. 分账服务消费消息，开始处理分账逻辑。
4. 查询该订单对应的分账接收方列表。
5. 根据分账比例计算各接收方的分账金额。
6. 调用支付渠道的分账接口。
7. 根据渠道返回的结果，更新分账记录状态。

**入口点**：
- `PayOrderDivisionProcessService` 类中的 `processPayOrderDivision` 方法是分账处理的入口。
- `PayOrderProcessService` 类中的 `confirmSuccess` 方法在支付成功时触发自动分账。

**Section sources**
- [PayOrderDivisionProcessService.java](file://sys-payment/src/main/java/com/unipay/pay/service/PayOrderDivisionProcessService.java#L35-L298)
- [PayOrderProcessService.java](file://sys-payment/src/main/java/com/unipay/pay/service/PayOrderProcessService.java#L20-L97)

### 手动分账

手动分账由商户主动发起，允许商户在支付成功后手动触发分账操作。该功能适用于分账规则不固定的场景，如临时的奖金分配。

**关键服务类**：
- `PayOrderDivisionProcessService`：同样用于处理手动分账请求，通过MQ消息触发。
- `PayOrderDivisionRecordService`：支持重新发送分账请求，处理手动分账的重试逻辑。

**入口点**：
- `PayOrderDivisionRecordService` 类中的 `updateResendState` 方法用于重置分账状态，支持手动重新分账。

**Section sources**
- [PayOrderDivisionRecordService.java](file://service/src/main/java/com/unipay/service/impl/PayOrderDivisionRecordService.java#L24-L88)

## 多级代理体系

多级代理体系支持代理商的层级管理、分润计算和商户发展，构建完整的代理网络。该功能的业务价值在于拓展商户来源，实现渠道的层级化管理。

### 代理商管理

代理商管理功能支持代理商的创建、查询和层级关系维护。系统通过`agentPath`字段记录代理商的层级路径，支持快速查询下级代理商。

**关键服务类**：
- `AgentInfoService`：处理代理商信息的核心服务类，负责代理商的增删改查和层级关系维护。

```mermaid
flowchart TD
A[创建代理商] --> B[生成代理商号]
B --> C[构建代理商路径]
C --> D[设置代理商层级]
D --> E[创建系统用户]
E --> F[保存代理商信息]
```

**流程说明**：
1. 创建代理商时，自动生成唯一的代理商号。
2. 根据上级代理商号构建代理商路径（如/A001/A002）。
3. 计算代理商层级（一级、二级等）。
4. 创建关联的系统用户，用于代理商登录。
5. 保存代理商信息到数据库。

**入口点**：
- `AgentInfoService` 类中的 `createAgentWithUser` 方法是创建代理商的入口。

**Section sources**
- [AgentInfoService.java](file://service/src/main/java/com/unipay/service/impl/AgentInfoService.java#L38-L389)

### 分润计算

分润计算功能根据代理商的分润比例和商户的交易手续费，自动计算代理商的分润金额。分润记录与支付订单关联，确保分润的准确性和可追溯性。

**关键服务类**：
- `AgentProfitRecordService`：处理分润记录的创建、查询和结算。
- `AgentInfoService`：提供代理商信息查询，用于获取分润比例。

```mermaid
flowchart TD
A[支付成功] --> B[查询关联代理商]
B --> C[获取分润比例]
C --> D[计算分润金额]
D --> E[创建分润记录]
E --> F[状态: 待结算]
```

**流程说明**：
1. 支付成功后，查询该商户关联的代理商。
2. 获取代理商的分润比例。
3. 基于商户手续费计算分润金额。
4. 创建分润记录，状态为"待结算"。
5. 分润记录与支付订单关联，便于后续查询和结算。

**入口点**：
- `AgentProfitRecordService` 类中的 `createProfitRecord` 方法用于创建分润记录。

**Section sources**
- [AgentProfitRecordService.java](file://service/src/main/java/com/unipay/service/impl/AgentProfitRecordService.java#L24-L167)

## 配置管理

配置管理功能支持动态参数的配置和实时生效，无需重启服务即可更新系统配置。该功能的业务价值在于提高系统的灵活性和可维护性。

### 动态参数

动态参数功能通过消息队列实现配置的实时同步。当配置更新时，系统发送广播消息通知所有相关服务节点更新本地缓存。

**关键服务类**：
- `SysConfigService`：处理系统配置的增删改查和缓存管理。
- `ConfigContextService`：管理商户配置上下文的缓存，支持快速查询。

```mermaid
flowchart TD
A[更新系统配置] --> B[保存到数据库]
B --> C[发送配置更新MQ]
C --> D[所有服务节点接收MQ]
D --> E[更新本地缓存]
E --> F[配置实时生效]
```

**流程说明**：
1. 管理员在后台更新系统配置。
2. 配置信息保存到数据库。
3. 发送配置更新消息到消息队列（广播模式）。
4. 所有相关服务节点（如支付服务、商户服务等）接收消息。
5. 各节点更新本地缓存中的配置信息。
6. 新配置实时生效，无需重启服务。

**入口点**：
- `SysConfigService` 类中的 `initDBConfig` 方法用于初始化数据库配置缓存。
- `ConfigContextService` 类中的 `getMchAppConfigContext` 方法用于获取商户应用配置上下文。

**Section sources**
- [SysConfigService.java](file://service/src/main/java/com/unipay/service/impl/SysConfigService.java#L24-L92)
- [ConfigContextService.java](file://sys-payment/src/main/java/com/unipay/pay/service/ConfigContextService.java#L32-L314)

## 用户管理

用户管理功能基于RBAC（基于角色的访问控制）模型实现权限控制，确保系统的安全性。该功能的业务价值在于实现精细化的权限管理，满足不同角色的访问需求。

### RBAC权限控制

RBAC权限控制通过用户、角色和权限的关联关系实现。用户通过角色获得权限，支持灵活的权限分配和管理。

**关键服务类**：
- `SysUserService`：处理系统用户的核心服务类，负责用户的增删改查和角色分配。
- `SysRoleService`：处理角色管理，支持角色的创建、删除和权限分配。
- `SysRoleEntRelaService`：处理角色与权限的关联关系。

```mermaid
erDiagram
  SYS_USER ||--o{ SYS_USER_ROLE_RELA : "拥有"
  SYS_ROLE ||--o{ SYS_USER_ROLE_RELA : "被分配"
  SYS_ROLE ||--o{ SYS_ROLE_ENT_RELA : "包含"
  SYS_ENTITLEMENT ||--o{ SYS_ROLE_ENT_RELA : "被包含"

  SYS_USER {
    Long sysUserId PK
    String loginUsername
    String realname
    String telphone
    Byte isAdmin
    String sysType
  }

  SYS_ROLE {
    String roleId PK
    String roleName
    String sysType
  }

  SYS_ENTITLEMENT {
    String entId PK
    String entName
    String menuUri
    String entType
  }

  SYS_USER_ROLE_RELA {
    Long userId PK, FK
    String roleId PK, FK
  }

  SYS_ROLE_ENT_RELA {
    String roleId PK, FK
   