# 开发指南

<cite>
**本文档中引用的文件**   
- [快速开始指南.md](file://代码示例/快速开始指南.md)
- [支付流程实现教程.md](file://代码示例/支付流程实现教程.md)
- [1-支付请求实现.java](file://代码示例/1-支付请求实现.java)
- [2-订单创建实现.java](file://代码示例/2-订单创建实现.java)
- [3-支付回调处理.java](file://代码示例/3-支付回调处理.java)
- [4-签名验证工具类.java](file://代码示例/4-签名验证工具类.java)
- [5-配置文件和实体类.java](file://代码示例/5-配置文件和实体类.java)
- [GameServerApplication.java](file://game-server/src/main/java/com/game/GameServerApplication.java)
- [application.yml](file://game-server/src/main/resources/application.yml)
- [PaymentConfig.java](file://game-server/src/main/java/com/game/config/PaymentConfig.java)
- [RechargeController.java](file://game-server/src/main/java/com/game/controller/RechargeController.java)
- [PaymentCallbackController.java](file://game-server/src/main/java/com/game/controller/PaymentCallbackController.java)
- [RechargeService.java](file://game-server/src/main/java/com/game/service/RechargeService.java)
- [PaymentService.java](file://game-server/src/main/java/com/game/service/PaymentService.java)
- [PaymentUtil.java](file://game-server/src/main/java/com/game/util/PaymentUtil.java)
- [GameUser.java](file://game-server/src/main/java/com/game/entity/GameUser.java)
- [RechargeOrder.java](file://game-server/src/main/java/com/game/entity/RechargeOrder.java)
</cite>

## 目录
1. [本地开发环境搭建](#本地开发环境搭建)
2. [IDE配置](#ide配置)
3. [依赖安装](#依赖安装)
4. [数据库连接与配置](#数据库连接与配置)
5. [配置文件设置](#配置文件设置)
6. [支付请求实现](#支付请求实现)
7. [订单创建实现](#订单创建实现)
8. [支付回调处理](#支付回调处理)
9. [代码规范](#代码规范)
10. [调试技巧](#调试技巧)
11. [单元测试](#单元测试)
12. [代码贡献流程](#代码贡献流程)
13. [代码审查标准](#代码审查标准)

## 本地开发环境搭建

本节将指导新加入的开发者完成本地开发环境的搭建，确保能够顺利进行开发工作。

**本节引用文件**
- [快速开始指南.md](file://代码示例/快速开始指南.md)
- [GameServerApplication.java](file://game-server/src/main/java/com/game/GameServerApplication.java)

## IDE配置

为确保开发环境的一致性，建议使用IntelliJ IDEA作为开发IDE。配置步骤如下：

1. 打开IntelliJ IDEA，导入项目
2. 配置JDK版本为17+
3. 安装Lombok插件
4. 配置Maven路径

**本节引用文件**
- [快速开始指南.md](file://代码示例/快速开始指南.md)

## 依赖安装

项目使用Maven进行依赖管理，主要依赖包括：

- Spring Boot Web
- Spring Boot JPA
- Thymeleaf模板引擎
- H2内存数据库
- FastJSON
- Apache HttpClient
- Apache Commons Codec
- Lombok

通过执行`mvn clean install`命令安装所有依赖。

**本节引用文件**
- [快速开始指南.md](file://代码示例/快速开始指南.md)

## 数据库连接与配置

项目使用H2内存数据库进行开发和测试，配置如下：

```yaml
spring:
  datasource:
    url: jdbc:h2:mem:gamedb
    driver-class-name: org.h2.Driver
    username: sa
    password: 
  
  jpa:
    hibernate:
      ddl-auto: create-drop
    show-sql: true
```

H2控制台可通过`http://localhost:8088/h2-console`访问。

**本节引用文件**
- [application.yml](file://game-server/src/main/resources/application.yml)

## 配置文件设置

主要配置文件包括：

1. `application.yml`: 应用配置
2. `PaymentConfig.java`: 支付网关配置
3. `GameConfig.java`: 游戏相关配置

关键配置项：
- 支付网关地址
- 商户号
- 应用ID
- 应用密钥
- 回调地址
- 游戏币兑换比例

**本节引用文件**
- [application.yml](file://game-server/src/main/resources/application.yml)
- [PaymentConfig.java](file://game-server/src/main/java/com/game/config/PaymentConfig.java)

## 支付请求实现

支付请求的实现主要在`RechargeController`中完成，核心方法为`createRecharge`。

```mermaid
sequenceDiagram
participant 前端 as 前端
participant 控制器 as RechargeController
participant 服务层 as RechargeService
前端->>控制器 : POST /recharge/create
控制器->>控制器 : 参数验证
控制器->>服务层 : createRechargeOrder()
服务层-->>控制器 : 订单信息
控制器-->>前端 : 返回订单信息和二维码URL
```

**本节引用文件**
- [1-支付请求实现.java](file://代码示例/1-支付请求实现.java)
- [RechargeController.java](file://game-server/src/main/java/com/game/controller/RechargeController.java)

## 订单创建实现

订单创建的实现主要在`RechargeService`中完成，核心方法为`createRechargeOrder`。

```mermaid
flowchart TD
Start([开始]) --> ValidateInput["验证输入参数"]
ValidateInput --> GetUser["获取或创建用户"]
GetUser --> CalculateCoin["计算游戏币数量"]
CalculateCoin --> CreateOrder["创建充值订单"]
CreateOrder --> SaveOrder["保存订单到数据库"]
SaveOrder --> CallPayment["调用支付网关创建支付订单"]
CallPayment --> CheckResult{"创建成功?"}
CheckResult --> |是| UpdateOrder["更新订单支付ID"]
CheckResult --> |否| UpdateStatus["更新订单状态为失败"]
UpdateOrder --> ReturnResult["返回订单信息"]
UpdateStatus --> ReturnResult
ReturnResult --> End([结束])
```

**本节引用文件**
- [2-订单创建实现.java](file://代码示例/2-订单创建实现.java)
- [RechargeService.java](file://game-server/src/main/java/com/game/service/RechargeService.java)
- [PaymentService.java](file://game-server/src/main/java/com/game/service/PaymentService.java)

## 支付回调处理

支付回调处理的实现主要在`PaymentCallbackController`中完成，核心方法为`paymentNotify`。

```mermaid
sequenceDiagram
participant 支付网关 as 支付网关
participant 回调控制器 as PaymentCallbackController
participant 服务层 as RechargeService
支付网关->>回调控制器 : POST /api/payment/notify
回调控制器->>回调控制器 : 解析回调数据
回调控制器->>回调控制器 : 验证签名
回调控制器->>服务层 : handlePaymentCallback()
服务层->>服务层 : 查找充值订单
服务层->>服务层 : 检查订单状态
服务层->>服务层 : 处理支付成功/失败
服务层-->>回调控制器 : 处理结果
回调控制器-->>支付网关 : success/fail
```

**本节引用文件**
- [3-支付回调处理.java](file://代码示例/3-支付回调处理.java)
- [PaymentCallbackController.java](file://game-server/src/main/java/com/game/controller/PaymentCallbackController.java)

## 代码规范

为确保代码质量，所有开发者必须遵守以下代码规范：

1. 使用Lombok注解减少样板代码
2. 使用`@Slf4j`注解进行日志记录
3. 使用`@Transactional`注解管理事务
4. 使用`@Data`注解生成getter/setter方法
5. 使用`@Valid`注解进行参数验证
6. 使用`@PrePersist`和`@PreUpdate`注解管理实体生命周期

**本节引用文件**
- [1-支付请求实现.java](file://代码示例/1-支付请求实现.java)
- [2-订单创建实现.java](file://代码示例/2-订单创建实现.java)
- [3-支付回调处理.java](file://代码示例/3-支付回调处理.java)

## 调试技巧

为提高开发效率，建议使用以下调试技巧：

1. 启用详细日志
```yaml
logging:
  level:
    com.game: DEBUG
    org.springframework.web: DEBUG
```

2. 在关键位置添加日志
```java
log.info("用户 {} 创建充值订单成功，金额: {}", username, amount);
```

3. 使用H2控制台查看数据库数据
4. 使用Postman测试API接口
5. 使用`test-notify`接口模拟支付回调

**本节引用文件**
- [application.yml](file://game-server/src/main/resources/application.yml)
- [3-支付回调处理.java](file://代码示例/3-支付回调处理.java)

## 单元测试

为确保代码质量，所有核心功能必须有相应的单元测试。测试重点包括：

1. 支付请求参数验证
2. 订单创建逻辑
3. 支付回调处理
4. 签名验证
5. 数据库操作

使用`@SpringBootTest`注解进行集成测试，使用`@MockBean`注解模拟外部依赖。

**本节引用文件**
- [2-订单创建实现.java](file://代码示例/2-订单创建实现.java)
- [3-支付回调处理.java](file://代码示例/3-支付回调处理.java)

## 代码贡献流程

新功能开发和bug修复的代码贡献流程如下：

1. 从`main`分支创建新分支
2. 在新分支上进行开发
3. 提交代码并推送至远程仓库
4. 创建Pull Request
5. 等待代码审查
6. 根据审查意见修改代码
7. 合并到`main`分支

**本节引用文件**
- [快速开始指南.md](file://代码示例/快速开始指南.md)

## 代码审查标准

代码审查时将重点关注以下方面：

1. 代码是否符合编码规范
2. 是否有适当的日志记录
3. 是否有充分的异常处理
4. 是否有必要的参数验证
5. 事务管理是否正确
6. 数据库操作是否高效
7. 是否有安全漏洞
8. 是否有性能问题
9. 是否有重复代码
10. 是否有适当的单元测试

**本节引用文件**
- [1-支付请求实现.java](file://代码示例/1-支付请求实现.java)
- [2-订单创建实现.java](file://代码示例/2-订单创建实现.java)
- [3-支付回调处理.java](file://代码示例/3-支付回调处理.java)