# 统一支付应用重构部署指南

## 部署概述

本文档描述了统一支付应用重构功能的部署步骤和注意事项。

## 部署前准备

### 1. 代码变更确认
确认以下文件已正确修改：
- `core/src/main/java/com/unipay/core/entity/UnifiedPayApp.java` - 新增
- `core/src/main/java/com/unipay/core/entity/MchIncomeRecord.java` - 新增
- `core/src/main/java/com/unipay/core/entity/MchInfo.java` - 修改
- `service/src/main/java/com/unipay/service/impl/UnifiedPayAppService.java` - 新增
- `service/src/main/java/com/unipay/service/impl/MchIncomeRecordService.java` - 新增
- `service/src/main/java/com/unipay/service/impl/MchInfoService.java` - 修改
- `sys-payment/src/main/java/com/unipay/pay/service/ConfigContextQueryService.java` - 修改
- `sys-payment/src/main/java/com/unipay/pay/service/PayOrderProcessService.java` - 修改
- `sys-manager/src/main/java/com/unipay/mgr/ctrl/unifiedapp/UnifiedPayAppController.java` - 新增

### 2. 数据库备份
在执行迁移前，务必备份以下表：
```bash
mysqldump -u username -p database_name t_mch_info t_mch_app > backup_before_unified_pay_migration.sql
```

### 3. 环境检查
- 确认所有服务实例已停止
- 确认数据库连接正常
- 确认有足够的磁盘空间

## 部署步骤

### 第一步：数据库迁移

1. 执行迁移脚本：
```bash
mysql -u username -p database_name < z-docs/sql/unified_pay_migration.sql
```

2. 验证表结构：
```sql
-- 检查统一应用表
DESCRIBE t_unified_pay_app;

-- 检查商户表新增字段
DESCRIBE t_mch_info;

-- 检查收益记录表
DESCRIBE t_mch_income_record;

-- 验证默认应用是否插入
SELECT * FROM t_unified_pay_app WHERE is_default = 1;
```

3. 验证数据迁移：
```sql
-- 检查商户是否绑定了统一应用
SELECT mch_no, unified_app_id, fee_rate FROM t_mch_info LIMIT 10;
```

### 第二步：代码部署

1. 编译项目：
```bash
mvn clean compile -DskipTests
```

2. 打包项目：
```bash
mvn clean package -DskipTests
```

3. 部署各个服务：
```bash
# 部署管理平台
cp sys-manager/target/sys-manager.jar /path/to/deployment/

# 部署支付网关
cp sys-payment/target/sys-payment.jar /path/to/deployment/

# 部署商户平台
cp sys-merchant/target/sys-merchant.jar /path/to/deployment/

# 部署代理商平台
cp sys-agent/target/sys-agent.jar /path/to/deployment/
```

### 第三步：服务启动

按以下顺序启动服务：

1. 启动管理平台：
```bash
java -jar sys-manager.jar --spring.profiles.active=prod
```

2. 启动支付网关：
```bash
java -jar sys-payment.jar --spring.profiles.active=prod
```

3. 启动商户平台：
```bash
java -jar sys-merchant.jar --spring.profiles.active=prod
```

4. 启动代理商平台：
```bash
java -jar sys-agent.jar --spring.profiles.active=prod
```

### 第四步：功能验证

1. 验证统一应用管理：
```bash
curl -X GET "http://localhost:9217/api/unifiedPayApps" \
  -H "iToken: your_token_here"
```

2. 验证商户创建：
```bash
curl -X POST "http://localhost:9217/api/mchInfos" \
  -H "Content-Type: application/json" \
  -H "iToken: your_token_here" \
  -d '{
    "mchName": "测试商户",
    "mchShortName": "测试商户",
    "contactName": "张三",
    "contactTel": "13800138000",
    "loginUserName": "testmch001"
  }'
```

3. 验证支付流程：
```bash
curl -X POST "http://localhost:9216/api/pay/unifiedOrder" \
  -H "Content-Type: application/json" \
  -d '{
    "mchNo": "M1727234567",
    "appId": "DEFAULT_UNIFIED_APP_001",
    "mchOrderNo": "TEST_ORDER_001",
    "wayCode": "ALI_QR",
    "amount": 10000,
    "currency": "cny",
    "subject": "测试商品",
    "body": "测试商品描述"
  }'
```

## 配置更新

### 1. 权限配置
在管理平台添加以下权限：
- ENT_UNIFIED_PAY_APP - 统一支付应用管理
- ENT_UNIFIED_PAY_APP_LIST - 统一支付应用列表
- ENT_UNIFIED_PAY_APP_VIEW - 统一支付应用详情
- ENT_UNIFIED_PAY_APP_ADD - 新建统一支付应用
- ENT_UNIFIED_PAY_APP_EDIT - 修改统一支付应用
- ENT_UNIFIED_PAY_APP_DEL - 删除统一支付应用
- ENT_MCH_INCOME_LIST - 商户收益列表
- ENT_MCH_INCOME_VIEW - 商户收益详情
- ENT_MCH_INCOME_STATS - 商户收益统计

### 2. 菜单配置
在管理平台添加统一应用管理菜单。

## 监控和日志

### 1. 关键日志监控
监控以下日志关键字：
- "创建商户收益记录成功" - 收益记录创建成功
- "商户收益记录异常" - 收益记录创建失败
- "统一应用" - 统一应用相关操作

### 2. 数据库监控
监控以下表的数据变化：
- t_unified_pay_app - 统一应用表
- t_mch_income_record - 收益记录表
- t_mch_info - 商户信息表（新增字段）

### 3. 性能监控
监控以下接口的性能：
- /api/unifiedPayApps - 统一应用管理
- /api/mchInfos - 商户管理
- /api/pay/unifiedOrder - 统一下单
- /api/mchIncomes - 收益查询

## 回滚方案

如果部署出现问题，可以按以下步骤回滚：

### 1. 停止所有服务
```bash
pkill -f sys-manager.jar
pkill -f sys-payment.jar
pkill -f sys-merchant.jar
pkill -f sys-agent.jar
```

### 2. 恢复数据库
```bash
# 恢复备份
mysql -u username -p database_name < backup_before_unified_pay_migration.sql

# 删除新增的表和字段
mysql -u username -p database_name << EOF
ALTER TABLE t_mch_info DROP COLUMN unified_app_id;
ALTER TABLE t_mch_info DROP COLUMN fee_rate;
DROP TABLE IF EXISTS t_unified_pay_app;
DROP TABLE IF EXISTS t_mch_income_record;
DROP VIEW IF EXISTS v_mch_income_stats;
EOF
```

### 3. 部署旧版本代码
恢复到之前的代码版本并重新部署。

## 注意事项

1. **数据一致性**：确保在迁移过程中数据的一致性，特别是商户与统一应用的绑定关系。

2. **缓存清理**：部署后清理相关缓存，确保新的配置生效。

3. **权限配置**：确保相关用户具有访问新功能的权限。

4. **监控告警**：设置相关监控告警，及时发现问题。

5. **文档更新**：更新相关的API文档和用户手册。

## 联系方式

如果在部署过程中遇到问题，请联系：
- 开发团队：[开发团队联系方式]
- 运维团队：[运维团队联系方式]
- 项目经理：[项目经理联系方式]
