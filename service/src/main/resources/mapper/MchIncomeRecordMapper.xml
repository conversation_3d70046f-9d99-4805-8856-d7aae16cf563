<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.unipay.service.mapper.MchIncomeRecordMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.unipay.core.entity.MchIncomeRecord">
        <id column="id" property="id" />
        <result column="mch_no" property="mchNo" />
        <result column="pay_order_id" property="payOrderId" />
        <result column="order_amount" property="orderAmount" />
        <result column="fee_amount" property="feeAmount" />
        <result column="fee_rate" property="feeRate" />
        <result column="income_amount" property="incomeAmount" />
        <result column="income_date" property="incomeDate" />
        <result column="created_at" property="createdAt" />
        <result column="updated_at" property="updatedAt" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, mch_no, pay_order_id, order_amount, fee_amount, fee_rate, income_amount, income_date, created_at, updated_at
    </sql>

    <!-- 统计商户收益金额 -->
    <select id="sumIncomeAmount" resultType="java.util.Map">
        SELECT 
            COUNT(*) as totalCount,
            COALESCE(SUM(order_amount), 0) as totalOrderAmount,
            COALESCE(SUM(fee_amount), 0) as totalFeeAmount,
            COALESCE(SUM(income_amount), 0) as totalIncomeAmount,
            COALESCE(AVG(fee_rate), 0) as avgFeeRate
        FROM t_mch_income_record 
        WHERE mch_no = #{mchNo}
        <if test="startDate != null">
            AND income_date >= #{startDate}
        </if>
        <if test="endDate != null">
            AND income_date &lt;= #{endDate}
        </if>
    </select>

</mapper>
