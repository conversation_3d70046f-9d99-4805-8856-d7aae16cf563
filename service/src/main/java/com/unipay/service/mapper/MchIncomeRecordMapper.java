package com.unipay.service.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.unipay.core.entity.MchIncomeRecord;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.Map;

/**
 * <p>
 * 商户收益记录表 Mapper 接口
 * </p>
 *
 * <AUTHOR> plus generator]
 * @since 2025-09-25
 */
@Mapper
public interface MchIncomeRecordMapper extends BaseMapper<MchIncomeRecord> {

    /**
     * 统计商户收益金额
     * @param mchNo 商户号
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 统计结果
     */
    Map<String, Object> sumIncomeAmount(@Param("mchNo") String mchNo,
                                       @Param("startDate") Date startDate,
                                       @Param("endDate") Date endDate);

}
