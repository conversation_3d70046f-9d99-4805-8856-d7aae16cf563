package com.unipay.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.unipay.core.entity.MchIncomeRecord;
import com.unipay.service.mapper.MchIncomeRecordMapper;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.Date;
import java.util.Map;

/**
 * <p>
 * 商户收益记录表 服务实现类
 * </p>
 *
 * <AUTHOR> plus generator]
 * @since 2025-09-25
 */
@Service
public class MchIncomeRecordService extends ServiceImpl<MchIncomeRecordMapper, MchIncomeRecord> {

    /**
     * 创建商户收益记录
     * @param mchNo 商户号
     * @param payOrderId 支付订单号
     * @param orderAmount 订单金额
     * @param feeAmount 手续费金额
     * @param feeRate 手续费率
     * @return 是否成功
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean createIncomeRecord(String mchNo, String payOrderId, Long orderAmount, 
                                     Long feeAmount, BigDecimal feeRate) {
        // 计算商户实际收益
        Long incomeAmount = orderAmount - feeAmount;
        
        MchIncomeRecord record = new MchIncomeRecord();
        record.setMchNo(mchNo);
        record.setPayOrderId(payOrderId);
        record.setOrderAmount(orderAmount);
        record.setFeeAmount(feeAmount);
        record.setFeeRate(feeRate);
        record.setIncomeAmount(incomeAmount);
        record.setIncomeDate(new Date());
        
        return save(record);
    }

    /**
     * 统计商户收益金额
     * @param mchNo 商户号
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 统计数据
     */
    public Map<String, Object> sumIncomeAmount(String mchNo, Date startDate, Date endDate) {
        return baseMapper.sumIncomeAmount(mchNo, startDate, endDate);
    }

    /**
     * 分页查询商户收益记录
     * @param iPage 分页参数
     * @param mchIncomeRecord 查询条件
     * @return 分页结果
     */
    public IPage<MchIncomeRecord> selectPage(IPage iPage, MchIncomeRecord mchIncomeRecord) {
        LambdaQueryWrapper<MchIncomeRecord> wrapper = MchIncomeRecord.gw();
        
        if (StringUtils.isNotBlank(mchIncomeRecord.getMchNo())) {
            wrapper.eq(MchIncomeRecord::getMchNo, mchIncomeRecord.getMchNo());
        }
        if (StringUtils.isNotEmpty(mchIncomeRecord.getPayOrderId())) {
            wrapper.eq(MchIncomeRecord::getPayOrderId, mchIncomeRecord.getPayOrderId());
        }
        
        wrapper.orderByDesc(MchIncomeRecord::getCreatedAt);

        return this.page(iPage, wrapper);
    }

    /**
     * 获取商户今日收益统计
     * @param mchNo 商户号
     * @return 统计数据
     */
    public Map<String, Object> getTodayIncomeStats(String mchNo) {
        Date today = new Date();
        return sumIncomeAmount(mchNo, today, today);
    }

    /**
     * 获取商户本月收益统计
     * @param mchNo 商户号
     * @return 统计数据
     */
    public Map<String, Object> getMonthIncomeStats(String mchNo) {
        // 获取本月第一天和最后一天
        java.util.Calendar cal = java.util.Calendar.getInstance();
        cal.set(java.util.Calendar.DAY_OF_MONTH, 1);
        Date monthStart = cal.getTime();
        
        cal.set(java.util.Calendar.DAY_OF_MONTH, cal.getActualMaximum(java.util.Calendar.DAY_OF_MONTH));
        Date monthEnd = cal.getTime();
        
        return sumIncomeAmount(mchNo, monthStart, monthEnd);
    }

    /**
     * 检查支付订单是否已生成收益记录
     * @param payOrderId 支付订单号
     * @return 是否已存在
     */
    public boolean existsIncomeRecord(String payOrderId) {
        return count(MchIncomeRecord.gw()
                .eq(MchIncomeRecord::getPayOrderId, payOrderId)) > 0;
    }
}
