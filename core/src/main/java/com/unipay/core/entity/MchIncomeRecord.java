package com.unipay.core.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.unipay.core.model.BaseModel;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 商户收益记录表
 * </p>
 *
 * <AUTHOR> plus generator]
 * @since 2025-09-25
 */
@Schema(description = "商户收益记录表")
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("t_mch_income_record")
public class MchIncomeRecord extends BaseModel implements Serializable {

    private static final long serialVersionUID = 1L;

    //gw
    public static final LambdaQueryWrapper<MchIncomeRecord> gw() {
        return new LambdaQueryWrapper<>();
    }

    /**
     * 记录ID
     */
    @Schema(title = "id", description = "记录ID")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 商户号
     */
    @Schema(title = "mchNo", description = "商户号")
    private String mchNo;

    /**
     * 支付订单号
     */
    @Schema(title = "payOrderId", description = "支付订单号")
    private String payOrderId;

    /**
     * 订单金额（单位：分）
     */
    @Schema(title = "orderAmount", description = "订单金额（单位：分）")
    private Long orderAmount;

    /**
     * 手续费金额（单位：分）
     */
    @Schema(title = "feeAmount", description = "手续费金额（单位：分）")
    private Long feeAmount;

    /**
     * 手续费率（百分比）
     */
    @Schema(title = "feeRate", description = "手续费率（百分比）")
    private BigDecimal feeRate;

    /**
     * 商户实际收益（单位：分）
     */
    @Schema(title = "incomeAmount", description = "商户实际收益（单位：分）")
    private Long incomeAmount;

    /**
     * 收益日期
     */
    @Schema(title = "incomeDate", description = "收益日期")
    private Date incomeDate;

    /**
     * 创建时间
     */
    @Schema(title = "createdAt", description = "创建时间")
    private Date createdAt;

    /**
     * 更新时间
     */
    @Schema(title = "updatedAt", description = "更新时间")
    private Date updatedAt;

}
